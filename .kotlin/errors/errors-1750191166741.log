kotlin version: 2.0.0
error message: org.jetbrains.kotlin.backend.common.CompilationException: Back-end: Please report this problem https://kotl.in/issue
/Users/<USER>/code/MyWorks/KBuilderTDD/src/main/kotlin/com/aicodingcli/plugins/Plugin.kt:32:1
Problem with `@Serializable
data class PluginMetadata {
  constructor(id: String, name: String, version: String, description: String, author: String, mainClass: String, dependencies: List<PluginDependency> = emptyList<PluginDependency>(), permissions: List<PluginPermission> = emptyList<PluginPermission>(), minCliVersion: String? = null, website: String? = null) /* primary */ {
    super/*Any*/()
    /* <init>() */

  }

  val id: String
    field = id
    get

  val name: String
    field = name
    get

  val version: String
    field = version
    get

  val description: String
    field = description
    get

  val author: String
    field = author
    get

  val mainClass: String
    field = mainClass
    get

  val dependencies: List<PluginDependency>
    field = dependencies
    get

  @Contextual
  val permissions: List<PluginPermission>
    field = permissions
    get

  val minCliVersion: String?
    field = minCliVersion
    get

  val website: String?
    field = website
    get

  companion object Companion {
    private constructor() /* primary */ {
      super/*Any*/()
      /* <init>() */

    }

    fun serializer(): KSerializer<PluginMetadata>

    /* fake */ override operator fun equals(other: Any?): Boolean

    /* fake */ override fun hashCode(): Int

    /* fake */ override fun toString(): String

  }

  @Deprecated(message = "This synthesized declaration should not be used directly", level = DeprecationLevel.HIDDEN)
  object $serializer : GeneratedSerializer<PluginMetadata> {
    private constructor() /* primary */ {
      super/*Any*/()
      /* <init>() */

    }

    override fun serialize(encoder: Encoder, value: PluginMetadata)

    override fun deserialize(decoder: Decoder): PluginMetadata

    override val descriptor: SerialDescriptor
      override get

    override fun childSerializers(): Array<KSerializer<*>>

    /* fake */ override fun typeParametersSerializers(): Array<KSerializer<*>>

    /* fake */ override operator fun equals(other: Any?): Boolean

    /* fake */ override fun hashCode(): Int

    /* fake */ override fun toString(): String

  }

  operator fun component1(): String {
    return <this>.#id
  }

  operator fun component2(): String {
    return <this>.#name
  }

  operator fun component3(): String {
    return <this>.#version
  }

  operator fun component4(): String {
    return <this>.#description
  }

  operator fun component5(): String {
    return <this>.#author
  }

  operator fun component6(): String {
    return <this>.#mainClass
  }

  operator fun component7(): List<PluginDependency> {
    return <this>.#dependencies
  }

  operator fun component8(): List<PluginPermission> {
    return <this>.#permissions
  }

  operator fun component9(): String? {
    return <this>.#minCliVersion
  }

  operator fun component10(): String? {
    return <this>.#website
  }

  fun copy(id: String = <this>.#id, name: String = <this>.#name, version: String = <this>.#version, description: String = <this>.#description, author: String = <this>.#author, mainClass: String = <this>.#mainClass, dependencies: List<PluginDependency> = <this>.#dependencies, permissions: List<PluginPermission> = <this>.#permissions, minCliVersion: String? = <this>.#minCliVersion, website: String? = <this>.#website): PluginMetadata {
    return PluginMetadata(id = id, name = name, version = version, description = description, author = author, mainClass = mainClass, dependencies = dependencies, permissions = permissions, minCliVersion = minCliVersion, website = website)
  }

  override fun toString(): String {
    return "PluginMetadata(" + "id=" + <this>.#id + ", " + "name=" + <this>.#name + ", " + "version=" + <this>.#version + ", " + "description=" + <this>.#description + ", " + "author=" + <this>.#author + ", " + "mainClass=" + <this>.#mainClass + ", " + "dependencies=" + <this>.#dependencies + ", " + "permissions=" + <this>.#permissions + ", " + "minCliVersion=" + <this>.#minCliVersion + ", " + "website=" + <this>.#website + ")"
  }

  override fun hashCode(): Int {
    var result: Int = <this>.#id.hashCode()
    result = result.times(other = 31).plus(other = <this>.#name.hashCode())
    result = result.times(other = 31).plus(other = <this>.#version.hashCode())
    result = result.times(other = 31).plus(other = <this>.#description.hashCode())
    result = result.times(other = 31).plus(other = <this>.#author.hashCode())
    result = result.times(other = 31).plus(other = <this>.#mainClass.hashCode())
    result = result.times(other = 31).plus(other = <this>.#dependencies.hashCode())
    result = result.times(other = 31).plus(other = <this>.#permissions.hashCode())
    result = result.times(other = 31).plus(other = when {
      EQEQ(arg0 = <this>.#minCliVersion, arg1 = null) -> 0
      else -> <this>.#minCliVersion.hashCode()
    })
    result = result.times(other = 31).plus(other = when {
      EQEQ(arg0 = <this>.#website, arg1 = null) -> 0
      else -> <this>.#website.hashCode()
    })
    return result
  }

  override operator fun equals(other: Any?): Boolean {
    when {
      EQEQEQ(arg0 = <this>, arg1 = other) -> return true
    }
    when {
      other !is PluginMetadata -> return false
    }
    val tmp0_other_with_cast: PluginMetadata = other as PluginMetadata
    when {
      EQEQ(arg0 = <this>.#id, arg1 = tmp0_other_with_cast.#id).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#name, arg1 = tmp0_other_with_cast.#name).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#version, arg1 = tmp0_other_with_cast.#version).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#description, arg1 = tmp0_other_with_cast.#description).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#author, arg1 = tmp0_other_with_cast.#author).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#mainClass, arg1 = tmp0_other_with_cast.#mainClass).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#dependencies, arg1 = tmp0_other_with_cast.#dependencies).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#permissions, arg1 = tmp0_other_with_cast.#permissions).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#minCliVersion, arg1 = tmp0_other_with_cast.#minCliVersion).not() -> return false
    }
    when {
      EQEQ(arg0 = <this>.#website, arg1 = tmp0_other_with_cast.#website).not() -> return false
    }
    return true
  }

  @JvmStatic
  internal fun write$Self(self: PluginMetadata, output: CompositeEncoder, serialDesc: SerialDescriptor)

  internal constructor(seen0: Int, id: String?, name: String?, version: String?, description: String?, author: String?, mainClass: String?, dependencies: List<PluginDependency>?, permissions: List<PluginPermission>?, minCliVersion: String?, website: String?, serializationConstructorMarker: SerializationConstructorMarker?)

}

`
Details: kotlinx.serialization compiler plugin internal error: unable to transform declaration, see cause
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializerClassLowering.lower(SerializationLoweringExtension.kt:228)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt$runOnFileInOrder$1.visitClass(SerializationLoweringExtension.kt:46)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitClass(IrElementVisitorVoid.kt:44)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt$runOnFileInOrder$1.visitClass(SerializationLoweringExtension.kt:40)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt$runOnFileInOrder$1.visitClass(SerializationLoweringExtension.kt:40)
	at org.jetbrains.kotlin.ir.declarations.IrClass.accept(IrClass.kt:72)
	at org.jetbrains.kotlin.ir.declarations.IrFile.acceptChildren(IrFile.kt:34)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorsKt.acceptChildrenVoid(IrVisitors.kt:15)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt$runOnFileInOrder$1.visitElement(SerializationLoweringExtension.kt:42)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitPackageFragment(IrElementVisitorVoid.kt:168)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt$runOnFileInOrder$1.visitPackageFragment(SerializationLoweringExtension.kt:40)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitFile(IrElementVisitorVoid.kt:184)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt$runOnFileInOrder$1.visitFile(SerializationLoweringExtension.kt:40)
	at org.jetbrains.kotlin.ir.visitors.IrElementVisitorVoid$DefaultImpls.visitFile(IrElementVisitorVoid.kt:180)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt$runOnFileInOrder$1.visitFile(SerializationLoweringExtension.kt:40)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt$runOnFileInOrder$1.visitFile(SerializationLoweringExtension.kt:40)
	at org.jetbrains.kotlin.ir.declarations.IrFile.accept(IrFile.kt:28)
	at org.jetbrains.kotlin.ir.visitors.IrVisitorsKt.acceptVoid(IrVisitors.kt:11)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtensionKt.runOnFileInOrder(SerializationLoweringExtension.kt:40)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializationLoweringExtension.generate(SerializationLoweringExtension.kt:193)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.applyIrGenerationExtensions(convertToIr.kt:260)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize(convertToIr.kt:139)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize$default(convertToIr.kt:65)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.convertToIrAndActualizeForJvm(jvmCompilerPipeline.kt:193)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.convertAnalyzedFirToIr(jvmCompilerPipeline.kt:167)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.compileModulesUsingFrontendIrAndLightTree(jvmCompilerPipeline.kt:138)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:155)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:50)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:104)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:48)
	at org.jetbrains.kotlin.cli.common.CLITool.exec(CLITool.kt:101)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:453)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:62)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:506)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:423)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally$lambda$9$compile(IncrementalCompilerRunner.kt:249)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally(IncrementalCompilerRunner.kt:267)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:120)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:676)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1661)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.lang.IllegalStateException: Serializer for element of type com.aicodingcli.plugins.PluginPermission has not been found
	at org.jetbrains.kotlinx.serialization.compiler.backend.ir.SerializerSearchUtilKt.findTypeSerializerOrContext(SerializerSearchUtil.kt:84)
	at org.jetbrains.kotlinx.serialization.compiler.backend.ir.BaseIrGenerator.serializerInstance(BaseIrGenerator.kt:713)
	at org.jetbrains.kotlinx.serialization.compiler.backend.ir.BaseIrGenerator.serializerInstance$instantiate(BaseIrGenerator.kt:546)
	at org.jetbrains.kotlinx.serialization.compiler.backend.ir.BaseIrGenerator.serializerInstance(BaseIrGenerator.kt:589)
	at org.jetbrains.kotlinx.serialization.compiler.backend.ir.BaseIrGenerator.cacheableChildSerializerInstance(BaseIrGenerator.kt:489)
	at org.jetbrains.kotlinx.serialization.compiler.backend.ir.BaseIrGenerator.createCachedChildSerializers(BaseIrGenerator.kt:467)
	at org.jetbrains.kotlinx.serialization.compiler.backend.ir.SerializableIrGenerator.<init>(SerializableIrGenerator.kt:59)
	at org.jetbrains.kotlinx.serialization.compiler.backend.ir.SerializableIrGenerator$Companion.generate(SerializableIrGenerator.kt:398)
	at org.jetbrains.kotlinx.serialization.compiler.extensions.SerializerClassLowering.lower(SerializationLoweringExtension.kt:145)
	... 55 more


