kotlin version: 2.0.0
error message: java.lang.IllegalStateException: Source classes should be created separately before referencing
	at org.jetbrains.kotlin.fir.backend.Fir2IrClassifierStorage.getIrClass(Fir2IrClassifierStorage.kt:213)
	at org.jetbrains.kotlin.fir.backend.Fir2IrClassifierStorage.getIrClass(Fir2IrClassifierStorage.kt:223)
	at org.jetbrains.kotlin.fir.backend.Fir2IrClassifierStorage.getIrClassSymbol(Fir2IrClassifierStorage.kt:242)
	at org.jetbrains.kotlin.fir.backend.Fir2IrDeclarationStorage.findIrParent$fir2ir(Fir2IrDeclarationStorage.kt:1465)
	at org.jetbrains.kotlin.fir.backend.Fir2IrDeclarationStorage.findIrParent$fir2ir(Fir2IrDeclarationStorage.kt:1546)
	at org.jetbrains.kotlin.fir.backend.Fir2IrDeclarationStorage.getIrConstructorSymbol(Fir2IrDeclarationStorage.kt:538)
	at org.jetbrains.kotlin.fir.backend.Fir2IrDeclarationStorage.createAndCacheIrConstructor(Fir2IrDeclarationStorage.kt:517)
	at org.jetbrains.kotlin.fir.backend.Fir2IrDeclarationStorage.createAndCacheIrConstructor$default(Fir2IrDeclarationStorage.kt:511)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processClassMembers$fir2ir(Fir2IrConverter.kt:263)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processMemberDeclaration(Fir2IrConverter.kt:525)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processClassMembers$fir2ir(Fir2IrConverter.kt:270)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processMemberDeclaration(Fir2IrConverter.kt:525)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processFileAndClassMembers(Fir2IrConverter.kt:227)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.runSourcesConversion(Fir2IrConverter.kt:95)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.access$runSourcesConversion(Fir2IrConverter.kt:65)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter$Companion.createIrModuleFragment(Fir2IrConverter.kt:767)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIr(convertToIr.kt:245)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize(convertToIr.kt:82)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize$default(convertToIr.kt:65)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.convertToIrAndActualizeForJvm(jvmCompilerPipeline.kt:193)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.convertAnalyzedFirToIr(jvmCompilerPipeline.kt:167)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.compileModulesUsingFrontendIrAndLightTree(jvmCompilerPipeline.kt:138)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:155)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:50)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:104)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:48)
	at org.jetbrains.kotlin.cli.common.CLITool.exec(CLITool.kt:101)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:453)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:62)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:506)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:423)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally$lambda$9$compile(IncrementalCompilerRunner.kt:249)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally(IncrementalCompilerRunner.kt:267)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:120)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:676)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1661)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)


