<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7b29c7dc-e0f3-482f-8897-75690eec0570" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/build.gradle.kts" beforeDir="false" afterPath="$PROJECT_DIR$/build.gradle.kts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPEN",
    "assignee": "iptton"
  }
}]]></component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "**************:iptton-ai/kbuildercli.git",
    "accountId": "9ef6f3b4-cfa6-4d1e-9387-c6585f3914fd"
  }
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="2yctzjG7JNiMIzRSlJIAttGIRTu" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Gradle.OpenAiServiceTest.should test connection successfully.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "main",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "/Users/<USER>/code/MyWorks/KBuilderTDD",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "project.propDebugger",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="OpenAiServiceTest.should test connection successfully" type="GradleRunConfiguration" factoryName="Gradle" temporary="true">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list>
            <option value=":test" />
            <option value="--tests" />
            <option value="&quot;com.aicodingcli.ai.providers.OpenAiServiceTest.should test connection successfully&quot;" />
          </list>
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>false</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>true</RunAsTest>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Gradle.OpenAiServiceTest.should test connection successfully" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="7b29c7dc-e0f3-482f-8897-75690eec0570" name="更改" comment="" />
      <created>1750145954226</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750145954226</updated>
      <workItem from="1750145955751" duration="8633000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>