Private property of Exodus: <EMAIL>

jetbrains.exodus.io.LockingManager.lock(LockingManager.kt:88)
jetbrains.exodus.io.LockingManager.lock(LockingManager.kt:39)
jetbrains.exodus.io.FileDataWriter.lock(FileDataWriter.kt:70)
jetbrains.exodus.log.Log.tryLock(Log.kt:804)
jetbrains.exodus.log.Log.<init>(Log.kt:117)
jetbrains.exodus.env.Environments.newLogInstance(Environments.kt:117)
jetbrains.exodus.env.Environments.newLogInstance(Environments.kt:81)
jetbrains.exodus.env.Environments.newLogInstance(Environments.kt:77)
jetbrains.exodus.env.Environments$newInstance$3.invoke(Environments.kt:40)
jetbrains.exodus.env.Environments$newInstance$3.invoke(Environments.kt:40)
jetbrains.exodus.env.Environments.prepare(Environments.kt:120)
jetbrains.exodus.env.Environments.newInstance(Environments.kt:40)
jetbrains.exodus.env.Environments.newInstance(Environments.kt:34)
cc.unitmesh.devti.history.ChatHistoryService.environment_delegate$lambda$0(ChatHistoryService.kt:24)
kotlin.SynchronizedLazyImpl.getValue(LazyJVM.kt:83)
cc.unitmesh.devti.history.ChatHistoryService.getEnvironment(ChatHistoryService.kt:21)
cc.unitmesh.devti.history.ChatHistoryService.entityStore_delegate$lambda$1(ChatHistoryService.kt:28)
kotlin.SynchronizedLazyImpl.getValue(LazyJVM.kt:83)
cc.unitmesh.devti.history.ChatHistoryService.getEntityStore(ChatHistoryService.kt:27)
cc.unitmesh.devti.history.ChatHistoryService.getAllSessions(ChatHistoryService.kt:62)
cc.unitmesh.devti.gui.toolbar.ViewHistoryAction.actionPerformed(ViewHistoryAction.kt:139)
com.intellij.openapi.actionSystem.impl.ActionButton.actionPerformed(ActionButton.java:239)
com.intellij.openapi.actionSystem.impl.ActionButton.lambda$performAction$2(ActionButton.java:219)
com.intellij.openapi.actionSystem.impl.ActionManagerImpl.performWithActionCallbacks(ActionManagerImpl.kt:1167)
com.intellij.openapi.actionSystem.ex.ActionUtil.performDumbAwareWithCallbacks(ActionUtil.kt:405)
com.intellij.openapi.actionSystem.impl.ActionButton.performAction(ActionButton.java:219)
com.intellij.openapi.actionSystem.impl.ActionButton.processMouseEvent(ActionButton.java:535)
java.desktop/java.awt.Component.processEvent(Component.java:6438)
java.desktop/java.awt.Container.processEvent(Container.java:2266)
java.desktop/java.awt.Component.dispatchEventImpl(Component.java:5043)
java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2324)
java.desktop/java.awt.Component.dispatchEvent(Component.java:4871)
java.desktop/java.awt.LightweightDispatcher.retargetMouseEvent(Container.java:4963)
java.desktop/java.awt.LightweightDispatcher.processMouseEvent(Container.java:4577)
java.desktop/java.awt.LightweightDispatcher.dispatchEvent(Container.java:4518)
java.desktop/java.awt.Container.dispatchEventImpl(Container.java:2310)
java.desktop/java.awt.Window.dispatchEventImpl(Window.java:2810)
java.desktop/java.awt.Component.dispatchEvent(Component.java:4871)
java.desktop/java.awt.EventQueue.dispatchEventImpl(EventQueue.java:783)
java.desktop/java.awt.EventQueue$4.run(EventQueue.java:728)
java.desktop/java.awt.EventQueue$4.run(EventQueue.java:722)
java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:98)
java.desktop/java.awt.EventQueue$5.run(EventQueue.java:755)
java.desktop/java.awt.EventQueue$5.run(EventQueue.java:753)
java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
java.base/java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87)
java.desktop/java.awt.EventQueue.dispatchEvent(EventQueue.java:752)
com.intellij.ide.IdeEventQueue.defaultDispatchEvent(IdeEventQueue.kt:585)
com.intellij.ide.IdeEventQueue.dispatchMouseEvent(IdeEventQueue.kt:530)
com.intellij.ide.IdeEventQueue._dispatchEvent$lambda$16(IdeEventQueue.kt:473)
com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction$lambda$6(AnyThreadWriteThreadingSupport.kt:274)
com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWithTemporaryThreadLocal(AnyThreadWriteThreadingSupport.kt:204)
com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runWriteIntentReadAction(AnyThreadWriteThreadingSupport.kt:274)
com.intellij.openapi.application.impl.AnyThreadWriteThreadingSupport.runPreventiveWriteIntentReadAction(AnyThreadWriteThreadingSupport.kt:218)
com.intellij.ide.IdeEventQueue._dispatchEvent(IdeEventQueue.kt:473)
com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10$lambda$9(IdeEventQueue.kt:307)
com.intellij.openapi.progress.impl.CoreProgressManager.computePrioritized(CoreProgressManager.java:864)
com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12$lambda$11$lambda$10(IdeEventQueue.kt:306)
com.intellij.ide.IdeEventQueueKt.performActivity$lambda$3(IdeEventQueue.kt:958)
com.intellij.openapi.application.TransactionGuardImpl.performActivity(TransactionGuardImpl.java:117)
com.intellij.ide.IdeEventQueueKt.performActivity(IdeEventQueue.kt:958)
com.intellij.ide.IdeEventQueue.dispatchEvent$lambda$12(IdeEventQueue.kt:301)
com.intellij.ide.IdeEventQueue.dispatchEvent(IdeEventQueue.kt:341)
java.desktop/java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:207)
java.desktop/java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:128)
java.desktop/java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:117)
java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:113)
java.desktop/java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:105)
java.desktop/java.awt.EventDispatchThread.run(EventDispatchThread.java:92)
