*com.aicodingcli.conversation.MockAiService2com.aicodingcli.conversation.ScenarioMockAiService)com.aicodingcli.conversation.TestScenario/com.aicodingcli.integration.SimpleMockAiService+com.aicodingcli.plugins.TestAiServicePlugin%com.aicodingcli.plugins.TestAiService)com.aicodingcli.plugins.TestCommandPlugin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            