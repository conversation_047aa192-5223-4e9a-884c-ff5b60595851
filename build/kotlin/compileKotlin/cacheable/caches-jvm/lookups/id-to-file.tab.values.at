/ Header Record For PersistentHashMapValueStorage( 'src/main/kotlin/com/aicodingcli/Main.kt/ .src/main/kotlin/com/aicodingcli/ai/AiModels.kt0 /src/main/kotlin/com/aicodingcli/ai/AiService.kt= <src/main/kotlin/com/aicodingcli/ai/providers/ClaudeModels.kt> =src/main/kotlin/com/aicodingcli/ai/providers/ClaudeService.kt= <src/main/kotlin/com/aicodingcli/ai/providers/OllamaModels.kt> =src/main/kotlin/com/aicodingcli/ai/providers/OllamaService.kt= <src/main/kotlin/com/aicodingcli/ai/providers/OpenAiModels.kt> =src/main/kotlin/com/aicodingcli/ai/providers/OpenAiService.ktD Csrc/main/kotlin/com/aicodingcli/code/analysis/CodeAnalysisModels.kt> =src/main/kotlin/com/aicodingcli/code/analysis/CodeAnalyzer.ktC Bsrc/main/kotlin/com/aicodingcli/code/common/ProgrammingLanguage.ktB Asrc/main/kotlin/com/aicodingcli/code/metrics/MetricsCalculator.kt@ ?src/main/kotlin/com/aicodingcli/code/quality/QualityAnalyzer.kt4 3src/main/kotlin/com/aicodingcli/config/AppConfig.kt8 7src/main/kotlin/com/aicodingcli/config/ConfigManager.ktL Ksrc/main/kotlin/com/aicodingcli/conversation/AiDrivenAutoExecutionEngine.kt? >src/main/kotlin/com/aicodingcli/conversation/AiPromptEngine.kt? >src/main/kotlin/com/aicodingcli/conversation/AiPromptModels.ktD Csrc/main/kotlin/com/aicodingcli/conversation/AutoExecutionEngine.ktC Bsrc/main/kotlin/com/aicodingcli/conversation/ConversationModels.ktH Gsrc/main/kotlin/com/aicodingcli/conversation/ConversationSerializers.ktI Hsrc/main/kotlin/com/aicodingcli/conversation/ConversationStateManager.ktB Asrc/main/kotlin/com/aicodingcli/conversation/RequirementParser.kt? >src/main/kotlin/com/aicodingcli/conversation/TaskDecomposer.kt= <src/main/kotlin/com/aicodingcli/conversation/ToolExecutor.ktK Jsrc/main/kotlin/com/aicodingcli/conversation/tools/AnalysisToolHandlers.ktO Nsrc/main/kotlin/com/aicodingcli/conversation/tools/ExecutionMetadataManager.ktG Fsrc/main/kotlin/com/aicodingcli/conversation/tools/FileToolHandlers.ktF Esrc/main/kotlin/com/aicodingcli/conversation/tools/GitToolHandlers.ktJ Isrc/main/kotlin/com/aicodingcli/conversation/tools/NetworkToolHandlers.ktJ Isrc/main/kotlin/com/aicodingcli/conversation/tools/ProcessToolHandlers.ktB Asrc/main/kotlin/com/aicodingcli/conversation/tools/ToolHandler.kt: 9src/main/kotlin/com/aicodingcli/history/HistoryManager.kt9 8src/main/kotlin/com/aicodingcli/history/HistoryModels.kt5 4src/main/kotlin/com/aicodingcli/http/AiHttpClient.kt3 2src/main/kotlin/com/aicodingcli/http/HttpModels.kt; :src/main/kotlin/com/aicodingcli/plugins/AiServicePlugin.kt9 8src/main/kotlin/com/aicodingcli/plugins/CommandPlugin.kt2 1src/main/kotlin/com/aicodingcli/plugins/Plugin.kt9 8src/main/kotlin/com/aicodingcli/plugins/PluginCommand.kt9 8src/main/kotlin/com/aicodingcli/plugins/PluginContext.kt9 8src/main/kotlin/com/aicodingcli/plugins/PluginManager.kt: 9src/main/kotlin/com/aicodingcli/plugins/PluginRegistry.kt: 9src/main/kotlin/com/aicodingcli/plugins/PluginSecurity.ktC Bsrc/main/kotlin/com/aicodingcli/plugins/PluginTemplateGenerator.kt? >src/main/kotlin/com/aicodingcli/plugins/PluginTestFramework.kt