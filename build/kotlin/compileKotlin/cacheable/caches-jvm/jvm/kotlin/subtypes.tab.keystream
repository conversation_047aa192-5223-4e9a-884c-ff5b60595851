kotlin.Enum2kotlinx.serialization.internal.GeneratedSerializercom.aicodingcli.ai.AiService com.aicodingcli.ai.BaseAiServicejava.lang.Exception*com.aicodingcli.code.analysis.CodeAnalyzer0com.aicodingcli.conversation.AutoExecutionEngine+com.aicodingcli.conversation.AiPromptEngine+com.aicodingcli.conversation.AnalysisResult+com.aicodingcli.conversation.ActionDecision1com.aicodingcli.conversation.CompletionEvaluation!kotlinx.serialization.KSerializer.com.aicodingcli.conversation.RequirementParser+com.aicodingcli.conversation.TaskDecomposer)com.aicodingcli.conversation.ToolExecutor2com.aicodingcli.conversation.tools.BaseToolHandler.com.aicodingcli.conversation.tools.ToolHandlercom.aicodingcli.plugins.Plugin'com.aicodingcli.plugins.AiServicePlugin%com.aicodingcli.plugins.CommandPlugin)com.aicodingcli.plugins.BaseCommandPlugin(com.aicodingcli.plugins.PluginPermission%com.aicodingcli.plugins.PluginContext$com.aicodingcli.plugins.PluginLoggerjava.lang.SecurityException"com.aicodingcli.plugins.PluginSpec                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          