<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.aicodingcli.ai.providers</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.aicodingcli.ai.providers</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.aicodingcli.ai.providers</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">22</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.202s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.ai.providers.ClaudeServiceTest.html">ClaudeServiceTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.145s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.ai.providers.OllamaServiceTest.html">OllamaServiceTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.025s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.ai.providers.OpenAiServiceTest.html">OpenAiServiceTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.032s</td>
<td class="success">100%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 下午12:43:03</p>
</div>
</div>
</body>
</html>
