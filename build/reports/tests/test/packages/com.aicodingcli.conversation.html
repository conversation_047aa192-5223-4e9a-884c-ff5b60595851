<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.aicodingcli.conversation</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.aicodingcli.conversation</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.aicodingcli.conversation</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">93</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">5</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.221s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">94%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Failed tests</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html#should extract file operations from requirement()">should extract file operations from requirement()</a>
</li>
<li>
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html#should extract intent from simple requirement()">should extract intent from simple requirement()</a>
</li>
<li>
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html#should extract parameters for API creation()">should extract parameters for API creation()</a>
</li>
<li>
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html#should parse REST API requirement()">should parse REST API requirement()</a>
</li>
<li>
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html#should parse simple class creation requirement()">should parse simple class creation requirement()</a>
</li>
</ul>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html">AiDrivenAutoExecutionEngineTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.096s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.conversation.AiPromptEngineTest.html">AiPromptEngineTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.011s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.conversation.AutoExecutionEngineTest.html">AutoExecutionEngineTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.038s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.conversation.ConversationStateManagerTest.html">ConversationStateManagerTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.012s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="../classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>
</td>
<td>17</td>
<td>5</td>
<td>0</td>
<td>0.019s</td>
<td class="failures">70%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.conversation.TaskDecomposerTest.html">TaskDecomposerTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.010s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="../classes/com.aicodingcli.conversation.ToolExecutorTest.html">ToolExecutorTest</a>
</td>
<td>22</td>
<td>0</td>
<td>0</td>
<td>0.035s</td>
<td class="success">100%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 下午12:43:03</p>
</div>
</div>
</body>
</html>
