<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - ClaudeServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>ClaudeServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.ai.providers.html">com.aicodingcli.ai.providers</a> &gt; ClaudeServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">7</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.145s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">should fail connection test on error()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle API error response()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle streaming chat request()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should make successful chat request()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should test connection successfully()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should use custom base URL if provided()</td>
<td class="success">0.121s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should validate request before sending()</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>12:43:02.576 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#3
12:43:02.680 [Test worker @kotlinx.coroutines.test runner#74] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #3#4
12:43:02.685 [Test worker @kotlinx.coroutines.test runner#74] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;id&quot;: &quot;msg_test&quot;,
    &quot;type&quot;: &quot;message&quot;,
    &quot;role&quot;: &quot;assistant&quot;,
    &quot;content&quot;: [
        {
            &quot;type&quot;: &quot;text&quot;,
            &quot;text&quot;: &quot;Hi&quot;
        }
    ],
    &quot;model&quot;: &quot;claude-3-sonnet-20240229&quot;,
    &quot;stop_reason&quot;: &quot;end_turn&quot;,
    &quot;stop_sequence&quot;: null,
    &quot;usage&quot;: {
        &quot;input_tokens&quot;: 1,
        &quot;output_tokens&quot;: 1
    }
}, headers={}) on AiHttpClient(#3).post(https://custom.anthropic.com/v1/messages, {&quot;model&quot;:&quot;claude-3-sonnet-20240229&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;max_tokens&quot;:1000,&quot;temperature&quot;:0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
12:43:02.699 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#5
12:43:02.701 [Test worker @kotlinx.coroutines.test runner#80] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #5#6
12:43:02.702 [Test worker @kotlinx.coroutines.test runner#80] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;id&quot;: &quot;msg_test&quot;,
    &quot;type&quot;: &quot;message&quot;,
    &quot;role&quot;: &quot;assistant&quot;,
    &quot;content&quot;: [
        {
            &quot;type&quot;: &quot;text&quot;,
            &quot;text&quot;: &quot;Connection test successful&quot;
        }
    ],
    &quot;model&quot;: &quot;claude-3-sonnet-20240229&quot;,
    &quot;stop_reason&quot;: &quot;end_turn&quot;,
    &quot;stop_sequence&quot;: null,
    &quot;usage&quot;: {
        &quot;input_tokens&quot;: 5,
        &quot;output_tokens&quot;: 5
    }
}, headers={Content-Type=application/json}) on AiHttpClient(#5).post(https://api.anthropic.com/v1/messages, {&quot;model&quot;:&quot;claude-3-sonnet-20240229&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Test connection&quot;}],&quot;max_tokens&quot;:10}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
12:43:02.703 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#7
12:43:02.705 [Test worker @kotlinx.coroutines.test runner#84] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #7#8
12:43:02.708 [Test worker @kotlinx.coroutines.test runner#84] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: Unauthorized on AiHttpClient(#7).post(https://api.anthropic.com/v1/messages, {&quot;model&quot;:&quot;claude-3-sonnet-20240229&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Test connection&quot;}],&quot;max_tokens&quot;:10}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
12:43:02.709 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#9
12:43:02.711 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#10
12:43:02.713 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#11
12:43:02.715 [Test worker @kotlinx.coroutines.test runner#92] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #11#12
12:43:02.715 [Test worker @kotlinx.coroutines.test runner#92] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    &quot;id&quot;: &quot;msg_01XFDUDYJgAACzvnptvVoYEL&quot;,
    &quot;type&quot;: &quot;message&quot;,
    &quot;role&quot;: &quot;assistant&quot;,
    &quot;content&quot;: [
        {
            &quot;type&quot;: &quot;text&quot;,
            &quot;text&quot;: &quot;Hello! How can I help you today?&quot;
        }
    ],
    &quot;model&quot;: &quot;claude-3-sonnet-20240229&quot;,
    &quot;stop_reason&quot;: &quot;end_turn&quot;,
    &quot;stop_sequence&quot;: null,
    &quot;usage&quot;: {
        &quot;input_tokens&quot;: 10,
        &quot;output_tokens&quot;: 15
    }
}, headers={Content-Type=application/json}) on AiHttpClient(#11).post(https://api.anthropic.com/v1/messages, {&quot;model&quot;:&quot;claude-3-sonnet-20240229&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello, Claude!&quot;}],&quot;max_tokens&quot;:1000,&quot;temperature&quot;:0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
12:43:02.719 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#13
12:43:02.721 [Test worker @kotlinx.coroutines.test runner#98] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #13#14
12:43:02.722 [Test worker @kotlinx.coroutines.test runner#98] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: Unauthorized on AiHttpClient(#13).post(https://api.anthropic.com/v1/messages, {&quot;model&quot;:&quot;claude-3-sonnet-20240229&quot;,&quot;messages&quot;:[{&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:&quot;Hello&quot;}],&quot;max_tokens&quot;:1000,&quot;temperature&quot;:0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 下午12:43:03</p>
</div>
</div>
</body>
</html>
