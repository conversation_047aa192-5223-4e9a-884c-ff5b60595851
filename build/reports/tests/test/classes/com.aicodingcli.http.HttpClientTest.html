<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - HttpClientTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>HttpClientTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.aicodingcli.http.html">com.aicodingcli.http</a> &gt; HttpClientTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">8</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.453s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">should add custom headers()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should fail after max retries exceeded()</td>
<td class="success">0.012s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle HTTP error responses()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle rate limiting with retry after()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle timeout()</td>
<td class="success">0.419s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should make successful GET request()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should make successful POST request with JSON body()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should retry on network errors()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>12:43:03.298 [Test worker @kotlinx.coroutines.test runner#372] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.301 [Test worker @kotlinx.coroutines.test runner#372] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
12:43:03.303 [Test worker @kotlinx.coroutines.test runner#372] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.303 [Test worker @kotlinx.coroutines.test runner#372] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
12:43:03.304 [Test worker @kotlinx.coroutines.test runner#372] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.305 [Test worker @kotlinx.coroutines.test runner#372] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
12:43:03.308 [Test worker @kotlinx.coroutines.test runner#380] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/create
METHOD: HttpMethod(value=POST)
12:43:03.310 [Test worker @kotlinx.coroutines.test runner#380] INFO io.ktor.client.HttpClient -- RESPONSE: 201 Created
METHOD: HttpMethod(value=POST)
FROM: https://api.example.com/create
12:43:03.313 [Test worker @kotlinx.coroutines.test runner#384] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.313 [Test worker @kotlinx.coroutines.test runner#384] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
12:43:03.315 [Test worker @kotlinx.coroutines.test runner#388] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.315 [Test worker @kotlinx.coroutines.test runner#388] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
12:43:03.317 [Test worker @kotlinx.coroutines.test runner#392] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
12:43:03.423 [Test worker @kotlinx.coroutines.test runner#392] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
12:43:03.424 [Test worker @kotlinx.coroutines.test runner#392] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
12:43:03.526 [Test worker @kotlinx.coroutines.test runner#392] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
12:43:03.526 [Test worker @kotlinx.coroutines.test runner#392] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
12:43:03.628 [Test worker @kotlinx.coroutines.test runner#392] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
12:43:03.629 [Test worker @kotlinx.coroutines.test runner#392] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
12:43:03.734 [Test worker @kotlinx.coroutines.test runner#392] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
12:43:03.737 [Test worker @kotlinx.coroutines.test runner#402] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.738 [Test worker @kotlinx.coroutines.test runner#402] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
12:43:03.738 [Test worker @kotlinx.coroutines.test runner#402] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.739 [Test worker @kotlinx.coroutines.test runner#402] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
12:43:03.739 [Test worker @kotlinx.coroutines.test runner#402] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.740 [Test worker @kotlinx.coroutines.test runner#402] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
12:43:03.742 [Test worker @kotlinx.coroutines.test runner#410] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/notfound
METHOD: HttpMethod(value=GET)
12:43:03.742 [Test worker @kotlinx.coroutines.test runner#410] INFO io.ktor.client.HttpClient -- RESPONSE: 404 Not Found
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/notfound
12:43:03.745 [Test worker @kotlinx.coroutines.test runner#414] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.745 [Test worker @kotlinx.coroutines.test runner#414] INFO io.ktor.client.HttpClient -- RESPONSE: 429 Too Many Requests
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
12:43:03.746 [Test worker @kotlinx.coroutines.test runner#414] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
12:43:03.746 [Test worker @kotlinx.coroutines.test runner#414] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 下午12:43:03</p>
</div>
</div>
</body>
</html>
