<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">306</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">5</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">3.259s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">98%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Failed tests</a>
</li>
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html#should extract file operations from requirement()">should extract file operations from requirement()</a>
</li>
<li>
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html#should extract intent from simple requirement()">should extract intent from simple requirement()</a>
</li>
<li>
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html#should extract parameters for API creation()">should extract parameters for API creation()</a>
</li>
<li>
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html#should parse REST API requirement()">should parse REST API requirement()</a>
</li>
<li>
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html">RequirementParserTest</a>.
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html#should parse simple class creation requirement()">should parse simple class creation requirement()</a>
</li>
</ul>
</div>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.html">com.aicodingcli</a>
</td>
<td>32</td>
<td>0</td>
<td>0</td>
<td>1.566s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.ai.html">com.aicodingcli.ai</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.500s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.ai.providers.html">com.aicodingcli.ai.providers</a>
</td>
<td>22</td>
<td>0</td>
<td>0</td>
<td>0.202s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.html">com.aicodingcli.code</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.analysis.html">com.aicodingcli.code.analysis</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.009s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.common.html">com.aicodingcli.code.common</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.metrics.html">com.aicodingcli.code.metrics</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.code.quality.html">com.aicodingcli.code.quality</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.config.html">com.aicodingcli.config</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.aicodingcli.conversation.html">com.aicodingcli.conversation</a>
</td>
<td>93</td>
<td>5</td>
<td>0</td>
<td>0.221s</td>
<td class="failures">94%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.history.html">com.aicodingcli.history</a>
</td>
<td>34</td>
<td>0</td>
<td>0</td>
<td>0.222s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.http.html">com.aicodingcli.http</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.453s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.integration.html">com.aicodingcli.integration</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.018s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.aicodingcli.plugins.html">com.aicodingcli.plugins</a>
</td>
<td>59</td>
<td>0</td>
<td>0</td>
<td>0.045s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.AiCodingCliTest.html">com.aicodingcli.AiCodingCliTest</a>
</td>
<td>26</td>
<td>0</td>
<td>0</td>
<td>1.537s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.AnalyzeCommandTest.html">com.aicodingcli.AnalyzeCommandTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.029s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.AiRequestResponseTest.html">com.aicodingcli.ai.AiRequestResponseTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.495s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.AiServiceTest.html">com.aicodingcli.ai.AiServiceTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.005s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.providers.ClaudeServiceTest.html">com.aicodingcli.ai.providers.ClaudeServiceTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.145s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.providers.OllamaServiceTest.html">com.aicodingcli.ai.providers.OllamaServiceTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.025s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.ai.providers.OpenAiServiceTest.html">com.aicodingcli.ai.providers.OpenAiServiceTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.032s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.DebugTest.html">com.aicodingcli.code.DebugTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.ProjectAnalysisDebugTest.html">com.aicodingcli.code.ProjectAnalysisDebugTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.003s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.analysis.CodeAnalysisModelsTest.html">com.aicodingcli.code.analysis.CodeAnalysisModelsTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.analysis.CodeAnalyzerTest.html">com.aicodingcli.code.analysis.CodeAnalyzerTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.008s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.common.ProgrammingLanguageTest.html">com.aicodingcli.code.common.ProgrammingLanguageTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest.html">com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.003s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.metrics.MetricsDebugTest.html">com.aicodingcli.code.metrics.MetricsDebugTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest.html">com.aicodingcli.code.quality.ImprovedQualityAnalyzerTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.code.quality.QualityAnalyzerTest.html">com.aicodingcli.code.quality.QualityAnalyzerTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.config.ConfigManagerTest.html">com.aicodingcli.config.ConfigManagerTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest.html">com.aicodingcli.conversation.AiDrivenAutoExecutionEngineTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.096s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.conversation.AiPromptEngineTest.html">com.aicodingcli.conversation.AiPromptEngineTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.011s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.conversation.AutoExecutionEngineTest.html">com.aicodingcli.conversation.AutoExecutionEngineTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.038s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.conversation.ConversationStateManagerTest.html">com.aicodingcli.conversation.ConversationStateManagerTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.012s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.aicodingcli.conversation.RequirementParserTest.html">com.aicodingcli.conversation.RequirementParserTest</a>
</td>
<td>17</td>
<td>5</td>
<td>0</td>
<td>0.019s</td>
<td class="failures">70%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.conversation.TaskDecomposerTest.html">com.aicodingcli.conversation.TaskDecomposerTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.010s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.conversation.ToolExecutorTest.html">com.aicodingcli.conversation.ToolExecutorTest</a>
</td>
<td>22</td>
<td>0</td>
<td>0</td>
<td>0.035s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.history.HistoryManagerTest.html">com.aicodingcli.history.HistoryManagerTest</a>
</td>
<td>18</td>
<td>0</td>
<td>0</td>
<td>0.220s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.history.HistoryModelsTest.html">com.aicodingcli.history.HistoryModelsTest</a>
</td>
<td>16</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.http.HttpClientTest.html">com.aicodingcli.http.HttpClientTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.453s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.integration.ConversationSystemIntegrationTest.html">com.aicodingcli.integration.ConversationSystemIntegrationTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.018s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.plugins.AiServicePluginTest.html">com.aicodingcli.plugins.AiServicePluginTest</a>
</td>
<td>12</td>
<td>0</td>
<td>0</td>
<td>0.010s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.plugins.CommandPluginTest.html">com.aicodingcli.plugins.CommandPluginTest</a>
</td>
<td>13</td>
<td>0</td>
<td>0</td>
<td>0.009s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.plugins.PluginManagerTest.html">com.aicodingcli.plugins.PluginManagerTest</a>
</td>
<td>15</td>
<td>0</td>
<td>0</td>
<td>0.019s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.plugins.PluginSecurityTest.html">com.aicodingcli.plugins.PluginSecurityTest</a>
</td>
<td>10</td>
<td>0</td>
<td>0</td>
<td>0.006s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.aicodingcli.plugins.PluginTest.html">com.aicodingcli.plugins.PluginTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月18日 下午12:43:03</p>
</div>
</div>
</body>
</html>
