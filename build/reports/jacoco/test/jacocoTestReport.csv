G<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUCTION_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON><PERSON>_MISSED,<PERSON><PERSON>CH_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,METHOD_MISSED,METHOD_COVERED
ai-coding-cli,com.aicodingcli.code.common,ProgrammingLanguage,18,99,0,0,1,11,2,6,2,6
ai-coding-cli,com.aicodingcli.code.common,ProgrammingLanguage.Companion,0,45,0,6,0,4,0,5,0,2
ai-coding-cli,com.aicodingcli,AiCodingCli.handleConfigSet.new Function2() {...},30,0,0,0,6,0,1,0,1,0
ai-coding-cli,com.aicodingcli,MainKt,13,0,0,0,4,0,2,0,2,0
ai-coding-cli,com.aicodingcli,AiCodingCli.handleAnalyzeMetrics.new Function2() {...},7,25,0,0,2,5,0,1,0,1
ai-coding-cli,com.aicodingcli,<PERSON>CodingCli.handleConfigGet.new Function2() {...},66,0,6,0,12,0,4,0,1,0
ai-coding-cli,com.aicodingcli,AiCodingCli.AnalyzeOptions,18,12,0,0,1,3,2,2,2,2
ai-coding-cli,com.aicodingcli,AiCodingCli.handlePluginInstall.new Function2() {...},42,0,2,0,9,0,2,0,1,0
ai-coding-cli,com.aicodingcli,AiCodingCli.testConnection.new Function2() {...},25,73,4,4,4,14,4,1,0,1
ai-coding-cli,com.aicodingcli,AiCodingCli.handleAnalyzeIssues.new Function2() {...},7,25,0,0,2,5,0,1,0,1
ai-coding-cli,com.aicodingcli,AiCodingCli.askQuestion.1.new FlowCollector() {...},0,44,0,2,0,11,0,2,0,1
ai-coding-cli,com.aicodingcli,AiCodingCli.handlePluginUninstall.new Function2() {...},42,0,2,0,9,0,2,0,1,0
ai-coding-cli,com.aicodingcli,AiCodingCli,2290,1815,268,137,418,334,196,89,20,40
ai-coding-cli,com.aicodingcli,AiCodingCli.handleConfigList.new Function2() {...},9,84,1,1,2,16,1,1,0,1
ai-coding-cli,com.aicodingcli,AiCodingCli.handlePluginEnable.new Function2() {...},28,0,0,0,6,0,1,0,1,0
ai-coding-cli,com.aicodingcli,AiCodingCli.askQuestion.new Function2() {...},70,301,19,19,10,57,15,5,0,1
ai-coding-cli,com.aicodingcli,AiCodingCli.handlePluginDisable.new Function2() {...},42,0,2,0,9,0,2,0,1,0
ai-coding-cli,com.aicodingcli,AiCodingCli.handleConfigProvider.new Function2() {...},29,0,0,0,6,0,1,0,1,0
ai-coding-cli,com.aicodingcli,AiCodingCli.handleAnalyzeProject.new Function2() {...},7,24,0,0,2,5,0,1,0,1
ai-coding-cli,com.aicodingcli,AiCodingCli.CommandOptions,0,78,0,0,0,8,0,8,0,8
ai-coding-cli,com.aicodingcli,AiCodingCli.handleAnalyzeFile.new Function2() {...},7,24,0,0,2,5,0,1,0,1
ai-coding-cli,com.aicodingcli.code.analysis,DefaultCodeAnalyzer,17,421,5,17,2,58,5,16,0,10
ai-coding-cli,com.aicodingcli.code.analysis,CodeMetrics,0,33,0,0,0,6,0,6,0,6
ai-coding-cli,com.aicodingcli.code.analysis,ImprovementType,0,37,0,0,0,5,0,1,0,1
ai-coding-cli,com.aicodingcli.code.analysis,IssueSeverity,0,31,0,0,0,4,0,1,0,1
ai-coding-cli,com.aicodingcli.code.analysis,Dependency,0,27,0,0,0,5,0,5,0,5
ai-coding-cli,com.aicodingcli.code.analysis,DependencyScope,0,31,0,0,0,4,0,1,0,1
ai-coding-cli,com.aicodingcli.code.analysis,ImprovementPriority,0,25,0,0,0,3,0,1,0,1
ai-coding-cli,com.aicodingcli.code.analysis,IssueType,0,49,0,0,0,7,0,1,0,1
ai-coding-cli,com.aicodingcli.code.analysis,AnalysisSummary,0,33,0,0,0,6,0,6,0,6
ai-coding-cli,com.aicodingcli.code.analysis,Improvement,0,39,0,0,0,6,0,6,0,6
ai-coding-cli,com.aicodingcli.code.analysis,CodeIssue,0,39,0,0,0,7,0,7,0,7
ai-coding-cli,com.aicodingcli.code.analysis,DependencyType,0,25,0,0,0,3,0,1,0,1
ai-coding-cli,com.aicodingcli.code.analysis,CodeAnalysisResult,0,39,0,0,0,7,0,7,0,7
ai-coding-cli,com.aicodingcli.code.analysis,ProjectAnalysisResult,0,27,0,0,0,5,0,5,0,5
ai-coding-cli,com.aicodingcli.config,AppConfig.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.config,AppConfig,43,122,7,11,3,6,10,10,3,8
ai-coding-cli,com.aicodingcli.config,ConfigManager,168,227,13,9,21,51,12,11,3,9
ai-coding-cli,com.aicodingcli.ai,OpenAiService,57,0,0,0,13,0,4,0,4,0
ai-coding-cli,com.aicodingcli.ai,AiResponse,58,52,2,0,0,6,3,6,2,6
ai-coding-cli,com.aicodingcli.ai,AiStreamChunk.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai,MessageRole,0,25,0,0,0,3,0,1,0,1
ai-coding-cli,com.aicodingcli.ai,AiServiceConfig,107,367,27,51,0,17,28,23,1,11
ai-coding-cli,com.aicodingcli.ai,AiStreamChunk,70,32,10,0,1,4,8,4,3,4
ai-coding-cli,com.aicodingcli.ai,AiProvider,0,25,0,0,0,3,0,1,0,1
ai-coding-cli,com.aicodingcli.ai,AiRequest,238,133,51,11,0,14,31,10,2,8
ai-coding-cli,com.aicodingcli.ai,BaseAiService,21,23,4,4,0,4,5,2,1,2
ai-coding-cli,com.aicodingcli.ai,AiResponse.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai,FinishReason,0,31,0,0,0,4,0,1,0,1
ai-coding-cli,com.aicodingcli.ai,TokenUsage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai,AiRequest.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai,AiServiceFactory,0,35,0,3,0,4,0,3,0,1
ai-coding-cli,com.aicodingcli.ai,TokenUsage,41,21,2,0,1,4,3,4,2,4
ai-coding-cli,com.aicodingcli.ai,AiMessage,55,51,6,4,0,7,5,6,2,4
ai-coding-cli,com.aicodingcli.ai,AiServiceConfig.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai,AiMessage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.code.metrics,MetricsCalculator,10,539,11,49,3,91,11,29,0,10
ai-coding-cli,com.aicodingcli.conversation,StepResult,3,29,0,0,0,5,1,4,1,4
ai-coding-cli,com.aicodingcli.conversation,ExecutableTask.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,InstantSerializer,2,18,0,0,0,4,1,3,1,3
ai-coding-cli,com.aicodingcli.conversation,ExecutionContext.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,ConversationStatus.Companion,0,7,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,ActionDecisionResponseJson.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.conversation,AiExecutionStrategy,34,147,5,7,0,15,8,9,4,7
ai-coding-cli,com.aicodingcli.conversation,ToolExample,92,0,2,0,5,0,8,0,7,0
ai-coding-cli,com.aicodingcli.conversation,ProjectContext,12,56,0,0,0,8,4,4,4,4
ai-coding-cli,com.aicodingcli.conversation,ConversationState,94,224,9,25,4,8,13,18,4,10
ai-coding-cli,com.aicodingcli.conversation,ActionDecision.WaitUser,0,10,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,AnalysisResult.Success,3,37,0,0,0,8,1,6,1,6
ai-coding-cli,com.aicodingcli.conversation,ExecutionError,65,79,12,6,0,8,13,4,4,4
ai-coding-cli,com.aicodingcli.conversation,CompletionEvaluation.Failure,3,7,0,0,0,1,1,1,1,1
ai-coding-cli,com.aicodingcli.conversation,SafetyCheckResult,0,32,0,0,0,5,0,5,0,5
ai-coding-cli,com.aicodingcli.conversation,DefaultToolExecutor,31,159,1,3,10,27,1,9,0,8
ai-coding-cli,com.aicodingcli.conversation,CompletionEvaluation.Success,0,34,0,0,0,7,0,6,0,6
ai-coding-cli,com.aicodingcli.conversation,AiDrivenAutoExecutionEngine,239,1054,26,30,46,187,17,22,1,10
ai-coding-cli,com.aicodingcli.conversation,ToolParameter.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,ExecutableTask,127,220,25,17,1,10,23,11,4,9
ai-coding-cli,com.aicodingcli.conversation,ConversationState.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,ToolRiskLevel,0,25,0,0,0,3,0,1,0,1
ai-coding-cli,com.aicodingcli.conversation,TaskStatus,0,51,0,0,0,7,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,Intent,0,117,0,0,0,18,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,ToolParameter,156,86,26,0,0,9,18,6,5,6
ai-coding-cli,com.aicodingcli.conversation,ParsedRequirement.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,ActionDecision.Companion,0,33,0,0,0,5,0,5,0,5
ai-coding-cli,com.aicodingcli.conversation,CompletionEvaluationResponseJson.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.conversation,ToolResult.Companion,3,40,0,0,1,4,1,4,1,4
ai-coding-cli,com.aicodingcli.conversation,ToolMetadata.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,CompletionEvaluationResponseJson,64,72,1,1,1,6,3,7,2,7
ai-coding-cli,com.aicodingcli.conversation,AiExecutionRound,78,43,18,0,9,10,19,2,10,2
ai-coding-cli,com.aicodingcli.conversation,ConversationStateManager,89,245,8,10,15,60,8,14,4,10
ai-coding-cli,com.aicodingcli.conversation,ToolCall.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,ActionDecision.Fail,0,10,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,SafetyCheckResult.Companion,0,25,0,0,0,3,0,3,0,3
ai-coding-cli,com.aicodingcli.conversation,ConversationSession.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.conversation,AnalysisResult.Companion,0,17,0,0,0,2,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,RequirementComplexity,0,25,0,0,0,3,0,1,0,1
ai-coding-cli,com.aicodingcli.conversation,ExecutionStep.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,CompletionEvaluation.Companion,0,16,0,0,0,2,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,ValidationResult.Companion,10,18,0,0,1,2,1,2,1,2
ai-coding-cli,com.aicodingcli.conversation,ParsedRequirement,90,62,10,0,0,7,9,5,4,5
ai-coding-cli,com.aicodingcli.conversation,RequirementCategory,0,67,0,0,0,10,0,1,0,1
ai-coding-cli,com.aicodingcli.conversation,AnalysisResult.Failure,0,10,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,ActionDecision.Failure,0,10,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,DefaultAutoExecutionEngine,194,597,3,15,52,108,4,12,1,6
ai-coding-cli,com.aicodingcli.conversation,ActionDecision.ExecuteTool,3,25,0,0,0,6,1,4,1,4
ai-coding-cli,com.aicodingcli.conversation,DefaultTaskDecomposer,167,1323,57,91,14,235,47,56,1,28
ai-coding-cli,com.aicodingcli.conversation,TaskStatus.Companion,0,7,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,ExecutionContext,130,89,18,0,0,9,13,7,4,7
ai-coding-cli,com.aicodingcli.conversation,ConversationSession,37,350,17,25,0,17,17,19,0,15
ai-coding-cli,com.aicodingcli.conversation,ToolCall,58,82,7,3,1,6,8,6,3,6
ai-coding-cli,com.aicodingcli.conversation,ExecutionResult.Companion,14,28,0,0,2,16,1,2,1,2
ai-coding-cli,com.aicodingcli.conversation,ActionDecision.Complete,0,10,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,ValidationResult,3,34,0,0,0,5,1,4,1,4
ai-coding-cli,com.aicodingcli.conversation,AnalysisResponseJson.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.conversation,AnalysisResponseJson,69,78,1,1,1,7,3,8,2,8
ai-coding-cli,com.aicodingcli.conversation,ExecutionError.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation,ConversationStatus,0,63,0,0,0,9,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,ToolResult,46,130,10,8,0,8,8,10,1,8
ai-coding-cli,com.aicodingcli.conversation,ExecutionStep,64,124,12,6,0,10,13,6,4,6
ai-coding-cli,com.aicodingcli.conversation,KotlinDurationSerializer,9,11,0,0,1,3,2,2,2,2
ai-coding-cli,com.aicodingcli.conversation,ActionDecisionResponseJson,108,86,13,5,2,6,10,9,3,7
ai-coding-cli,com.aicodingcli.conversation,DefaultAiPromptEngine,24,389,4,9,3,96,4,19,0,15
ai-coding-cli,com.aicodingcli.conversation,StepResult.Companion,7,9,0,0,1,1,1,1,1,1
ai-coding-cli,com.aicodingcli.conversation,Intent.Companion,0,7,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation,ToolMetadata,161,96,26,0,0,9,17,7,4,7
ai-coding-cli,com.aicodingcli.conversation,DurationSerializer,20,0,0,0,4,0,4,0,4,0
ai-coding-cli,com.aicodingcli.conversation,ExecutionResult,0,73,0,0,0,10,0,10,0,10
ai-coding-cli,com.aicodingcli.conversation,DefaultRequirementParser,675,2141,180,242,47,231,142,111,2,35
ai-coding-cli,com.aicodingcli.conversation,ToolExample.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.plugins,PluginSecurityException,0,29,0,0,0,5,0,4,0,4
ai-coding-cli,com.aicodingcli.plugins,CommandOption,4,59,0,0,0,8,0,8,0,8
ai-coding-cli,com.aicodingcli.plugins,CommandPluginSpec,68,0,0,0,10,0,10,0,10,0
ai-coding-cli,com.aicodingcli.plugins,PluginSecurityPolicy,10,202,7,15,1,39,10,7,3,3
ai-coding-cli,com.aicodingcli.plugins,PluginSandbox,2,178,3,19,0,34,3,17,0,9
ai-coding-cli,com.aicodingcli.plugins,PluginDiscoveryService,393,64,85,5,41,13,47,4,3,3
ai-coding-cli,com.aicodingcli.plugins,AiServicePluginRegistry,0,30,0,0,0,8,0,6,0,6
ai-coding-cli,com.aicodingcli.plugins,CommandArgs,6,42,0,2,0,8,2,7,2,6
ai-coding-cli,com.aicodingcli.plugins,PluginPermission.FileSystemPermission,10,16,0,0,0,4,1,3,1,3
ai-coding-cli,com.aicodingcli.plugins,AiServicePlugin.DefaultImpls,13,27,0,2,3,7,0,2,0,1
ai-coding-cli,com.aicodingcli.plugins,CommandPlugin.DefaultImpls,0,37,0,4,0,2,0,4,0,2
ai-coding-cli,com.aicodingcli.plugins,PluginTestResult,3,18,0,0,0,4,1,3,1,3
ai-coding-cli,com.aicodingcli.plugins,PluginRegistryStatistics,0,33,0,0,0,6,0,6,0,6
ai-coding-cli,com.aicodingcli.plugins,PluginMetadata,6,90,0,0,0,12,2,10,2,10
ai-coding-cli,com.aicodingcli.plugins,PluginContextFactory,30,0,0,0,9,0,2,0,2,0
ai-coding-cli,com.aicodingcli.plugins,PluginTemplateGenerator,794,0,12,0,197,0,27,0,21,0
ai-coding-cli,com.aicodingcli.plugins,PluginOperation,6,15,0,0,0,4,2,2,2,2
ai-coding-cli,com.aicodingcli.plugins,PluginPermission.NetworkPermission,0,10,0,0,0,3,0,2,0,2
ai-coding-cli,com.aicodingcli.plugins,PluginManager,987,211,120,8,124,36,70,13,7,12
ai-coding-cli,com.aicodingcli.plugins,PluginPermission.HistoryPermission,0,4,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.plugins,LoadedPlugin,33,0,0,0,6,0,6,0,6,0
ai-coding-cli,com.aicodingcli.plugins,MockConfigManager,13,24,0,0,3,9,3,1,3,1
ai-coding-cli,com.aicodingcli.plugins,PluginEvent,11,33,0,0,0,6,3,3,3,3
ai-coding-cli,com.aicodingcli.plugins,BaseCommandPlugin,87,92,12,2,13,25,11,9,4,9
ai-coding-cli,com.aicodingcli.plugins,CommandResult,21,27,2,0,1,5,2,5,1,5
ai-coding-cli,com.aicodingcli.plugins,DefaultPluginLogger,41,40,7,1,10,11,7,4,3,4
ai-coding-cli,com.aicodingcli.plugins,BaseCommandPlugin.createCommand.new Function3() {...},17,26,0,0,2,5,0,1,0,1
ai-coding-cli,com.aicodingcli.plugins,PluginValidationResult,2,35,0,0,0,5,0,5,0,5
ai-coding-cli,com.aicodingcli.plugins,PluginOperationType,0,43,0,0,0,6,0,1,0,1
ai-coding-cli,com.aicodingcli.plugins,PluginEventType,0,55,0,0,0,8,0,1,0,1
ai-coding-cli,com.aicodingcli.plugins,PluginExecutionException,0,15,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.plugins,PluginSecurityManager,120,181,23,7,17,36,17,13,4,11
ai-coding-cli,com.aicodingcli.plugins,CommandResult.Companion,2,66,0,2,0,9,0,7,0,6
ai-coding-cli,com.aicodingcli.plugins,PluginPermission.SystemPermission,0,10,0,0,0,3,0,2,0,2
ai-coding-cli,com.aicodingcli.plugins,EventPluginSpec,61,0,0,0,9,0,9,0,9,0
ai-coding-cli,com.aicodingcli.plugins,MockHistoryManager,13,3,0,0,5,1,1,1,1,1
ai-coding-cli,com.aicodingcli.plugins,PluginCommand,19,27,0,0,1,6,3,4,3,4
ai-coding-cli,com.aicodingcli.plugins,PluginDependency,11,21,0,0,1,4,1,4,1,4
ai-coding-cli,com.aicodingcli.plugins,SingleCommandPlugin,7,0,0,0,2,0,2,0,2,0
ai-coding-cli,com.aicodingcli.plugins,TestPluginContext,35,73,0,0,8,18,5,7,5,7
ai-coding-cli,com.aicodingcli.plugins,PluginLoadException,0,15,0,0,0,1,0,2,0,2
ai-coding-cli,com.aicodingcli.plugins,AiServicePluginSpec,87,0,0,0,12,0,12,0,12,0
ai-coding-cli,com.aicodingcli.plugins,DefaultPluginContext,134,0,0,0,28,0,13,0,13,0
ai-coding-cli,com.aicodingcli.plugins,PluginTemplate,21,0,0,0,4,0,4,0,4,0
ai-coding-cli,com.aicodingcli.plugins,PluginSandboxInfo,0,39,0,0,0,7,0,7,0,7
ai-coding-cli,com.aicodingcli.plugins,PluginInfo,0,27,0,0,0,5,0,5,0,5
ai-coding-cli,com.aicodingcli.plugins,PluginRegistry,250,129,20,2,49,28,21,11,10,11
ai-coding-cli,com.aicodingcli.plugins,PluginState,0,43,0,0,0,6,0,1,0,1
ai-coding-cli,com.aicodingcli.plugins,PluginPermission.ConfigPermission,0,4,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.plugins,MockAiService,122,0,8,0,24,0,10,0,6,0
ai-coding-cli,com.aicodingcli.plugins,PluginTestFramework,65,230,7,11,14,61,9,9,2,7
ai-coding-cli,com.aicodingcli.plugins,PluginLogger.DefaultImpls,10,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.plugins,BaseAiServicePlugin,52,139,12,10,9,33,14,8,3,8
ai-coding-cli,com.aicodingcli.plugins,PluginEventDispatcher,82,30,6,2,17,4,7,3,3,3
ai-coding-cli,com.aicodingcli.plugins,MockAiServiceFactory,11,3,0,0,1,1,1,1,1,1
ai-coding-cli,com.aicodingcli.conversation.tools,WebFetchHandler.executeInternal.new Function2() {...},55,0,0,0,14,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation.tools,GitHubApiHandler.executeInternal.new Function2() {...},60,68,11,3,7,13,7,1,0,1
ai-coding-cli,com.aicodingcli.conversation.tools,UpdateTasksHandler,49,49,6,0,8,8,5,2,2,2
ai-coding-cli,com.aicodingcli.conversation.tools,TaskManagementHandler,3,109,1,1,0,15,1,4,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,SaveFileHandler,36,159,13,11,3,23,9,7,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,DiagnosticsHandler,7,128,5,5,0,19,5,4,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,GitHubApiHandler,2,176,2,6,0,20,2,6,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,DefaultToolHandlerFactory,0,111,0,0,0,19,0,2,0,2
ai-coding-cli,com.aicodingcli.conversation.tools,ExecutionContext,67,0,0,0,10,0,8,0,8,0
ai-coding-cli,com.aicodingcli.conversation.tools,ToolExecutionStatsCollector,5,104,2,2,0,26,2,5,0,5
ai-coding-cli,com.aicodingcli.conversation.tools,CodebaseRetrievalHandler,3,70,1,1,0,13,1,4,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,GitOperationsHandler,116,49,8,0,9,8,6,2,2,2
ai-coding-cli,com.aicodingcli.conversation.tools,WebFetchHandler,59,28,4,0,10,7,4,2,2,2
ai-coding-cli,com.aicodingcli.conversation.tools,OpenBrowserHandler.executeInternal.new Function2() {...},44,0,0,0,10,0,1,0,1,0
ai-coding-cli,com.aicodingcli.conversation.tools,RemoveFilesHandler,101,28,6,0,13,7,5,2,2,2
ai-coding-cli,com.aicodingcli.conversation.tools,ToolExecutionStatsCollector.ToolExecutionRecord,6,21,0,0,0,5,2,3,2,3
ai-coding-cli,com.aicodingcli.conversation.tools,OpenBrowserHandler,59,28,4,0,10,7,4,2,2,2
ai-coding-cli,com.aicodingcli.conversation.tools,BaseToolHandler,12,84,3,7,1,14,3,6,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,StrReplaceEditorHandler,8,170,1,5,1,24,1,6,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,WebSearchHandler,7,163,4,8,2,29,4,6,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,LaunchProcessHandler.executeInternal.new Function2() {...},61,117,5,5,13,21,5,1,0,1
ai-coding-cli,com.aicodingcli.conversation.tools,ViewHandler,45,154,11,7,7,22,8,6,1,4
ai-coding-cli,com.aicodingcli.conversation.tools,ToolHandlerRegistry,6,35,0,0,1,6,1,4,1,4
ai-coding-cli,com.aicodingcli.conversation.tools,GitOperationsHandler.executeInternal.new Function2() {...},120,0,22,0,21,0,15,0,1,0
ai-coding-cli,com.aicodingcli.conversation.tools,ExecutionMetadataManager,38,57,0,0,5,9,1,2,1,2
ai-coding-cli,com.aicodingcli.conversation.tools,LaunchProcessHandler,14,162,4,10,3,23,4,7,0,4
ai-coding-cli,com.aicodingcli.conversation.tools,ToolExecutionStats,25,27,2,0,1,7,6,3,5,3
ai-coding-cli,com.aicodingcli.conversation.tools,ReadTerminalHandler,11,28,0,0,4,7,2,2,2,2
ai-coding-cli,com.aicodingcli.http,AiHttpClient.post.new Function1() {...},0,58,0,0,0,8,0,1,0,1
ai-coding-cli,com.aicodingcli.http,AiHttpClient.get.new Function1() {...},0,43,0,0,0,6,0,1,0,1
ai-coding-cli,com.aicodingcli.http,RequestConfig,81,0,4,0,9,0,8,0,6,0
ai-coding-cli,com.aicodingcli.http,AiHttpClient.put.new Function1() {...},58,0,0,0,8,0,1,0,1,0
ai-coding-cli,com.aicodingcli.http,HttpException,0,31,0,0,0,5,0,4,0,4
ai-coding-cli,com.aicodingcli.http,AiHttpClient,90,407,6,16,12,66,13,21,7,16
ai-coding-cli,com.aicodingcli.http,RetryConfig,36,86,8,8,0,12,8,6,0,6
ai-coding-cli,com.aicodingcli.http,AiHttpClient.postStream.new Function2() {...},54,126,7,7,6,17,6,2,0,1
ai-coding-cli,com.aicodingcli.http,HttpResponse,0,21,0,0,0,4,0,4,0,4
ai-coding-cli,com.aicodingcli.http,AiHttpClient.delete.new Function1() {...},43,0,0,0,6,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiService.streamChat.2.new FlowCollector() {...},90,0,23,0,16,0,14,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiChatRequest,106,116,17,9,2,7,20,3,7,3
ai-coding-cli,com.aicodingcli.ai.providers,OllamaStreamResponse,428,0,66,0,21,0,48,0,15,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeErrorResponse.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeUsage,27,20,1,1,1,3,3,3,2,3
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiMessage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeError.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiErrorResponse,20,14,1,1,1,2,3,2,2,2
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiStreamDelta.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeContent.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiUsage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeService.streamChat.2.new FlowCollector() {...},105,0,26,0,16,0,15,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiError,103,39,15,3,2,5,13,4,4,4
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiErrorResponse.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiChoice.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaRequest,102,141,17,9,0,10,20,4,7,4
ai-coding-cli,com.aicodingcli.ai.providers,OllamaModel.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaService.streamChat.2.new FlowCollector() {...},14,50,7,5,4,10,5,2,0,1
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeStreamMessage,256,0,18,0,13,0,22,0,13,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeService.streamChat.new Function2() {...},87,0,0,0,14,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaModel,201,47,29,5,9,3,27,1,10,1
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeStreamEvent,216,0,34,0,8,0,26,0,9,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeStreamDelta,171,0,26,0,9,0,21,0,8,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaException,10,5,0,0,1,2,1,1,1,1
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiStreamChoice.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiChatResponse.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OllamaModelsResponse,27,25,1,1,2,1,4,2,3,2
ai-coding-cli,com.aicodingcli.ai.providers,OllamaResponse,286,115,43,15,7,14,30,14,8,7
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiStreamResponse.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiStreamDelta,109,0,18,0,5,0,15,0,6,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaStreamResponse.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeRequest,125,172,20,14,0,11,23,6,8,4
ai-coding-cli,com.aicodingcli.ai.providers,OllamaMessage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeStreamDelta.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiChatRequest.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeStreamMessage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaModelDetails,136,76,13,5,7,3,16,4,9,2
ai-coding-cli,com.aicodingcli.ai.providers,OllamaResponseMessage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiChatResponse,80,69,1,1,4,4,6,5,5,5
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeResponse,105,86,1,1,5,5,7,6,6,6
ai-coding-cli,com.aicodingcli.ai.providers,OllamaModelsResponse.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiModel,114,28,15,3,6,1,16,1,7,1
ai-coding-cli,com.aicodingcli.ai.providers,OllamaResponse.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiUsage,36,26,1,1,1,4,3,4,2,4
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeException,19,8,0,0,1,3,2,1,2,1
ai-coding-cli,com.aicodingcli.ai.providers,OllamaModelDetails.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiModelsResponse.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OllamaErrorResponse,18,14,1,1,1,2,3,2,2,2
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeMessage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiService,53,281,7,7,12,67,7,13,0,11
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiStreamResponse,128,0,2,0,7,0,10,0,9,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaRequest.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OllamaService,36,298,7,12,7,72,7,14,0,11
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiModelsResponse,39,32,1,1,3,1,5,2,4,2
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeService,58,328,13,10,11,73,11,13,0,11
ai-coding-cli,com.aicodingcli.ai.providers,OllamaOptions,156,171,32,18,1,11,32,4,7,4
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeUsage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaMessage,53,70,7,3,0,6,9,4,4,4
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiError.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiMessage,8,39,1,1,0,4,2,4,1,4
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeErrorResponse,32,17,1,1,2,2,4,2,3,2
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiModel.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiService.streamChat.new Function2() {...},88,0,0,0,14,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeError,27,20,1,1,1,3,3,3,2,3
ai-coding-cli,com.aicodingcli.ai.providers,OllamaResponseMessage,76,47,8,2,3,3,10,3,5,3
ai-coding-cli,com.aicodingcli.ai.providers,OllamaErrorResponse.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiStreamChoice,99,0,10,0,7,0,12,0,7,0
ai-coding-cli,com.aicodingcli.ai.providers,OllamaService.streamChat.new Function2() {...},16,68,0,0,4,10,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeMessage,25,22,2,0,0,4,4,2,3,2
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeContent,30,17,1,1,2,2,4,2,3,2
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeStreamEvent.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeRequest.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiException,28,11,0,0,1,4,3,1,3,1
ai-coding-cli,com.aicodingcli.ai.providers,OllamaOptions.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.ai.providers,ClaudeResponse.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.ai.providers,OpenAiChoice,43,23,1,1,2,3,4,3,3,3
ai-coding-cli,com.aicodingcli.history,MessageTokenUsage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.history,HistoryStatistics,78,77,2,0,0,8,3,8,2,8
ai-coding-cli,com.aicodingcli.history,MessageTokenUsage,5,57,1,1,0,5,1,6,0,6
ai-coding-cli,com.aicodingcli.history,ConversationMessage,26,238,9,25,0,11,9,18,0,10
ai-coding-cli,com.aicodingcli.history,ConversationSession,26,351,10,34,0,18,10,28,0,16
ai-coding-cli,com.aicodingcli.history,ConversationMessage.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.history,ConversationSession.Companion,0,3,0,0,0,1,0,1,0,1
ai-coding-cli,com.aicodingcli.history,HistoryManager,91,617,15,48,11,92,13,36,2,20
ai-coding-cli,com.aicodingcli.history,HistorySearchCriteria,0,78,0,0,0,8,0,8,0,8
ai-coding-cli,com.aicodingcli.history,HistoryStatistics.Companion,3,0,0,0,1,0,1,0,1,0
ai-coding-cli,com.aicodingcli.code.quality,QualityAnalyzer,83,913,17,53,26,189,17,31,1,11
