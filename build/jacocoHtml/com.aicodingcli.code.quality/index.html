<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.code.quality</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.code.quality</span></div><h1>com.aicodingcli.code.quality</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">83 of 996</td><td class="ctr2">91%</td><td class="bar">17 of 70</td><td class="ctr2">75%</td><td class="ctr1">17</td><td class="ctr2">48</td><td class="ctr1">26</td><td class="ctr2">215</td><td class="ctr1">1</td><td class="ctr2">12</td><td class="ctr1">0</td><td class="ctr2">1</td></tr></tfoot><tbody><tr><td id="a0"><a href="QualityAnalyzer.html" class="el_class">QualityAnalyzer</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="83" alt="83"/><img src="../jacoco-resources/greenbar.gif" width="110" height="10" title="913" alt="913"/></td><td class="ctr2" id="c0">91%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="53" alt="53"/></td><td class="ctr2" id="e0">75%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g0">48</td><td class="ctr1" id="h0">26</td><td class="ctr2" id="i0">215</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">12</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>