<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>QualityAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.code.quality</a> &gt; <span class="el_class">QualityAnalyzer</span></div><h1>QualityAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">83 of 996</td><td class="ctr2">91%</td><td class="bar">17 of 70</td><td class="ctr2">75%</td><td class="ctr1">17</td><td class="ctr2">48</td><td class="ctr1">26</td><td class="ctr2">215</td><td class="ctr1">1</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a0"><a href="QualityAnalyzer.kt.html#L230" class="el_method">detectGenericIssues(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="36" alt="36"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h0">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="QualityAnalyzer.kt.html#L52" class="el_method">detectJvmLanguageIssues(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="113" height="10" title="346" alt="346"/></td><td class="ctr2" id="c7">94%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="92" height="10" title="17" alt="17"/></td><td class="ctr2" id="e4">77%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h1">7</td><td class="ctr2" id="i0">72</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a7"><a href="QualityAnalyzer.kt.html#L308" class="el_method">detectReadabilityImprovements(String, ProgrammingLanguage)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="152" alt="152"/></td><td class="ctr2" id="c8">93%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="54" height="10" title="10" alt="10"/></td><td class="ctr2" id="e7">58%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g1">10</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i1">30</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="QualityAnalyzer.kt.html#L250" class="el_method">detectPerformanceImprovements(String, ProgrammingLanguage)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="40" alt="40"/></td><td class="ctr2" id="c10">80%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="3" alt="3"/></td><td class="ctr2" id="e5">75%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h2">5</td><td class="ctr2" id="i5">14</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="QualityAnalyzer.kt.html#L15" class="el_method">detectIssues(String, ProgrammingLanguage)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="28" alt="28"/></td><td class="ctr2" id="c9">82%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">66%</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i9">6</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="QualityAnalyzer.kt.html#L276" class="el_method">detectMaintainabilityImprovements(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="101" alt="101"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i2">20</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="QualityAnalyzer.kt.html#L181" class="el_method">detectPerformanceIssues(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="73" alt="73"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="7" alt="7"/></td><td class="ctr2" id="e3">87%</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i3">18</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="QualityAnalyzer.kt.html#L151" class="el_method">detectSecurityIssues(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="56" alt="56"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i4">18</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="QualityAnalyzer.kt.html#L211" class="el_method">detectPythonIssues(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="43" alt="43"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a9"><a href="QualityAnalyzer.kt.html#L358" class="el_method">findLineNumber(String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="41" alt="41"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d8"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a11"><a href="QualityAnalyzer.kt.html#L37" class="el_method">suggestImprovements(String, ProgrammingLanguage)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="30" alt="30"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">5</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a10"><a href="QualityAnalyzer.kt.html#L9" class="el_method">QualityAnalyzer()</a></td><td class="bar" id="b11"/><td class="ctr2" id="c6">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>