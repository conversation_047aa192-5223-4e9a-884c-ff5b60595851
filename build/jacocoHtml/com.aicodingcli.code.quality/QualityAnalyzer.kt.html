<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>QualityAnalyzer.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.code.quality</a> &gt; <span class="el_source">QualityAnalyzer.kt</span></div><h1>QualityAnalyzer.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.code.quality

import com.aicodingcli.code.analysis.*
import com.aicodingcli.code.common.ProgrammingLanguage

/**
 * Analyzer for code quality issues and improvements
 */
<span class="fc" id="L9">class QualityAnalyzer {</span>

    /**
     * Detect issues in code
     */
    fun detectIssues(code: String, language: ProgrammingLanguage): List&lt;CodeIssue&gt; {
<span class="fc" id="L15">        val issues = mutableListOf&lt;CodeIssue&gt;()</span>
        
<span class="pc bpc" id="L17" title="1 of 3 branches missed.">        when (language) {</span>
            ProgrammingLanguage.KOTLIN, ProgrammingLanguage.JAVA -&gt; {
<span class="fc" id="L19">                issues.addAll(detectJvmLanguageIssues(code))</span>
            }
            ProgrammingLanguage.PYTHON -&gt; {
<span class="fc" id="L22">                issues.addAll(detectPythonIssues(code))</span>
            }
            else -&gt; {
                // Generic issues for other languages
<span class="pc" id="L26">                issues.addAll(detectGenericIssues(code))</span>
            }
        }
        
<span class="fc" id="L30">        return issues</span>
    }

    /**
     * Suggest improvements for code
     */
    fun suggestImprovements(code: String, language: ProgrammingLanguage): List&lt;Improvement&gt; {
<span class="fc" id="L37">        val suggestions = mutableListOf&lt;Improvement&gt;()</span>
        
        // Performance improvements
<span class="fc" id="L40">        suggestions.addAll(detectPerformanceImprovements(code, language))</span>
        
        // Maintainability improvements
<span class="fc" id="L43">        suggestions.addAll(detectMaintainabilityImprovements(code))</span>
        
        // Readability improvements
<span class="fc" id="L46">        suggestions.addAll(detectReadabilityImprovements(code, language))</span>
        
<span class="fc" id="L48">        return suggestions</span>
    }

    private fun detectJvmLanguageIssues(code: String): List&lt;CodeIssue&gt; {
<span class="fc" id="L52">        val issues = mutableListOf&lt;CodeIssue&gt;()</span>

        // Check for bad class names
<span class="fc" id="L55">        val badClassNameRegex = Regex(&quot;class\\s+([a-z][a-zA-Z0-9]*)&quot;)</span>
<span class="fc" id="L56">        badClassNameRegex.findAll(code).forEach { match -&gt;</span>
<span class="fc" id="L57">            issues.add(CodeIssue(</span>
<span class="fc" id="L58">                type = IssueType.NAMING_CONVENTION,</span>
<span class="fc" id="L59">                severity = IssueSeverity.MEDIUM,</span>
<span class="fc" id="L60">                message = &quot;Class name '${match.groupValues[1]}' should start with uppercase letter&quot;,</span>
<span class="fc" id="L61">                line = findLineNumber(code, match.value),</span>
<span class="fc" id="L62">                column = null,</span>
<span class="pc bpc" id="L63" title="2 of 4 branches missed.">                suggestion = &quot;Use PascalCase for class names (e.g., ${match.groupValues[1].replaceFirstChar { it.uppercase() }})&quot;</span>
            ))
<span class="fc" id="L65">        }</span>

        // Check for bad method names (should be camelCase)
<span class="fc" id="L68">        val badMethodNameRegex = Regex(&quot;fun\\s+([A-Z][a-zA-Z0-9]*|\\w*_\\w*)&quot;)</span>
<span class="fc" id="L69">        badMethodNameRegex.findAll(code).forEach { match -&gt;</span>
<span class="fc" id="L70">            val methodName = match.groupValues[1]</span>
<span class="fc" id="L71">            issues.add(CodeIssue(</span>
<span class="fc" id="L72">                type = IssueType.NAMING_CONVENTION,</span>
<span class="fc" id="L73">                severity = IssueSeverity.MEDIUM,</span>
<span class="fc" id="L74">                message = &quot;Method name '$methodName' should use camelCase&quot;,</span>
<span class="fc" id="L75">                line = findLineNumber(code, match.value),</span>
<span class="fc" id="L76">                column = null,</span>
<span class="fc" id="L77">                suggestion = &quot;Use camelCase for method names&quot;</span>
            ))
<span class="fc" id="L79">        }</span>

        // Check for bad variable names
<span class="fc" id="L82">        val badVariableNameRegex = Regex(&quot;(val|var)\\s+([A-Z][a-zA-Z0-9]*|\\w*_\\w*|[a-z])\\s*=&quot;)</span>
<span class="fc" id="L83">        badVariableNameRegex.findAll(code).forEach { match -&gt;</span>
<span class="fc" id="L84">            val varName = match.groupValues[2]</span>
<span class="pc bpc" id="L85" title="1 of 8 branches missed.">            if (varName.length == 1 &amp;&amp; varName != &quot;x&quot; &amp;&amp; varName != &quot;y&quot; &amp;&amp; varName != &quot;z&quot;) {</span>
<span class="nc" id="L86">                issues.add(CodeIssue(</span>
<span class="nc" id="L87">                    type = IssueType.NAMING_CONVENTION,</span>
<span class="nc" id="L88">                    severity = IssueSeverity.LOW,</span>
<span class="nc" id="L89">                    message = &quot;Variable name '$varName' is too short&quot;,</span>
<span class="nc" id="L90">                    line = findLineNumber(code, match.value),</span>
<span class="nc" id="L91">                    column = null,</span>
<span class="nc" id="L92">                    suggestion = &quot;Use descriptive variable names&quot;</span>
                ))
<span class="fc bfc" id="L94" title="All 2 branches covered.">            } else if (varName.contains(&quot;_&quot;)) {</span>
<span class="fc" id="L95">                issues.add(CodeIssue(</span>
<span class="fc" id="L96">                    type = IssueType.NAMING_CONVENTION,</span>
<span class="fc" id="L97">                    severity = IssueSeverity.MEDIUM,</span>
<span class="fc" id="L98">                    message = &quot;Variable name '$varName' should use camelCase instead of snake_case&quot;,</span>
<span class="fc" id="L99">                    line = findLineNumber(code, match.value),</span>
<span class="fc" id="L100">                    column = null,</span>
<span class="fc" id="L101">                    suggestion = &quot;Use camelCase for variable names&quot;</span>
                ))
            }
<span class="fc" id="L104">        }</span>
        
        // Check for unused variables (simplified)
<span class="fc" id="L107">        val unusedVarRegex = Regex(&quot;(val|var)\\s+(\\w+)\\s*=&quot;)</span>
<span class="fc" id="L108">        unusedVarRegex.findAll(code).forEach { match -&gt;</span>
<span class="fc" id="L109">            val varName = match.groupValues[2]</span>
<span class="fc" id="L110">            val restOfCode = code.substring(match.range.last + 1)</span>
<span class="fc bfc" id="L111" title="All 2 branches covered.">            if (!restOfCode.contains(varName)) {</span>
<span class="fc" id="L112">                issues.add(CodeIssue(</span>
<span class="fc" id="L113">                    type = IssueType.UNUSED_CODE,</span>
<span class="fc" id="L114">                    severity = IssueSeverity.LOW,</span>
<span class="fc" id="L115">                    message = &quot;Variable '$varName' is declared but never used&quot;,</span>
<span class="fc" id="L116">                    line = findLineNumber(code, match.value),</span>
<span class="fc" id="L117">                    column = null,</span>
<span class="fc" id="L118">                    suggestion = &quot;Remove unused variable or use it in the code&quot;</span>
                ))
            }
<span class="fc" id="L121">        }</span>
        
        // Check for magic numbers (avoid duplicates)
<span class="fc" id="L124">        val magicNumbers = mutableSetOf&lt;String&gt;()</span>
<span class="fc" id="L125">        val magicNumberRegex = Regex(&quot;\\b([0-9]{2,})\\b&quot;)</span>
<span class="fc" id="L126">        magicNumberRegex.findAll(code).forEach { match -&gt;</span>
<span class="fc" id="L127">            val number = match.groupValues[1]</span>
<span class="pc bpc" id="L128" title="2 of 6 branches missed.">            if (number != &quot;100&quot; &amp;&amp; number != &quot;1000&quot; &amp;&amp; !magicNumbers.contains(number)) { // Common acceptable numbers</span>
<span class="fc" id="L129">                magicNumbers.add(number)</span>
<span class="fc" id="L130">                issues.add(CodeIssue(</span>
<span class="fc" id="L131">                    type = IssueType.CODE_SMELL,</span>
<span class="fc" id="L132">                    severity = IssueSeverity.LOW,</span>
<span class="fc" id="L133">                    message = &quot;Magic number '$number' should be replaced with a named constant&quot;,</span>
<span class="fc" id="L134">                    line = findLineNumber(code, match.value),</span>
<span class="fc" id="L135">                    column = null,</span>
<span class="fc" id="L136">                    suggestion = &quot;Define a constant with a meaningful name&quot;</span>
                ))
            }
<span class="fc" id="L139">        }</span>

        // Check for security vulnerabilities
<span class="fc" id="L142">        issues.addAll(detectSecurityIssues(code))</span>

        // Check for performance issues
<span class="fc" id="L145">        issues.addAll(detectPerformanceIssues(code))</span>

<span class="fc" id="L147">        return issues</span>
    }

    private fun detectSecurityIssues(code: String): List&lt;CodeIssue&gt; {
<span class="fc" id="L151">        val issues = mutableListOf&lt;CodeIssue&gt;()</span>

        // SQL injection vulnerability
<span class="fc bfc" id="L154" title="All 2 branches covered.">        if (code.contains(Regex(&quot;\&quot;.*SELECT.*\\$\\{?\\w+\\}?.*\&quot;&quot;))) {</span>
<span class="fc" id="L155">            issues.add(CodeIssue(</span>
<span class="fc" id="L156">                type = IssueType.SECURITY,</span>
<span class="fc" id="L157">                severity = IssueSeverity.HIGH,</span>
<span class="fc" id="L158">                message = &quot;Potential SQL injection vulnerability&quot;,</span>
<span class="fc" id="L159">                line = findLineNumber(code, &quot;SELECT&quot;),</span>
<span class="fc" id="L160">                column = null,</span>
<span class="fc" id="L161">                suggestion = &quot;Use parameterized queries or prepared statements&quot;</span>
            ))
        }

        // Weak random number generation
<span class="fc bfc" id="L166" title="All 2 branches covered.">        if (code.contains(&quot;Math.random()&quot;)) {</span>
<span class="fc" id="L167">            issues.add(CodeIssue(</span>
<span class="fc" id="L168">                type = IssueType.SECURITY,</span>
<span class="fc" id="L169">                severity = IssueSeverity.MEDIUM,</span>
<span class="fc" id="L170">                message = &quot;Weak random number generation&quot;,</span>
<span class="fc" id="L171">                line = findLineNumber(code, &quot;Math.random()&quot;),</span>
<span class="fc" id="L172">                column = null,</span>
<span class="fc" id="L173">                suggestion = &quot;Use SecureRandom for cryptographic purposes&quot;</span>
            ))
        }

<span class="fc" id="L177">        return issues</span>
    }

    private fun detectPerformanceIssues(code: String): List&lt;CodeIssue&gt; {
<span class="fc" id="L181">        val issues = mutableListOf&lt;CodeIssue&gt;()</span>

        // Inefficient string concatenation in loops (simplified detection)
<span class="pc bpc" id="L184" title="1 of 4 branches missed.">        if (code.contains(&quot;for&quot;) &amp;&amp; code.contains(&quot;+=&quot;)) {</span>
<span class="fc" id="L185">            issues.add(CodeIssue(</span>
<span class="fc" id="L186">                type = IssueType.PERFORMANCE,</span>
<span class="fc" id="L187">                severity = IssueSeverity.MEDIUM,</span>
<span class="fc" id="L188">                message = &quot;Inefficient string concatenation in loop&quot;,</span>
<span class="fc" id="L189">                line = findLineNumber(code, &quot;+=&quot;),</span>
<span class="fc" id="L190">                column = null,</span>
<span class="fc" id="L191">                suggestion = &quot;Use StringBuilder for string concatenation in loops&quot;</span>
            ))
        }

        // Inefficient contains check
<span class="fc bfc" id="L196" title="All 4 branches covered.">        if (code.contains(&quot;contains(&quot;) &amp;&amp; code.contains(&quot;for&quot;)) {</span>
<span class="fc" id="L197">            issues.add(CodeIssue(</span>
<span class="fc" id="L198">                type = IssueType.PERFORMANCE,</span>
<span class="fc" id="L199">                severity = IssueSeverity.LOW,</span>
<span class="fc" id="L200">                message = &quot;Inefficient contains() check in loop&quot;,</span>
<span class="fc" id="L201">                line = findLineNumber(code, &quot;contains(&quot;),</span>
<span class="fc" id="L202">                column = null,</span>
<span class="fc" id="L203">                suggestion = &quot;Consider using Set for frequent contains() operations&quot;</span>
            ))
        }

<span class="fc" id="L207">        return issues</span>
    }

    private fun detectPythonIssues(code: String): List&lt;CodeIssue&gt; {
<span class="fc" id="L211">        val issues = mutableListOf&lt;CodeIssue&gt;()</span>
        
        // Check for snake_case violations in function names
<span class="fc" id="L214">        val badFunctionNameRegex = Regex(&quot;def\\s+([a-z][a-zA-Z0-9]*[A-Z][a-zA-Z0-9]*)&quot;)</span>
<span class="fc" id="L215">        badFunctionNameRegex.findAll(code).forEach { match -&gt;</span>
<span class="fc" id="L216">            issues.add(CodeIssue(</span>
<span class="fc" id="L217">                type = IssueType.NAMING_CONVENTION,</span>
<span class="fc" id="L218">                severity = IssueSeverity.MEDIUM,</span>
<span class="fc" id="L219">                message = &quot;Function name '${match.groupValues[1]}' should use snake_case&quot;,</span>
<span class="fc" id="L220">                line = findLineNumber(code, match.value),</span>
<span class="fc" id="L221">                column = null,</span>
<span class="fc" id="L222">                suggestion = &quot;Use snake_case for function names&quot;</span>
            ))
<span class="fc" id="L224">        }</span>
        
<span class="fc" id="L226">        return issues</span>
    }

    private fun detectGenericIssues(code: String): List&lt;CodeIssue&gt; {
<span class="nc" id="L230">        val issues = mutableListOf&lt;CodeIssue&gt;()</span>
        
        // Check for very long lines
<span class="nc" id="L233">        code.lines().forEachIndexed { index, line -&gt;</span>
<span class="nc bnc" id="L234" title="All 2 branches missed.">            if (line.length &gt; 120) {</span>
<span class="nc" id="L235">                issues.add(CodeIssue(</span>
<span class="nc" id="L236">                    type = IssueType.CODE_SMELL,</span>
<span class="nc" id="L237">                    severity = IssueSeverity.LOW,</span>
<span class="nc" id="L238">                    message = &quot;Line is too long (${line.length} characters)&quot;,</span>
<span class="nc" id="L239">                    line = index + 1,</span>
<span class="nc" id="L240">                    column = null,</span>
<span class="nc" id="L241">                    suggestion = &quot;Break long lines into multiple lines&quot;</span>
                ))
            }
<span class="nc" id="L244">        }</span>
        
<span class="nc" id="L246">        return issues</span>
    }

    private fun detectPerformanceImprovements(code: String, language: ProgrammingLanguage): List&lt;Improvement&gt; {
<span class="fc" id="L250">        val suggestions = mutableListOf&lt;Improvement&gt;()</span>
        
        // Check for string concatenation in loops
<span class="fc bfc" id="L253" title="All 2 branches covered.">        if (code.contains(Regex(&quot;\\+\\s*=\\s*.*\\+\\s*&quot;))) {</span>
<span class="fc" id="L254">            suggestions.add(Improvement(</span>
<span class="fc" id="L255">                type = ImprovementType.PERFORMANCE,</span>
<span class="fc" id="L256">                description = &quot;Consider using StringBuilder for string concatenation in loops&quot;,</span>
<span class="fc" id="L257">                line = findLineNumber(code, &quot;\\+\\s*=\\s*.*\\+\\s*&quot;),</span>
<span class="fc" id="L258">                priority = ImprovementPriority.MEDIUM</span>
            ))
        }
        
        // Check for inefficient collection operations
<span class="pc bpc" id="L263" title="1 of 2 branches missed.">        if (code.contains(Regex(&quot;list\\.contains\\(.*\\).*for&quot;))) {</span>
<span class="nc" id="L264">            suggestions.add(Improvement(</span>
<span class="nc" id="L265">                type = ImprovementType.PERFORMANCE,</span>
<span class="nc" id="L266">                description = &quot;Consider using Set for frequent contains() operations&quot;,</span>
<span class="nc" id="L267">                line = null,</span>
<span class="nc" id="L268">                priority = ImprovementPriority.LOW</span>
            ))
        }
        
<span class="fc" id="L272">        return suggestions</span>
    }

    private fun detectMaintainabilityImprovements(code: String): List&lt;Improvement&gt; {
<span class="fc" id="L276">        val suggestions = mutableListOf&lt;Improvement&gt;()</span>
        
        // Check for complex nested conditions
<span class="fc" id="L279">        val nestedIfCount = code.split(&quot;if&quot;).size - 1</span>
<span class="fc bfc" id="L280" title="All 2 branches covered.">        if (nestedIfCount &gt;= 3) {</span>
<span class="fc" id="L281">            suggestions.add(Improvement(</span>
<span class="fc" id="L282">                type = ImprovementType.MAINTAINABILITY,</span>
<span class="fc" id="L283">                description = &quot;Consider extracting complex conditional logic into separate methods&quot;,</span>
<span class="fc" id="L284">                line = null,</span>
<span class="fc" id="L285">                priority = ImprovementPriority.HIGH</span>
            ))
        }
        
        // Check for long methods (simplified)
<span class="fc" id="L290">        val methodRegex = Regex(&quot;fun\\s+\\w+\\([^)]*\\)\\s*\\{([^}]+)\\}&quot;)</span>
<span class="fc" id="L291">        methodRegex.findAll(code).forEach { match -&gt;</span>
<span class="fc" id="L292">            val methodBody = match.groupValues[1]</span>
<span class="fc bfc" id="L293" title="All 2 branches covered.">            val methodLines = methodBody.lines().filter { it.trim().isNotEmpty() }</span>
<span class="fc bfc" id="L294" title="All 2 branches covered.">            if (methodLines.size &gt; 20) {</span>
<span class="fc" id="L295">                suggestions.add(Improvement(</span>
<span class="fc" id="L296">                    type = ImprovementType.MAINTAINABILITY,</span>
<span class="fc" id="L297">                    description = &quot;Method is too long (${methodLines.size} lines). Consider breaking it into smaller methods&quot;,</span>
<span class="fc" id="L298">                    line = findLineNumber(code, match.value),</span>
<span class="fc" id="L299">                    priority = ImprovementPriority.MEDIUM</span>
                ))
            }
<span class="fc" id="L302">        }</span>
        
<span class="fc" id="L304">        return suggestions</span>
    }

    private fun detectReadabilityImprovements(code: String, language: ProgrammingLanguage): List&lt;Improvement&gt; {
<span class="fc" id="L308">        val suggestions = mutableListOf&lt;Improvement&gt;()</span>
        
        // Check for missing documentation
<span class="pc bpc" id="L311" title="2 of 3 branches missed.">        val publicMethodRegex = when (language) {</span>
<span class="fc" id="L312">            ProgrammingLanguage.KOTLIN, ProgrammingLanguage.JAVA -&gt; Regex(&quot;(public\\s+)?fun\\s+\\w+&quot;)</span>
<span class="nc" id="L313">            ProgrammingLanguage.PYTHON -&gt; Regex(&quot;def\\s+\\w+&quot;)</span>
<span class="nc" id="L314">            else -&gt; Regex(&quot;function\\s+\\w+&quot;)</span>
        }

<span class="fc" id="L317">        publicMethodRegex.findAll(code).forEach { match -&gt;</span>
<span class="fc" id="L318">            val beforeMethod = code.substring(0, match.range.first)</span>
<span class="fc" id="L319">            val lastLines = beforeMethod.lines().takeLast(3)</span>
<span class="fc" id="L320">            val hasDocumentation = lastLines.any {</span>
<span class="pc bpc" id="L321" title="3 of 6 branches missed.">                it.trim().startsWith(&quot;/**&quot;) || it.trim().startsWith(&quot;\&quot;\&quot;\&quot;&quot;) || it.trim().startsWith(&quot;//&quot;)</span>
            }

<span class="pc bpc" id="L324" title="1 of 2 branches missed.">            if (!hasDocumentation) {</span>
<span class="fc" id="L325">                suggestions.add(Improvement(</span>
<span class="fc" id="L326">                    type = ImprovementType.READABILITY,</span>
<span class="fc" id="L327">                    description = &quot;Consider adding documentation for public methods&quot;,</span>
<span class="fc" id="L328">                    line = findLineNumber(code, match.value),</span>
<span class="fc" id="L329">                    priority = ImprovementPriority.LOW</span>
                ))
            }
<span class="fc" id="L332">        }</span>

        // Check for poor error handling
<span class="pc bpc" id="L335" title="1 of 4 branches missed.">        if (code.contains(&quot;catch (e: Exception)&quot;) &amp;&amp; code.contains(&quot;return \&quot;\&quot;&quot;)) {</span>
<span class="fc" id="L336">            suggestions.add(Improvement(</span>
<span class="fc" id="L337">                type = ImprovementType.SECURITY,</span>
<span class="fc" id="L338">                description = &quot;Improve error handling - avoid returning empty strings on exceptions&quot;,</span>
<span class="fc" id="L339">                line = findLineNumber(code, &quot;catch&quot;),</span>
<span class="fc" id="L340">                priority = ImprovementPriority.MEDIUM</span>
            ))
        }

        // Check for dangerous null assertions
<span class="fc bfc" id="L345" title="All 2 branches covered.">        if (code.contains(&quot;!!&quot;)) {</span>
<span class="fc" id="L346">            suggestions.add(Improvement(</span>
<span class="fc" id="L347">                type = ImprovementType.SECURITY,</span>
<span class="fc" id="L348">                description = &quot;Replace null assertion operator (!!) with safe calls or proper null checking&quot;,</span>
<span class="fc" id="L349">                line = findLineNumber(code, &quot;!!&quot;),</span>
<span class="fc" id="L350">                priority = ImprovementPriority.HIGH</span>
            ))
        }
        
<span class="fc" id="L354">        return suggestions</span>
    }

    private fun findLineNumber(code: String, pattern: String): Int? {
<span class="fc bfc" id="L358" title="All 2 branches covered.">        val regex = if (pattern.startsWith(&quot;\\&quot;)) Regex(pattern) else Regex.escape(pattern).toRegex()</span>
<span class="fc" id="L359">        val lines = code.lines()</span>
<span class="fc" id="L360">        lines.forEachIndexed { index, line -&gt;</span>
<span class="fc bfc" id="L361" title="All 2 branches covered.">            if (regex.containsMatchIn(line)) {</span>
<span class="fc" id="L362">                return index + 1</span>
            }
<span class="fc" id="L364">        }</span>
<span class="fc" id="L365">        return null</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>