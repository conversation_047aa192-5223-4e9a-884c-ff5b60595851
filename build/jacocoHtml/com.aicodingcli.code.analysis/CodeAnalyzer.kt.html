<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CodeAnalyzer.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.code.analysis</a> &gt; <span class="el_source">CodeAnalyzer.kt</span></div><h1>CodeAnalyzer.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.code.analysis

import com.aicodingcli.code.common.ProgrammingLanguage
import com.aicodingcli.code.metrics.MetricsCalculator
import com.aicodingcli.code.quality.QualityAnalyzer
import java.io.File

/**
 * Interface for code analysis functionality
 */
interface CodeAnalyzer {
    /**
     * Analyze a single file
     */
    suspend fun analyzeFile(filePath: String): CodeAnalysisResult

    /**
     * Analyze an entire project
     */
    suspend fun analyzeProject(projectPath: String): ProjectAnalysisResult

    /**
     * Detect issues in code
     */
    suspend fun detectIssues(code: String, language: ProgrammingLanguage): List&lt;CodeIssue&gt;

    /**
     * Suggest improvements for code
     */
    suspend fun suggestImprovements(code: String, language: ProgrammingLanguage): List&lt;Improvement&gt;
}

/**
 * Default implementation of CodeAnalyzer
 */
<span class="fc" id="L36">class DefaultCodeAnalyzer : CodeAnalyzer {</span>

<span class="fc" id="L38">    private val metricsCalculator = MetricsCalculator()</span>
<span class="fc" id="L39">    private val qualityAnalyzer = QualityAnalyzer()</span>

    override suspend fun analyzeFile(filePath: String): CodeAnalysisResult {
<span class="fc" id="L42">        val file = File(filePath)</span>
<span class="fc bfc" id="L43" title="All 2 branches covered.">        if (!file.exists()) {</span>
<span class="fc" id="L44">            throw IllegalArgumentException(&quot;File does not exist: $filePath&quot;)</span>
        }

<span class="fc" id="L47">        val content = file.readText()</span>
<span class="fc" id="L48">        val language = ProgrammingLanguage.fromFilePath(filePath)</span>

<span class="fc" id="L50">        val metrics = metricsCalculator.calculateMetrics(content, language)</span>
<span class="fc" id="L51">        val issues = qualityAnalyzer.detectIssues(content, language)</span>
<span class="fc" id="L52">        val suggestions = qualityAnalyzer.suggestImprovements(content, language)</span>
<span class="fc" id="L53">        val dependencies = extractDependencies(content, language)</span>

<span class="fc" id="L55">        return CodeAnalysisResult(</span>
<span class="fc" id="L56">            filePath = filePath,</span>
<span class="fc" id="L57">            language = language,</span>
<span class="fc" id="L58">            metrics = metrics,</span>
<span class="fc" id="L59">            issues = issues,</span>
<span class="fc" id="L60">            suggestions = suggestions,</span>
<span class="fc" id="L61">            dependencies = dependencies</span>
        )
    }

    override suspend fun analyzeProject(projectPath: String): ProjectAnalysisResult {
<span class="fc" id="L66">        val projectDir = File(projectPath)</span>
<span class="pc bpc" id="L67" title="2 of 4 branches missed.">        if (!projectDir.exists() || !projectDir.isDirectory) {</span>
<span class="nc" id="L68">            throw IllegalArgumentException(&quot;Project directory does not exist: $projectPath&quot;)</span>
        }

<span class="fc" id="L71">        val sourceFiles = findSourceFiles(projectDir)</span>
<span class="fc" id="L72">        val fileResults = sourceFiles.map { analyzeFile(it.absolutePath) }</span>
        
<span class="fc" id="L74">        val overallMetrics = calculateOverallMetrics(fileResults)</span>
<span class="fc" id="L75">        val summary = createSummary(fileResults)</span>

<span class="fc" id="L77">        return ProjectAnalysisResult(</span>
<span class="fc" id="L78">            projectPath = projectPath,</span>
<span class="fc" id="L79">            fileResults = fileResults,</span>
<span class="fc" id="L80">            overallMetrics = overallMetrics,</span>
<span class="fc" id="L81">            summary = summary</span>
        )
    }

    override suspend fun detectIssues(code: String, language: ProgrammingLanguage): List&lt;CodeIssue&gt; {
<span class="fc" id="L86">        return qualityAnalyzer.detectIssues(code, language)</span>
    }

    override suspend fun suggestImprovements(code: String, language: ProgrammingLanguage): List&lt;Improvement&gt; {
<span class="fc" id="L90">        return qualityAnalyzer.suggestImprovements(code, language)</span>
    }



    private fun extractDependencies(code: String, language: ProgrammingLanguage): List&lt;Dependency&gt; {
        // Simplified dependency extraction
<span class="fc" id="L97">        return emptyList()</span>
    }

    private fun findSourceFiles(directory: File): List&lt;File&gt; {
<span class="fc" id="L101">        val supportedExtensions = setOf(&quot;kt&quot;, &quot;java&quot;, &quot;py&quot;, &quot;js&quot;, &quot;ts&quot;)</span>
<span class="fc" id="L102">        return directory.walkTopDown()</span>
<span class="pc bpc" id="L103" title="1 of 4 branches missed.">            .filter { it.isFile &amp;&amp; it.extension in supportedExtensions }</span>
<span class="fc" id="L104">            .toList()</span>
    }

    private fun calculateOverallMetrics(fileResults: List&lt;CodeAnalysisResult&gt;): CodeMetrics {
<span class="pc bpc" id="L108" title="1 of 2 branches missed.">        if (fileResults.isEmpty()) {</span>
<span class="nc" id="L109">            return CodeMetrics(0, 0, 0.0, null, 0)</span>
        }

<span class="fc bfc" id="L112" title="All 2 branches covered.">        val totalLoc = fileResults.sumOf { it.metrics.linesOfCode }</span>
<span class="fc" id="L113">        val avgComplexity = fileResults.map { it.metrics.cyclomaticComplexity.toDouble() }.average().toInt()</span>
<span class="fc" id="L114">        val avgMaintainability = fileResults.map { it.metrics.maintainabilityIndex }.average()</span>

<span class="fc" id="L116">        return CodeMetrics(</span>
<span class="fc" id="L117">            linesOfCode = totalLoc,</span>
<span class="fc" id="L118">            cyclomaticComplexity = avgComplexity,</span>
<span class="fc" id="L119">            maintainabilityIndex = avgMaintainability,</span>
<span class="fc" id="L120">            testCoverage = null,</span>
<span class="fc bfc" id="L121" title="All 2 branches covered.">            duplicatedLines = fileResults.sumOf { it.metrics.duplicatedLines }</span>
        )
    }

    private fun createSummary(fileResults: List&lt;CodeAnalysisResult&gt;): AnalysisSummary {
<span class="fc bfc" id="L126" title="All 2 branches covered.">        val totalIssues = fileResults.sumOf { it.issues.size }</span>
<span class="fc bfc" id="L127" title="All 2 branches covered.">        val criticalIssues = fileResults.sumOf { result -&gt;</span>
<span class="pc bpc" id="L128" title="1 of 2 branches missed.">            result.issues.count { it.severity == IssueSeverity.CRITICAL }</span>
        }
<span class="fc" id="L130">        val avgComplexity = fileResults.map { it.metrics.cyclomaticComplexity.toDouble() }.average()</span>
<span class="fc" id="L131">        val avgMaintainability = fileResults.map { it.metrics.maintainabilityIndex }.average()</span>

<span class="fc" id="L133">        return AnalysisSummary(</span>
<span class="fc" id="L134">            totalFiles = fileResults.size,</span>
<span class="fc" id="L135">            totalIssues = totalIssues,</span>
<span class="fc" id="L136">            criticalIssues = criticalIssues,</span>
<span class="fc" id="L137">            averageComplexity = avgComplexity,</span>
<span class="fc" id="L138">            overallMaintainabilityIndex = avgMaintainability</span>
        )
    }


}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>