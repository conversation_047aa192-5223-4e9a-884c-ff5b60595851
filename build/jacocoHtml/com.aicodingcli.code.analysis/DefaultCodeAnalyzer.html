<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DefaultCodeAnalyzer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.code.analysis</a> &gt; <span class="el_class">DefaultCodeAnalyzer</span></div><h1>DefaultCodeAnalyzer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">17 of 438</td><td class="ctr2">96%</td><td class="bar">5 of 22</td><td class="ctr2">77%</td><td class="ctr1">5</td><td class="ctr2">21</td><td class="ctr1">2</td><td class="ctr2">60</td><td class="ctr1">0</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a2"><a href="CodeAnalyzer.kt.html#L108" class="el_method">calculateOverallMetrics(List)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="111" height="10" title="116" alt="116"/></td><td class="ctr2" id="c8">92%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="5" alt="5"/></td><td class="ctr2" id="e1">83%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="CodeAnalyzer.kt.html#L66" class="el_method">analyzeProject(String, Continuation)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="65" height="10" title="68" alt="68"/></td><td class="ctr2" id="c9">91%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="CodeAnalyzer.kt.html#L126" class="el_method">createSummary(List)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="102" height="10" title="107" alt="107"/></td><td class="ctr2" id="c7">98%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="5" alt="5"/></td><td class="ctr2" id="e2">83%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a0"><a href="CodeAnalyzer.kt.html#L42" class="el_method">analyzeFile(String, Continuation)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="54" height="10" title="57" alt="57"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i0">16</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="CodeAnalyzer.kt.html#L101" class="el_method">findSourceFiles(File)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="34" alt="34"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">4</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="CodeAnalyzer.kt.html#L36" class="el_method">DefaultCodeAnalyzer()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="13" alt="13"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="CodeAnalyzer.kt.html#L103" class="el_method">findSourceFiles$lambda$1(Set, File)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="12" alt="12"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="3" alt="3"/></td><td class="ctr2" id="e3">75%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="CodeAnalyzer.kt.html#L86" class="el_method">detectIssues(String, ProgrammingLanguage, Continuation)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a9"><a href="CodeAnalyzer.kt.html#L90" class="el_method">suggestImprovements(String, ProgrammingLanguage, Continuation)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a6"><a href="CodeAnalyzer.kt.html#L97" class="el_method">extractDependencies(String, ProgrammingLanguage)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>