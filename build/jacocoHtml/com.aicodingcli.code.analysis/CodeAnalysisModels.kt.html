<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CodeAnalysisModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.code.analysis</a> &gt; <span class="el_source">CodeAnalysisModels.kt</span></div><h1>CodeAnalysisModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.code.analysis

import com.aicodingcli.code.common.ProgrammingLanguage

/**
 * Code metrics for analysis results
 */
<span class="fc" id="L8">data class CodeMetrics(</span>
<span class="fc" id="L9">    val linesOfCode: Int,</span>
<span class="fc" id="L10">    val cyclomaticComplexity: Int,</span>
<span class="fc" id="L11">    val maintainabilityIndex: Double,</span>
<span class="fc" id="L12">    val testCoverage: Double?,</span>
<span class="fc" id="L13">    val duplicatedLines: Int</span>
)

/**
 * Types of code issues
 */
enum class IssueType {
<span class="fc" id="L20">    SYNTAX_ERROR,</span>
<span class="fc" id="L21">    LOGIC_ERROR,</span>
<span class="fc" id="L22">    PERFORMANCE,</span>
<span class="fc" id="L23">    SECURITY,</span>
<span class="fc" id="L24">    CODE_SMELL,</span>
<span class="fc" id="L25">    NAMING_CONVENTION,</span>
<span class="fc" id="L26">    UNUSED_CODE</span>
}

/**
 * Severity levels for issues
 */
enum class IssueSeverity {
<span class="fc" id="L33">    LOW,</span>
<span class="fc" id="L34">    MEDIUM,</span>
<span class="fc" id="L35">    HIGH,</span>
<span class="fc" id="L36">    CRITICAL</span>
}

/**
 * Code issue detected during analysis
 */
<span class="fc" id="L42">data class CodeIssue(</span>
<span class="fc" id="L43">    val type: IssueType,</span>
<span class="fc" id="L44">    val severity: IssueSeverity,</span>
<span class="fc" id="L45">    val message: String,</span>
<span class="fc" id="L46">    val line: Int?,</span>
<span class="fc" id="L47">    val column: Int?,</span>
<span class="fc" id="L48">    val suggestion: String?</span>
)

/**
 * Types of improvements
 */
enum class ImprovementType {
<span class="fc" id="L55">    PERFORMANCE,</span>
<span class="fc" id="L56">    MAINTAINABILITY,</span>
<span class="fc" id="L57">    READABILITY,</span>
<span class="fc" id="L58">    SECURITY,</span>
<span class="fc" id="L59">    TESTING</span>
}

/**
 * Priority levels for improvements
 */
enum class ImprovementPriority {
<span class="fc" id="L66">    LOW,</span>
<span class="fc" id="L67">    MEDIUM,</span>
<span class="fc" id="L68">    HIGH</span>
}

/**
 * Improvement suggestion
 */
<span class="fc" id="L74">data class Improvement(</span>
<span class="fc" id="L75">    val type: ImprovementType,</span>
<span class="fc" id="L76">    val description: String,</span>
<span class="fc" id="L77">    val line: Int?,</span>
<span class="fc" id="L78">    val priority: ImprovementPriority = ImprovementPriority.MEDIUM</span>
<span class="fc" id="L79">)</span>

/**
 * Types of dependencies
 */
enum class DependencyType {
<span class="fc" id="L85">    INTERNAL,</span>
<span class="fc" id="L86">    EXTERNAL,</span>
<span class="fc" id="L87">    SYSTEM</span>
}

/**
 * Dependency scopes
 */
enum class DependencyScope {
<span class="fc" id="L94">    COMPILE,</span>
<span class="fc" id="L95">    RUNTIME,</span>
<span class="fc" id="L96">    TEST,</span>
<span class="fc" id="L97">    PROVIDED</span>
}

/**
 * Dependency information
 */
<span class="fc" id="L103">data class Dependency(</span>
<span class="fc" id="L104">    val name: String,</span>
<span class="fc" id="L105">    val version: String?,</span>
<span class="fc" id="L106">    val type: DependencyType,</span>
<span class="fc" id="L107">    val scope: DependencyScope</span>
)

/**
 * Complete code analysis result for a single file
 */
<span class="fc" id="L113">data class CodeAnalysisResult(</span>
<span class="fc" id="L114">    val filePath: String,</span>
<span class="fc" id="L115">    val language: ProgrammingLanguage,</span>
<span class="fc" id="L116">    val metrics: CodeMetrics,</span>
<span class="fc" id="L117">    val issues: List&lt;CodeIssue&gt;,</span>
<span class="fc" id="L118">    val suggestions: List&lt;Improvement&gt;,</span>
<span class="fc" id="L119">    val dependencies: List&lt;Dependency&gt;</span>
)

/**
 * Project-level analysis result
 */
<span class="fc" id="L125">data class ProjectAnalysisResult(</span>
<span class="fc" id="L126">    val projectPath: String,</span>
<span class="fc" id="L127">    val fileResults: List&lt;CodeAnalysisResult&gt;,</span>
<span class="fc" id="L128">    val overallMetrics: CodeMetrics,</span>
<span class="fc" id="L129">    val summary: AnalysisSummary</span>
)

/**
 * Summary of analysis results
 */
<span class="fc" id="L135">data class AnalysisSummary(</span>
<span class="fc" id="L136">    val totalFiles: Int,</span>
<span class="fc" id="L137">    val totalIssues: Int,</span>
<span class="fc" id="L138">    val criticalIssues: Int,</span>
<span class="fc" id="L139">    val averageComplexity: Double,</span>
<span class="fc" id="L140">    val overallMaintainabilityIndex: Double</span>
)
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>