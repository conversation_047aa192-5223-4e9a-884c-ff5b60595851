<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ProgrammingLanguage.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.code.common</a> &gt; <span class="el_source">ProgrammingLanguage.kt</span></div><h1>ProgrammingLanguage.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.code.common

/**
 * Supported programming languages for code analysis
 */
<span class="pc" id="L6">enum class ProgrammingLanguage(</span>
<span class="fc" id="L7">    val displayName: String,</span>
<span class="fc" id="L8">    val fileExtension: String,</span>
<span class="fc" id="L9">    val supportsClasses: Boolean,</span>
<span class="fc" id="L10">    val supportsInterfaces: Boolean,</span>
<span class="pc" id="L11">    val supportsPackages: Boolean = true</span>
<span class="nc" id="L12">) {</span>
<span class="fc" id="L13">    KOTLIN(&quot;Kotlin&quot;, &quot;kt&quot;, true, true, true),</span>
<span class="fc" id="L14">    JAVA(&quot;Java&quot;, &quot;java&quot;, true, true, true),</span>
<span class="fc" id="L15">    PYTHON(&quot;Python&quot;, &quot;py&quot;, true, false, true),</span>
<span class="fc" id="L16">    JAVASCRIPT(&quot;JavaScript&quot;, &quot;js&quot;, false, false, false),</span>
<span class="fc" id="L17">    TYPESCRIPT(&quot;TypeScript&quot;, &quot;ts&quot;, true, true, true);</span>

    companion object {
        /**
         * Get programming language from file extension
         */
        fun fromFileExtension(extension: String): ProgrammingLanguage {
<span class="fc bfc" id="L24" title="All 6 branches covered.">            return values().find { it.fileExtension == extension }</span>
<span class="fc" id="L25">                ?: throw IllegalArgumentException(&quot;Unsupported file extension: $extension&quot;)</span>
        }

        /**
         * Get programming language from file path
         */
        fun fromFilePath(filePath: String): ProgrammingLanguage {
<span class="fc" id="L32">            val extension = filePath.substringAfterLast('.', &quot;&quot;)</span>
<span class="fc" id="L33">            return fromFileExtension(extension)</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>