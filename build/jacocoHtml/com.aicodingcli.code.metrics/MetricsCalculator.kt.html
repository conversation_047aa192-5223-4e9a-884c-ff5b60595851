<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MetricsCalculator.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.code.metrics</a> &gt; <span class="el_source">MetricsCalculator.kt</span></div><h1>MetricsCalculator.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.code.metrics

import com.aicodingcli.code.analysis.CodeMetrics
import com.aicodingcli.code.common.ProgrammingLanguage

/**
 * Calculator for code metrics
 */
<span class="fc" id="L9">class MetricsCalculator {</span>

    /**
     * Calculate metrics for given code
     */
    fun calculateMetrics(code: String, language: ProgrammingLanguage): CodeMetrics {
<span class="fc" id="L15">        val lines = code.lines()</span>
<span class="fc" id="L16">        val linesOfCode = calculateLinesOfCode(lines)</span>
<span class="fc" id="L17">        val cyclomaticComplexity = calculateCyclomaticComplexity(code)</span>
<span class="fc" id="L18">        val maintainabilityIndex = calculateMaintainabilityIndex(cyclomaticComplexity, linesOfCode)</span>
<span class="fc" id="L19">        val duplicatedLines = calculateDuplicatedLines(code)</span>

<span class="fc" id="L21">        return CodeMetrics(</span>
<span class="fc" id="L22">            linesOfCode = linesOfCode,</span>
<span class="fc" id="L23">            cyclomaticComplexity = cyclomaticComplexity,</span>
<span class="fc" id="L24">            maintainabilityIndex = maintainabilityIndex,</span>
<span class="fc" id="L25">            testCoverage = null, // Would need external tool integration</span>
<span class="fc" id="L26">            duplicatedLines = duplicatedLines</span>
        )
    }

    private fun calculateLinesOfCode(lines: List&lt;String&gt;): Int {
<span class="fc" id="L31">        var inBlockComment = false</span>
<span class="fc" id="L32">        var count = 0</span>

<span class="fc bfc" id="L34" title="All 2 branches covered.">        for (line in lines) {</span>
<span class="fc" id="L35">            val trimmed = line.trim()</span>

            // Skip empty lines
<span class="fc bfc" id="L38" title="All 4 branches covered.">            if (trimmed.isEmpty()) continue</span>

            // Handle block comments
<span class="fc bfc" id="L41" title="All 2 branches covered.">            if (trimmed.startsWith(&quot;/*&quot;)) {</span>
<span class="fc" id="L42">                inBlockComment = true</span>
                // Check if comment ends on the same line
<span class="fc bfc" id="L44" title="All 2 branches covered.">                if (trimmed.contains(&quot;*/&quot;)) {</span>
<span class="fc" id="L45">                    inBlockComment = false</span>
                    // Check if there's code after the comment
<span class="fc" id="L47">                    val afterComment = trimmed.substringAfter(&quot;*/&quot;).trim()</span>
<span class="pc bpc" id="L48" title="2 of 4 branches missed.">                    if (afterComment.isNotEmpty()) {</span>
<span class="nc" id="L49">                        count++</span>
                    }
                }
<span class="fc" id="L52">                continue</span>
            }

<span class="fc bfc" id="L55" title="All 2 branches covered.">            if (inBlockComment) {</span>
<span class="fc bfc" id="L56" title="All 2 branches covered.">                if (trimmed.contains(&quot;*/&quot;)) {</span>
<span class="fc" id="L57">                    inBlockComment = false</span>
                    // Check if there's code after the comment
<span class="fc" id="L59">                    val afterComment = trimmed.substringAfter(&quot;*/&quot;).trim()</span>
<span class="pc bpc" id="L60" title="2 of 4 branches missed.">                    if (afterComment.isNotEmpty()) {</span>
<span class="nc" id="L61">                        count++</span>
                    }
                }
<span class="fc" id="L64">                continue</span>
            }

            // Skip single line comments
<span class="fc bfc" id="L68" title="All 2 branches covered.">            if (trimmed.startsWith(&quot;//&quot;)) continue</span>

            // Skip lines that are only block comment continuation
<span class="pc bpc" id="L71" title="1 of 2 branches missed.">            if (trimmed.startsWith(&quot;*&quot;)) continue</span>

            // Check if line has code before inline comment
<span class="fc bfc" id="L74" title="All 2 branches covered.">            val beforeComment = if (trimmed.contains(&quot;//&quot;)) {</span>
<span class="fc" id="L75">                trimmed.substringBefore(&quot;//&quot;).trim()</span>
            } else {
<span class="fc" id="L77">                trimmed</span>
            }

<span class="pc bpc" id="L80" title="2 of 4 branches missed.">            if (beforeComment.isNotEmpty()) {</span>
<span class="fc" id="L81">                count++</span>
            }
        }

<span class="fc" id="L85">        return count</span>
    }

    private fun calculateCyclomaticComplexity(code: String): Int {
<span class="fc" id="L89">        var complexity = 1 // Base complexity</span>

        // Remove comments and strings to avoid false positives
<span class="fc" id="L92">        val cleanCode = removeCommentsAndStrings(code)</span>

        // Count decision points more accurately
<span class="fc" id="L95">        complexity += countDecisionPoints(cleanCode)</span>

<span class="fc" id="L97">        return maxOf(complexity, 1)</span>
    }

    private fun countDecisionPoints(cleanCode: String): Int {
<span class="fc" id="L101">        var points = 0</span>

        // Count if statements (but not else if as separate)
<span class="fc" id="L104">        points += countPattern(cleanCode, &quot;\\bif\\s*\\(&quot;)</span>

        // Count while loops
<span class="fc" id="L107">        points += countPattern(cleanCode, &quot;\\bwhile\\s*\\(&quot;)</span>

        // Count for loops
<span class="fc" id="L110">        points += countPattern(cleanCode, &quot;\\bfor\\s*\\(&quot;)</span>

        // Count when expressions by counting -&gt; arrows, excluding function declarations and lambdas
<span class="fc" id="L113">        val whenArrows = countPattern(cleanCode, &quot;-&gt;&quot;)</span>
<span class="fc" id="L114">        val functionDeclarations = countPattern(cleanCode, &quot;\\bfun\\s+\\w+&quot;)</span>
<span class="fc" id="L115">        val lambdas = countPattern(cleanCode, &quot;\\{.*-&gt;&quot;)</span>
<span class="fc" id="L116">        points += maxOf(0, whenArrows - functionDeclarations - lambdas)</span>

        // Count catch blocks
<span class="fc" id="L119">        points += countPattern(cleanCode, &quot;\\bcatch\\s*\\(&quot;)</span>

        // Count logical operators (but be careful not to double count)
<span class="fc" id="L122">        points += countLogicalOperators(cleanCode)</span>

<span class="fc" id="L124">        return points</span>
    }

    private fun countLogicalOperators(code: String): Int {
        // Count &amp;&amp; and || but avoid counting them multiple times in the same expression
<span class="fc" id="L129">        val lines = code.lines()</span>
<span class="fc" id="L130">        var count = 0</span>

<span class="fc bfc" id="L132" title="All 2 branches covered.">        for (line in lines) {</span>
<span class="fc" id="L133">            val trimmed = line.trim()</span>
<span class="pc bpc" id="L134" title="2 of 4 branches missed.">            if (trimmed.contains(&quot;&amp;&amp;&quot;) || trimmed.contains(&quot;||&quot;)) {</span>
                // Count each line with logical operators as one additional complexity point
<span class="nc" id="L136">                count += 1</span>
            }
        }

<span class="fc" id="L140">        return count</span>
    }

    private fun countPattern(text: String, pattern: String): Int {
<span class="fc" id="L144">        return Regex(pattern).findAll(text).count()</span>
    }

    private fun removeCommentsAndStrings(code: String): String {
<span class="fc" id="L148">        var result = code</span>

        // Remove single line comments
<span class="fc" id="L151">        result = result.replace(Regex(&quot;//.*&quot;), &quot;&quot;)</span>

        // Remove block comments (simplified)
<span class="fc" id="L154">        result = result.replace(Regex(&quot;/\\*.*?\\*/&quot;, RegexOption.DOT_MATCHES_ALL), &quot;&quot;)</span>

        // Remove string literals (simplified)
<span class="fc" id="L157">        result = result.replace(Regex(&quot;\&quot;.*?\&quot;&quot;), &quot;\&quot;\&quot;&quot;)</span>
<span class="fc" id="L158">        result = result.replace(Regex(&quot;'.*?'&quot;), &quot;''&quot;)</span>

<span class="fc" id="L160">        return result</span>
    }

    private fun calculateMaintainabilityIndex(complexity: Int, linesOfCode: Int): Double {
        // Simplified maintainability index calculation
        // Real formula: MI = 171 - 5.2 * ln(Halstead Volume) - 0.23 * (Cyclomatic Complexity) - 16.2 * ln(Lines of Code)
        
<span class="fc" id="L167">        val complexityPenalty = complexity * 2.0</span>
<span class="fc" id="L168">        val sizePenalty = kotlin.math.ln(linesOfCode.toDouble()) * 5.0</span>
        
<span class="fc" id="L170">        val baseScore = 100.0</span>
<span class="fc" id="L171">        val maintainabilityIndex = baseScore - complexityPenalty - sizePenalty</span>
        
<span class="fc" id="L173">        return maxOf(maintainabilityIndex, 0.0).coerceAtMost(100.0)</span>
    }

    private fun calculateDuplicatedLines(code: String): Int {
        // More accurate duplicate detection
<span class="fc" id="L178">        val lines = code.lines()</span>
<span class="fc" id="L179">            .map { line -&gt;</span>
                // Normalize the line: remove leading/trailing whitespace and comments
<span class="fc" id="L181">                val trimmed = line.trim()</span>
<span class="fc" id="L182">                when {</span>
<span class="fc bfc" id="L183" title="All 4 branches covered.">                    trimmed.isEmpty() -&gt; null</span>
<span class="fc bfc" id="L184" title="All 2 branches covered.">                    trimmed.startsWith(&quot;//&quot;) -&gt; null</span>
<span class="fc bfc" id="L185" title="All 2 branches covered.">                    trimmed.startsWith(&quot;/*&quot;) -&gt; null</span>
<span class="fc bfc" id="L186" title="All 2 branches covered.">                    trimmed.startsWith(&quot;*&quot;) -&gt; null</span>
<span class="fc bfc" id="L187" title="All 2 branches covered.">                    trimmed.contains(&quot;//&quot;) -&gt; trimmed.substringBefore(&quot;//&quot;).trim()</span>
<span class="fc" id="L188">                    else -&gt; trimmed</span>
<span class="fc" id="L189">                }</span>
            }
<span class="fc" id="L191">            .filterNotNull()</span>
<span class="pc bpc" id="L192" title="1 of 2 branches missed.">            .filter { it.isNotEmpty() }</span>

        // Group identical lines and count duplicates
<span class="fc" id="L195">        val lineGroups = lines.groupBy { it }</span>
<span class="fc" id="L196">        var duplicateCount = 0</span>

<span class="fc bfc" id="L198" title="All 2 branches covered.">        for ((line, occurrences) in lineGroups) {</span>
<span class="fc bfc" id="L199" title="All 2 branches covered.">            if (occurrences.size &gt; 1) {</span>
                // Only count meaningful duplicates (not single characters or braces)
<span class="pc bpc" id="L201" title="1 of 4 branches missed.">                if (line.length &gt; 3 &amp;&amp; !line.matches(Regex(&quot;[{}();,]*&quot;))) {</span>
<span class="fc" id="L202">                    duplicateCount += occurrences.size - 1</span>
                }
            }
        }

<span class="fc" id="L207">        return duplicateCount</span>
    }


}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>