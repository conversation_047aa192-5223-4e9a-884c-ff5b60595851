<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MetricsCalculator</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.code.metrics</a> &gt; <span class="el_class">MetricsCalculator</span></div><h1>MetricsCalculator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">10 of 549</td><td class="ctr2">98%</td><td class="bar">11 of 60</td><td class="ctr2">81%</td><td class="ctr1">11</td><td class="ctr2">40</td><td class="ctr1">3</td><td class="ctr2">94</td><td class="ctr1">0</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a2"><a href="MetricsCalculator.kt.html#L31" class="el_method">calculateLinesOfCode(List)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="114" height="10" title="140" alt="140"/></td><td class="ctr2" id="c9">95%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="93" height="10" title="25" alt="25"/></td><td class="ctr2" id="e1">78%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">17</td><td class="ctr1" id="h0">2</td><td class="ctr2" id="i0">28</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="MetricsCalculator.kt.html#L129" class="el_method">countLogicalOperators(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="41" alt="41"/></td><td class="ctr2" id="c8">95%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">66%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="MetricsCalculator.kt.html#L178" class="el_method">calculateDuplicatedLines(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="117" height="10" title="144" alt="144"/></td><td class="ctr2" id="c7">99%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="75" height="10" title="20" alt="20"/></td><td class="ctr2" id="e0">90%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g1">12</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i1">20</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="MetricsCalculator.kt.html#L148" class="el_method">removeCommentsAndStrings(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="65" alt="65"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="MetricsCalculator.kt.html#L101" class="el_method">countDecisionPoints(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="51" height="10" title="63" alt="63"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="MetricsCalculator.kt.html#L15" class="el_method">calculateMetrics(String, ProgrammingLanguage)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="30" alt="30"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="MetricsCalculator.kt.html#L167" class="el_method">calculateMaintainabilityIndex(int, int)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="25" alt="25"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="MetricsCalculator.kt.html#L89" class="el_method">calculateCyclomaticComplexity(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="16" alt="16"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="MetricsCalculator.kt.html#L144" class="el_method">countPattern(String, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="12" alt="12"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="MetricsCalculator.kt.html#L9" class="el_method">MetricsCalculator()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>