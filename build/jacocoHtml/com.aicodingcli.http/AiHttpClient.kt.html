<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiHttpClient.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.http</a> &gt; <span class="el_source">AiHttpClient.kt</span></div><h1>AiHttpClient.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.http

import io.ktor.client.*
import io.ktor.client.engine.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.utils.io.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json

/**
 * HTTP client wrapper for AI service calls
 */
<span class="fc" id="L22">class AiHttpClient(</span>
<span class="fc" id="L23">    private val engine: HttpClientEngine? = null,</span>
<span class="fc" id="L24">    private val timeoutMs: Long = 30000,</span>
<span class="fc" id="L25">    private val retryConfig: RetryConfig = RetryConfig()</span>
<span class="fc" id="L26">) {</span>
    
<span class="fc bfc" id="L28" title="All 2 branches covered.">    private val client = HttpClient(engine ?: CIO.create()) {</span>
<span class="fc" id="L29">        install(ContentNegotiation) {</span>
<span class="fc" id="L30">            json(Json {</span>
<span class="fc" id="L31">                ignoreUnknownKeys = true</span>
<span class="fc" id="L32">                isLenient = true</span>
<span class="fc" id="L33">            })</span>
<span class="fc" id="L34">        }</span>

<span class="fc" id="L36">        install(Logging) {</span>
<span class="fc" id="L37">            logger = Logger.DEFAULT</span>
<span class="fc" id="L38">            level = LogLevel.INFO</span>
<span class="fc" id="L39">        }</span>

<span class="fc" id="L41">        install(HttpTimeout) {</span>
<span class="fc" id="L42">            requestTimeoutMillis = timeoutMs</span>
<span class="fc" id="L43">            connectTimeoutMillis = timeoutMs</span>
<span class="fc" id="L44">            socketTimeoutMillis = timeoutMs</span>
<span class="fc" id="L45">        }</span>

<span class="fc" id="L47">        install(HttpRequestRetry) {</span>
<span class="fc" id="L48">            retryOnServerErrors(maxRetries = 0) // We handle retries manually</span>
<span class="fc" id="L49">            retryOnException(maxRetries = 0, retryOnTimeout = false)</span>
<span class="fc" id="L50">        }</span>

<span class="fc" id="L52">        defaultRequest {</span>
<span class="fc" id="L53">            headers.append(HttpHeaders.UserAgent, &quot;AiCodingCli/1.0&quot;)</span>
<span class="fc" id="L54">        }</span>
<span class="fc" id="L55">    }</span>

    /**
     * Make GET request
     */
<span class="fc" id="L60">    suspend fun get(</span>
        url: String,
<span class="fc" id="L62">        headers: Map&lt;String, String&gt; = emptyMap()</span>
    ): HttpResponse {
<span class="fc" id="L64">        return executeWithRetry {</span>
<span class="fc" id="L65">            val response = client.get(url) {</span>
<span class="fc" id="L66">                headers.forEach { (key, value) -&gt;</span>
<span class="fc" id="L67">                    header(key, value)</span>
<span class="fc" id="L68">                }</span>
<span class="fc" id="L69">            }</span>
<span class="fc" id="L70">            handleResponse(response)</span>
        }
    }

    /**
     * Make POST request
     */
<span class="nc" id="L77">    suspend fun post(</span>
        url: String,
        body: String,
<span class="nc" id="L80">        headers: Map&lt;String, String&gt; = emptyMap()</span>
    ): HttpResponse {
<span class="fc" id="L82">        return executeWithRetry {</span>
<span class="fc" id="L83">            val response = client.post(url) {</span>
<span class="fc" id="L84">                headers.forEach { (key, value) -&gt;</span>
<span class="fc" id="L85">                    header(key, value)</span>
<span class="fc" id="L86">                }</span>
<span class="fc" id="L87">                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())</span>
<span class="fc" id="L88">                setBody(body)</span>
<span class="fc" id="L89">            }</span>
<span class="fc" id="L90">            handleResponse(response)</span>
        }
    }

    /**
     * Make streaming POST request for Server-Sent Events or JSONL
     */
<span class="nc" id="L97">    suspend fun postStream(</span>
        url: String,
        body: String,
<span class="nc" id="L100">        headers: Map&lt;String, String&gt; = emptyMap()</span>
    ): Flow&lt;String&gt; {
<span class="pc" id="L102">        return flow {</span>
<span class="fc" id="L103">            val response = client.post(url) {</span>
<span class="fc" id="L104">                headers.forEach { (key, value) -&gt;</span>
<span class="fc" id="L105">                    header(key, value)</span>
<span class="fc" id="L106">                }</span>
<span class="fc" id="L107">                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())</span>
<span class="fc" id="L108">                setBody(body)</span>
<span class="fc" id="L109">            }</span>

<span class="pc bpc" id="L111" title="1 of 2 branches missed.">            if (!response.status.isSuccess()) {</span>
<span class="nc" id="L112">                val errorBody = response.bodyAsText()</span>
<span class="nc" id="L113">                throw HttpException(response.status, errorBody)</span>
            }

<span class="fc" id="L116">            val channel = response.bodyAsChannel()</span>

<span class="fc bfc" id="L118" title="All 2 branches covered.">            while (!channel.isClosedForRead) {</span>
<span class="fc" id="L119">                val chunk = channel.readUTF8Line()</span>
<span class="pc bpc" id="L120" title="1 of 2 branches missed.">                if (chunk != null) {</span>
<span class="pc bpc" id="L121" title="1 of 2 branches missed.">                    if (chunk.startsWith(&quot;data: &quot;)) {</span>
                        // Server-Sent Events format (OpenAI, Claude)
<span class="nc" id="L123">                        val data = chunk.substring(6)</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">                        if (data == &quot;[DONE]&quot;) {</span>
<span class="nc" id="L125">                            break</span>
                        }
<span class="nc" id="L127">                        emit(data)</span>
<span class="pc bpc" id="L128" title="2 of 4 branches missed.">                    } else if (chunk.trim().isNotEmpty()) {</span>
                        // JSONL format (Ollama)
<span class="fc" id="L130">                        emit(chunk.trim())</span>
                    }
                }
            }
<span class="fc" id="L134">        }</span>
    }

    /**
     * Make PUT request
     */
<span class="nc" id="L140">    suspend fun put(</span>
        url: String,
        body: String,
<span class="nc" id="L143">        headers: Map&lt;String, String&gt; = emptyMap()</span>
    ): HttpResponse {
<span class="nc" id="L145">        return executeWithRetry {</span>
<span class="nc" id="L146">            val response = client.put(url) {</span>
<span class="nc" id="L147">                headers.forEach { (key, value) -&gt;</span>
<span class="nc" id="L148">                    header(key, value)</span>
<span class="nc" id="L149">                }</span>
<span class="nc" id="L150">                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())</span>
<span class="nc" id="L151">                setBody(body)</span>
<span class="nc" id="L152">            }</span>
<span class="nc" id="L153">            handleResponse(response)</span>
        }
    }

    /**
     * Make DELETE request
     */
<span class="nc" id="L160">    suspend fun delete(</span>
        url: String,
<span class="nc" id="L162">        headers: Map&lt;String, String&gt; = emptyMap()</span>
    ): HttpResponse {
<span class="nc" id="L164">        return executeWithRetry {</span>
<span class="nc" id="L165">            val response = client.delete(url) {</span>
<span class="nc" id="L166">                headers.forEach { (key, value) -&gt;</span>
<span class="nc" id="L167">                    header(key, value)</span>
<span class="nc" id="L168">                }</span>
<span class="nc" id="L169">            }</span>
<span class="nc" id="L170">            handleResponse(response)</span>
        }
    }

    /**
     * Execute request with retry logic
     */
    private suspend fun &lt;T&gt; executeWithRetry(request: suspend () -&gt; T): T {
<span class="fc" id="L178">        var lastException: Exception? = null</span>
<span class="fc" id="L179">        var currentDelay = retryConfig.delayMs</span>
        
<span class="fc bfc" id="L181" title="All 2 branches covered.">        repeat(retryConfig.maxRetries + 1) { attempt -&gt;</span>
<span class="fc" id="L182">            try {</span>
<span class="fc" id="L183">                return request()</span>
<span class="fc" id="L184">            } catch (e: HttpException) {</span>
                // Don't retry on client errors (4xx), only on server errors (5xx) and network issues
<span class="pc bpc" id="L186" title="3 of 8 branches missed.">                if (e.statusCode.value in 400..499 &amp;&amp; e.statusCode != HttpStatusCode.TooManyRequests) {</span>
<span class="fc" id="L187">                    throw e</span>
                }
<span class="fc" id="L189">                lastException = e</span>
                
                // Handle rate limiting with Retry-After header
<span class="pc bpc" id="L192" title="1 of 2 branches missed.">                if (e.statusCode == HttpStatusCode.TooManyRequests) {</span>
                    // In a real implementation, we would parse the Retry-After header
                    // For now, we'll use the configured delay
                }
<span class="fc" id="L196">            } catch (e: Exception) {</span>
<span class="fc" id="L197">                lastException = e</span>
            }
            
            // Don't delay after the last attempt
<span class="fc bfc" id="L201" title="All 2 branches covered.">            if (attempt &lt; retryConfig.maxRetries) {</span>
<span class="fc" id="L202">                delay(currentDelay)</span>
<span class="fc" id="L203">                currentDelay = minOf(</span>
<span class="fc" id="L204">                    (currentDelay * retryConfig.backoffMultiplier).toLong(),</span>
<span class="fc" id="L205">                    retryConfig.maxDelayMs</span>
                )
            }
<span class="fc" id="L208">        }</span>
        
<span class="pc bpc" id="L210" title="1 of 2 branches missed.">        throw lastException ?: Exception(&quot;Request failed after ${retryConfig.maxRetries} retries&quot;)</span>
    }

    /**
     * Handle HTTP response and convert to our wrapper
     */
<span class="fc" id="L216">    private suspend fun handleResponse(response: io.ktor.client.statement.HttpResponse): HttpResponse {</span>
<span class="fc" id="L217">        val body = response.bodyAsText()</span>
<span class="fc" id="L218">        val headers = mutableMapOf&lt;String, String&gt;()</span>
<span class="fc" id="L219">        response.headers.forEach { key, values -&gt;</span>
<span class="pc bpc" id="L220" title="1 of 2 branches missed.">            headers[key] = values.firstOrNull() ?: &quot;&quot;</span>
<span class="fc" id="L221">        }</span>

<span class="fc" id="L223">        val httpResponse = HttpResponse(</span>
<span class="fc" id="L224">            status = response.status,</span>
<span class="fc" id="L225">            body = body,</span>
<span class="fc" id="L226">            headers = headers</span>
        )

        // Throw exception for error status codes
<span class="fc bfc" id="L230" title="All 2 branches covered.">        if (!response.status.isSuccess()) {</span>
<span class="fc" id="L231">            throw HttpException(response.status, body)</span>
        }

<span class="fc" id="L234">        return httpResponse</span>
    }

    /**
     * Close the HTTP client
     */
    fun close() {
<span class="nc" id="L241">        client.close()</span>
<span class="nc" id="L242">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>