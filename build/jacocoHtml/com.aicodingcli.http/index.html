<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.http</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.http</span></div><h1>com.aicodingcli.http</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">362 of 1,134</td><td class="ctr2">68%</td><td class="bar">25 of 56</td><td class="ctr2">55%</td><td class="ctr1">37</td><td class="ctr2">76</td><td class="ctr1">41</td><td class="ctr2">158</td><td class="ctr1">15</td><td class="ctr2">48</td><td class="ctr1">3</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a0"><a href="AiHttpClient.html" class="el_class">AiHttpClient</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="90" alt="90"/><img src="../jacoco-resources/greenbar.gif" width="98" height="10" title="407" alt="407"/></td><td class="ctr2" id="c4">81%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="87" height="10" title="16" alt="16"/></td><td class="ctr2" id="e0">72%</td><td class="ctr1" id="f0">13</td><td class="ctr2" id="g0">34</td><td class="ctr1" id="h0">12</td><td class="ctr2" id="i0">78</td><td class="ctr1" id="j0">7</td><td class="ctr2" id="k0">23</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a8"><a href="RequestConfig.html" class="el_class">RequestConfig</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="81" alt="81"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h1">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j1">6</td><td class="ctr2" id="k1">6</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a5"><a href="AiHttpClient$put$2.html" class="el_class">AiHttpClient.put.new Function1() {...}</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="58" alt="58"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h2">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k5">1</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="AiHttpClient$postStream$2.html" class="el_class">AiHttpClient.postStream.new Function2() {...}</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="54" alt="54"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="126" alt="126"/></td><td class="ctr2" id="c6">70%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="7" alt="7"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h3">6</td><td class="ctr2" id="i1">23</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k6">1</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a1"><a href="AiHttpClient$delete$2.html" class="el_class">AiHttpClient.delete.new Function1() {...}</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="43" alt="43"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k7">1</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a9"><a href="RetryConfig.html" class="el_class">RetryConfig</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="36" alt="36"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="86" alt="86"/></td><td class="ctr2" id="c5">70%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g1">14</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k2">6</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a3"><a href="AiHttpClient$post$2.html" class="el_class">AiHttpClient.post.new Function1() {...}</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="58" alt="58"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k8">1</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a2"><a href="AiHttpClient$get$2.html" class="el_class">AiHttpClient.get.new Function1() {...}</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="43" alt="43"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a6"><a href="HttpException.html" class="el_class">HttpException</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="31" alt="31"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k3">4</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a7"><a href="HttpResponse.html" class="el_class">HttpResponse</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="21" alt="21"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k4">4</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>