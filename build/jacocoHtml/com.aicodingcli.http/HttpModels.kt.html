<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>HttpModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.http</a> &gt; <span class="el_source">HttpModels.kt</span></div><h1>HttpModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.http

import io.ktor.http.*

/**
 * HTTP response wrapper
 */
<span class="fc" id="L8">data class HttpResponse(</span>
<span class="fc" id="L9">    val status: HttpStatusCode,</span>
<span class="fc" id="L10">    val body: String,</span>
<span class="fc" id="L11">    val headers: Map&lt;String, String&gt;</span>
)

/**
 * HTTP exception for error responses
 */
<span class="fc" id="L17">class HttpException(</span>
<span class="fc" id="L18">    val statusCode: HttpStatusCode,</span>
<span class="fc" id="L19">    val responseBody: String,</span>
<span class="fc" id="L20">    message: String = &quot;HTTP error ${statusCode.value}: ${statusCode.description}&quot;</span>
<span class="fc" id="L21">) : Exception(message)</span>

/**
 * Retry configuration for HTTP requests
 */
<span class="fc" id="L26">data class RetryConfig(</span>
<span class="fc" id="L27">    val maxRetries: Int = 3,</span>
<span class="fc" id="L28">    val delayMs: Long = 1000,</span>
<span class="fc" id="L29">    val backoffMultiplier: Double = 2.0,</span>
<span class="fc" id="L30">    val maxDelayMs: Long = 30000</span>
<span class="fc" id="L31">) {</span>
<span class="fc" id="L32">    init {</span>
<span class="pc bpc" id="L33" title="2 of 4 branches missed.">        require(maxRetries &gt;= 0) { &quot;Max retries must be non-negative&quot; }</span>
<span class="pc bpc" id="L34" title="2 of 4 branches missed.">        require(delayMs &gt; 0) { &quot;Delay must be positive&quot; }</span>
<span class="pc bpc" id="L35" title="2 of 4 branches missed.">        require(backoffMultiplier &gt;= 1.0) { &quot;Backoff multiplier must be &gt;= 1.0&quot; }</span>
<span class="pc bpc" id="L36" title="2 of 4 branches missed.">        require(maxDelayMs &gt; 0) { &quot;Max delay must be positive&quot; }</span>
<span class="fc" id="L37">    }</span>
}

/**
 * HTTP request configuration
 */
<span class="nc" id="L43">data class RequestConfig(</span>
<span class="nc" id="L44">    val timeoutMs: Long = 30000,</span>
<span class="nc" id="L45">    val retryConfig: RetryConfig = RetryConfig(),</span>
<span class="nc" id="L46">    val followRedirects: Boolean = true,</span>
<span class="nc" id="L47">    val userAgent: String = &quot;AiCodingCli/1.0&quot;</span>
<span class="nc" id="L48">) {</span>
<span class="nc" id="L49">    init {</span>
<span class="nc bnc" id="L50" title="All 4 branches missed.">        require(timeoutMs &gt; 0) { &quot;Timeout must be positive&quot; }</span>
<span class="nc" id="L51">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>