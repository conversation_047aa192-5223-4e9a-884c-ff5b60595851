<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConversationStateManager.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">ConversationStateManager.kt</span></div><h1>ConversationStateManager.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import java.io.File
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap

/**
 * Manager for conversation state storage and retrieval
 */
<span class="pc" id="L13">class ConversationStateManager(</span>
<span class="pc" id="L14">    private val stateDir: String = System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli/conversations&quot;</span>
<span class="nc" id="L15">) {</span>
    
<span class="fc" id="L17">    private val stateFile = File(stateDir, &quot;sessions.json&quot;)</span>
<span class="fc" id="L18">    private val json = Json {</span>
<span class="fc" id="L19">        prettyPrint = true</span>
<span class="fc" id="L20">        ignoreUnknownKeys = true</span>
<span class="fc" id="L21">    }</span>
    
<span class="fc" id="L23">    private val sessions = ConcurrentHashMap&lt;String, ConversationSession&gt;()</span>
    
<span class="fc" id="L25">    init {</span>
<span class="fc" id="L26">        ensureStateDirectoryExists()</span>
<span class="fc" id="L27">        loadSessions()</span>
<span class="fc" id="L28">    }</span>
    
    /**
     * Create a new conversation session
     */
    suspend fun createSession(requirement: String): ConversationSession {
<span class="fc" id="L34">        val session = ConversationSession(</span>
<span class="fc" id="L35">            requirement = requirement,</span>
<span class="fc" id="L36">            state = ConversationState(</span>
<span class="fc" id="L37">                status = ConversationStatus.CREATED,</span>
<span class="fc" id="L38">                currentTaskIndex = 0,</span>
<span class="fc" id="L39">                executionRound = 0,</span>
<span class="fc" id="L40">                context = emptyMap(),</span>
<span class="fc" id="L41">                errors = emptyList()</span>
            )
        )
        
<span class="fc" id="L45">        sessions[session.id] = session</span>
<span class="fc" id="L46">        saveSessions()</span>
<span class="fc" id="L47">        return session</span>
    }
    
    /**
     * Update conversation state
     */
    suspend fun updateState(sessionId: String, state: ConversationState): ConversationSession {
<span class="fc bfc" id="L54" title="All 2 branches covered.">        val session = sessions[sessionId]</span>
<span class="fc" id="L55">            ?: throw IllegalArgumentException(&quot;Conversation session not found: $sessionId&quot;)</span>

<span class="fc" id="L57">        val updatedSession = session.withUpdatedState(state)</span>
<span class="fc" id="L58">        sessions[sessionId] = updatedSession</span>
<span class="fc" id="L59">        saveSessions()</span>
<span class="fc" id="L60">        return updatedSession</span>
    }

    /**
     * Update entire conversation session
     */
    suspend fun updateSession(session: ConversationSession): ConversationSession {
<span class="fc" id="L67">        sessions[session.id] = session</span>
<span class="fc" id="L68">        saveSessions()</span>
<span class="fc" id="L69">        return session</span>
    }
    
    /**
     * Get conversation session by ID
     */
    suspend fun getSession(sessionId: String): ConversationSession? {
<span class="fc" id="L76">        return sessions[sessionId]</span>
    }
    
    /**
     * End conversation session
     */
    suspend fun endSession(sessionId: String) {
<span class="fc bfc" id="L83" title="All 2 branches covered.">        val session = sessions[sessionId]</span>
<span class="fc" id="L84">            ?: throw IllegalArgumentException(&quot;Conversation session not found: $sessionId&quot;)</span>
        
<span class="fc" id="L86">        val endedState = session.state.copy(status = ConversationStatus.COMPLETED)</span>
<span class="fc" id="L87">        val endedSession = session.withUpdatedState(endedState)</span>
<span class="fc" id="L88">        sessions[sessionId] = endedSession</span>
<span class="fc" id="L89">        saveSessions()</span>
<span class="fc" id="L90">    }</span>
    
    /**
     * Get all active sessions
     */
    suspend fun getActiveSessions(): List&lt;ConversationSession&gt; {
<span class="nc" id="L96">        return sessions.values.filter { </span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">            it.state.status !in listOf(ConversationStatus.COMPLETED, ConversationStatus.FAILED, ConversationStatus.CANCELLED)</span>
<span class="nc" id="L98">        }.sortedByDescending { it.updatedAt }</span>
    }
    
    /**
     * Delete a conversation session
     */
    suspend fun deleteSession(sessionId: String): Boolean {
<span class="nc bnc" id="L105" title="All 2 branches missed.">        val removed = sessions.remove(sessionId) != null</span>
<span class="nc bnc" id="L106" title="All 2 branches missed.">        if (removed) {</span>
<span class="nc" id="L107">            saveSessions()</span>
        }
<span class="nc" id="L109">        return removed</span>
    }
    
    /**
     * Clear all sessions
     */
    suspend fun clearAllSessions() {
<span class="nc" id="L116">        sessions.clear()</span>
<span class="nc" id="L117">        saveSessions()</span>
<span class="nc" id="L118">    }</span>
    
    /**
     * Ensure state directory exists
     */
    private fun ensureStateDirectoryExists() {
<span class="fc" id="L124">        val dir = File(stateDir)</span>
<span class="fc bfc" id="L125" title="All 2 branches covered.">        if (!dir.exists()) {</span>
<span class="fc" id="L126">            dir.mkdirs()</span>
        }
<span class="fc" id="L128">    }</span>
    
    /**
     * Load sessions from file
     */
    private fun loadSessions() {
<span class="fc" id="L134">        try {</span>
<span class="fc bfc" id="L135" title="All 2 branches covered.">            if (stateFile.exists()) {</span>
<span class="fc" id="L136">                val content = stateFile.readText()</span>
<span class="pc bpc" id="L137" title="2 of 4 branches missed.">                if (content.isNotBlank()) {</span>
<span class="fc" id="L138">                    val sessionList = json.decodeFromString&lt;List&lt;ConversationSession&gt;&gt;(content)</span>
<span class="fc" id="L139">                    sessions.clear()</span>
<span class="fc" id="L140">                    sessionList.forEach { session -&gt;</span>
<span class="fc" id="L141">                        sessions[session.id] = session</span>
<span class="fc" id="L142">                    }</span>
                }
            }
<span class="nc" id="L145">        } catch (e: Exception) {</span>
            // Log error but don't fail - start with empty sessions
<span class="nc" id="L147">            println(&quot;Warning: Failed to load conversation sessions: ${e.message}&quot;)</span>
        }
<span class="fc" id="L149">    }</span>
    
    /**
     * Save sessions to file
     */
    private fun saveSessions() {
<span class="fc" id="L155">        try {</span>
<span class="fc" id="L156">            ensureStateDirectoryExists()</span>
<span class="fc" id="L157">            val sessionList = sessions.values.toList()</span>
<span class="fc" id="L158">            val content = json.encodeToString(sessionList)</span>
<span class="fc" id="L159">            stateFile.writeText(content)</span>
<span class="nc" id="L160">        } catch (e: Exception) {</span>
<span class="nc" id="L161">            throw IOException(&quot;Failed to save conversation sessions: ${e.message}&quot;, e)</span>
        }
<span class="fc" id="L163">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>