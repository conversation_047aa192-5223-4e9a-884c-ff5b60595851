<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConversationModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">ConversationModels.kt</span></div><h1>ConversationModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

import kotlinx.serialization.Serializable
import java.time.Duration
import java.time.Instant
import java.util.*

/**
 * Represents a conversation session for continuous dialogue
 */
<span class="pc bpc" id="L11" title="17 of 42 branches missed.">@Serializable</span>
<span class="fc" id="L12">data class ConversationSession(</span>
<span class="pc" id="L13">    val id: String = UUID.randomUUID().toString(),</span>
<span class="fc" id="L14">    val requirement: String,</span>
<span class="fc" id="L15">    val state: ConversationState,</span>
<span class="fc" id="L16">    val tasks: List&lt;ExecutableTask&gt; = emptyList(),</span>
<span class="fc" id="L17">    val executionHistory: List&lt;ExecutionStep&gt; = emptyList(),</span>
<span class="fc" id="L18">    @Serializable(with = InstantSerializer::class)</span>
<span class="pc" id="L19">    val createdAt: Instant = Instant.now(),</span>
<span class="fc" id="L20">    @Serializable(with = InstantSerializer::class)</span>
<span class="pc" id="L21">    val updatedAt: Instant = Instant.now()</span>
<span class="fc" id="L22">) {</span>
    fun withUpdatedState(newState: ConversationState): ConversationSession {
<span class="fc" id="L24">        return copy(state = newState, updatedAt = Instant.now())</span>
    }

    fun withTasks(newTasks: List&lt;ExecutableTask&gt;): ConversationSession {
<span class="fc" id="L28">        return copy(tasks = newTasks, updatedAt = Instant.now())</span>
    }

    fun addExecutionStep(step: ExecutionStep): ConversationSession {
<span class="fc" id="L32">        return copy(</span>
<span class="fc" id="L33">            executionHistory = executionHistory + step,</span>
<span class="fc" id="L34">            updatedAt = Instant.now()</span>
        )
    }
}

/**
 * Current state of a conversation
 */
<span class="pc bpc" id="L42" title="9 of 34 branches missed.">@Serializable</span>
<span class="pc" id="L43">data class ConversationState(</span>
<span class="fc" id="L44">    val status: ConversationStatus,</span>
<span class="pc" id="L45">    val currentTaskIndex: Int = 0,</span>
<span class="pc" id="L46">    val executionRound: Int = 0,</span>
<span class="pc" id="L47">    val context: Map&lt;String, String&gt; = emptyMap(),</span>
<span class="pc" id="L48">    val errors: List&lt;ExecutionError&gt; = emptyList()</span>
<span class="nc" id="L49">) {</span>
    fun nextRound(): ConversationState {
<span class="nc" id="L51">        return copy(executionRound = executionRound + 1)</span>
    }

    fun nextTask(): ConversationState {
<span class="nc" id="L55">        return copy(currentTaskIndex = currentTaskIndex + 1)</span>
    }

    fun withError(error: ExecutionError): ConversationState {
<span class="fc" id="L59">        return copy(errors = errors + error)</span>
    }

    fun withContext(key: String, value: String): ConversationState {
<span class="nc" id="L63">        return copy(context = context + (key to value))</span>
    }
}

/**
 * Status of a conversation
 */
<span class="fc" id="L70">@Serializable</span>
enum class ConversationStatus {
<span class="fc" id="L72">    CREATED,        // Just created, not started</span>
<span class="fc" id="L73">    PLANNING,       // Analyzing requirements and planning tasks</span>
<span class="fc" id="L74">    EXECUTING,      // Currently executing tasks</span>
<span class="fc" id="L75">    WAITING_USER,   // Waiting for user input/confirmation</span>
<span class="fc" id="L76">    COMPLETED,      // Successfully completed</span>
<span class="fc" id="L77">    FAILED,         // Failed with errors</span>
<span class="fc" id="L78">    CANCELLED       // Cancelled by user</span>
<span class="fc" id="L79">}</span>

/**
 * A task that can be executed as part of a conversation
 */
<span class="pc bpc" id="L84" title="25 of 42 branches missed.">@Serializable</span>
<span class="fc" id="L85">data class ExecutableTask(</span>
<span class="pc" id="L86">    val id: String = UUID.randomUUID().toString(),</span>
<span class="fc" id="L87">    val description: String,</span>
<span class="fc" id="L88">    val toolCalls: List&lt;ToolCall&gt;,</span>
<span class="pc" id="L89">    val dependencies: List&lt;String&gt; = emptyList(),</span>
<span class="pc" id="L90">    val priority: Int = 0,</span>
<span class="pc" id="L91">    val estimatedDuration: kotlin.time.Duration? = null,</span>
<span class="pc" id="L92">    val status: TaskStatus = TaskStatus.PENDING</span>
<span class="fc" id="L93">) {</span>
    fun withStatus(newStatus: TaskStatus): ExecutableTask {
<span class="nc" id="L95">        return copy(status = newStatus)</span>
    }
}

/**
 * Status of an executable task
 */
<span class="fc" id="L102">@Serializable</span>
enum class TaskStatus {
<span class="fc" id="L104">    PENDING,        // Not started yet</span>
<span class="fc" id="L105">    RUNNING,        // Currently executing</span>
<span class="fc" id="L106">    COMPLETED,      // Successfully completed</span>
<span class="fc" id="L107">    FAILED,         // Failed with errors</span>
<span class="fc" id="L108">    SKIPPED         // Skipped due to dependencies or conditions</span>
<span class="fc" id="L109">}</span>

/**
 * A tool call within a task
 */
<span class="pc bpc" id="L114" title="7 of 10 branches missed.">@Serializable</span>
<span class="fc" id="L115">data class ToolCall(</span>
<span class="fc" id="L116">    val toolName: String,</span>
<span class="fc" id="L117">    val parameters: Map&lt;String, String&gt;,</span>
<span class="pc" id="L118">    val expectedResult: String? = null</span>
<span class="fc" id="L119">) {</span>
    fun withParameter(key: String, value: String): ToolCall {
<span class="nc" id="L121">        return copy(parameters = parameters + (key to value))</span>
    }
}

/**
 * A step in the execution history
 */
<span class="pc bpc" id="L128" title="12 of 18 branches missed.">@Serializable</span>
<span class="fc" id="L129">data class ExecutionStep(</span>
<span class="pc" id="L130">    val id: String = UUID.randomUUID().toString(),</span>
<span class="pc" id="L131">    val taskId: String,</span>
<span class="fc" id="L132">    val toolCall: ToolCall,</span>
<span class="fc" id="L133">    val result: ToolResult,</span>
<span class="pc" id="L134">    @Serializable(with = InstantSerializer::class)</span>
<span class="pc" id="L135">    val executedAt: Instant = Instant.now(),</span>
<span class="fc" id="L136">    @Serializable(with = KotlinDurationSerializer::class)</span>
    val duration: kotlin.time.Duration
<span class="fc" id="L138">)</span>

/**
 * Result of a tool execution
 */
<span class="pc bpc" id="L143" title="10 of 18 branches missed.">@Serializable</span>
<span class="fc" id="L144">data class ToolResult(</span>
<span class="fc" id="L145">    val success: Boolean,</span>
<span class="fc" id="L146">    val output: String,</span>
<span class="pc" id="L147">    val error: String? = null,</span>
<span class="pc" id="L148">    val metadata: Map&lt;String, String&gt; = emptyMap()</span>
<span class="fc" id="L149">) {</span>
<span class="pc" id="L150">    companion object {</span>
<span class="fc" id="L151">        fun success(output: String, metadata: Map&lt;String, String&gt; = emptyMap()): ToolResult {</span>
<span class="fc" id="L152">            return ToolResult(success = true, output = output, metadata = metadata)</span>
        }

<span class="fc" id="L155">        fun failure(error: String, output: String = &quot;&quot;): ToolResult {</span>
<span class="fc" id="L156">            return ToolResult(success = false, output = output, error = error)</span>
        }
    }
}

/**
 * An execution error
 */
<span class="pc bpc" id="L164" title="12 of 18 branches missed.">@Serializable</span>
<span class="fc" id="L165">data class ExecutionError(</span>
<span class="fc" id="L166">    val message: String,</span>
<span class="pc" id="L167">    val code: String,</span>
<span class="pc" id="L168">    val details: String? = null,</span>
<span class="pc" id="L169">    @Serializable(with = InstantSerializer::class)</span>
<span class="pc" id="L170">    val timestamp: Instant = Instant.now()</span>
<span class="fc" id="L171">)</span>

/**
 * Metadata about a tool
 */
<span class="pc bnc" id="L176" title="All 26 branches missed.">@Serializable</span>
<span class="fc" id="L177">data class ToolMetadata(</span>
<span class="fc" id="L178">    val name: String,</span>
<span class="fc" id="L179">    val description: String,</span>
<span class="fc" id="L180">    val parameters: List&lt;ToolParameter&gt;,</span>
<span class="pc" id="L181">    val category: String = &quot;general&quot;,</span>
<span class="pc" id="L182">    val riskLevel: ToolRiskLevel = ToolRiskLevel.LOW,</span>
<span class="pc" id="L183">    val examples: List&lt;ToolExample&gt; = emptyList()</span>
<span class="fc" id="L184">)</span>

/**
 * Parameter definition for a tool
 */
<span class="pc bnc" id="L189" title="All 26 branches missed.">@Serializable</span>
<span class="fc" id="L190">data class ToolParameter(</span>
<span class="fc" id="L191">    val name: String,</span>
<span class="fc" id="L192">    val type: String,</span>
<span class="fc" id="L193">    val description: String,</span>
<span class="pc" id="L194">    val required: Boolean = true,</span>
<span class="pc" id="L195">    val defaultValue: String? = null,</span>
<span class="pc" id="L196">    val examples: List&lt;String&gt; = emptyList()</span>
<span class="fc" id="L197">)</span>

/**
 * Tool usage example
 */
<span class="nc bnc" id="L202" title="All 2 branches missed.">@Serializable</span>
<span class="nc" id="L203">data class ToolExample(</span>
<span class="nc" id="L204">    val description: String,</span>
<span class="nc" id="L205">    val parameters: Map&lt;String, String&gt;,</span>
<span class="nc" id="L206">    val expectedResult: String</span>
)

/**
 * Risk levels for tools
 */
enum class ToolRiskLevel {
<span class="fc" id="L213">    LOW,        // Safe operations like view, codebase-retrieval</span>
<span class="fc" id="L214">    MEDIUM,     // File modifications like save-file, str-replace-editor</span>
<span class="fc" id="L215">    HIGH        // Destructive operations like remove-files, system commands</span>
}

/**
 * Validation result for operations
 */
<span class="fc" id="L221">data class ValidationResult(</span>
<span class="fc" id="L222">    val isValid: Boolean,</span>
<span class="fc" id="L223">    val errors: List&lt;String&gt; = emptyList(),</span>
<span class="pc" id="L224">    val warnings: List&lt;String&gt; = emptyList()</span>
<span class="fc" id="L225">) {</span>
    companion object {
<span class="fc" id="L227">        fun valid(): ValidationResult = ValidationResult(true)</span>
<span class="fc" id="L228">        fun invalid(errors: List&lt;String&gt;): ValidationResult = ValidationResult(false, errors)</span>
<span class="nc" id="L229">        fun invalid(error: String): ValidationResult = ValidationResult(false, listOf(error))</span>
    }
}

/**
 * Context for project operations
 */
<span class="fc" id="L236">data class ProjectContext(</span>
<span class="fc" id="L237">    val projectPath: String,</span>
<span class="pc" id="L238">    val language: String,</span>
<span class="fc" id="L239">    val framework: String? = null,</span>
<span class="pc" id="L240">    val buildTool: String? = null,</span>
<span class="pc" id="L241">    val dependencies: List&lt;String&gt; = emptyList(),</span>
<span class="pc" id="L242">    val metadata: Map&lt;String, String&gt; = emptyMap()</span>
<span class="fc" id="L243">)</span>

/**
 * Result of auto execution
 */
<span class="fc" id="L248">data class ExecutionResult(</span>
<span class="fc" id="L249">    val success: Boolean,</span>
<span class="fc" id="L250">    val sessionId: String?,</span>
<span class="fc" id="L251">    val finalStatus: ConversationStatus,</span>
<span class="fc" id="L252">    val executedSteps: List&lt;ExecutionStep&gt;,</span>
<span class="fc" id="L253">    val executionRounds: Int,</span>
<span class="fc" id="L254">    val executionTime: kotlin.time.Duration,</span>
<span class="fc" id="L255">    val summary: String? = null,</span>
<span class="fc" id="L256">    val error: String? = null</span>
<span class="fc" id="L257">) {</span>
    companion object {
<span class="nc" id="L259">        fun success(</span>
            sessionId: String,
            finalStatus: ConversationStatus,
            executedSteps: List&lt;ExecutionStep&gt;,
            executionRounds: Int,
            executionTime: kotlin.time.Duration,
<span class="nc" id="L265">            summary: String? = null</span>
        ): ExecutionResult {
<span class="fc" id="L267">            return ExecutionResult(</span>
<span class="fc" id="L268">                success = true,</span>
<span class="fc" id="L269">                sessionId = sessionId,</span>
<span class="fc" id="L270">                finalStatus = finalStatus,</span>
<span class="fc" id="L271">                executedSteps = executedSteps,</span>
<span class="fc" id="L272">                executionRounds = executionRounds,</span>
<span class="fc" id="L273">                executionTime = executionTime,</span>
<span class="fc" id="L274">                summary = summary</span>
            )
        }

        fun failure(
            sessionId: String?,
            finalStatus: ConversationStatus,
            executedSteps: List&lt;ExecutionStep&gt;,
            executionRounds: Int,
            executionTime: kotlin.time.Duration,
            error: String
        ): ExecutionResult {
<span class="fc" id="L286">            return ExecutionResult(</span>
<span class="fc" id="L287">                success = false,</span>
<span class="fc" id="L288">                sessionId = sessionId,</span>
<span class="fc" id="L289">                finalStatus = finalStatus,</span>
<span class="fc" id="L290">                executedSteps = executedSteps,</span>
<span class="fc" id="L291">                executionRounds = executionRounds,</span>
<span class="fc" id="L292">                executionTime = executionTime,</span>
<span class="fc" id="L293">                error = error</span>
            )
        }
    }
}

/**
 * Result of a single step execution
 */
<span class="fc" id="L302">data class StepResult(</span>
<span class="fc" id="L303">    val success: Boolean,</span>
<span class="fc" id="L304">    val toolResult: ToolResult?,</span>
<span class="pc" id="L305">    val error: String? = null</span>
<span class="fc" id="L306">) {</span>
    companion object {
        fun success(toolResult: ToolResult): StepResult {
<span class="fc" id="L309">            return StepResult(success = true, toolResult = toolResult)</span>
        }

        fun failure(error: String): StepResult {
<span class="nc" id="L313">            return StepResult(success = false, toolResult = null, error = error)</span>
        }
    }
}

/**
 * Parsed requirement with intent and parameters
 */
<span class="pc bnc" id="L321" title="All 10 branches missed.">@Serializable</span>
<span class="fc" id="L322">data class ParsedRequirement(</span>
<span class="fc" id="L323">    val intent: Intent,</span>
<span class="fc" id="L324">    val parameters: Map&lt;String, String&gt;,</span>
<span class="pc" id="L325">    val confidence: Float = 1.0f,</span>
<span class="pc" id="L326">    val originalText: String</span>
<span class="fc" id="L327">)</span>

/**
 * Intent types for requirements
 */
<span class="fc" id="L332">@Serializable</span>
enum class Intent {
<span class="fc" id="L334">    CREATE_CLASS,           // Create a new class/interface</span>
<span class="fc" id="L335">    CREATE_API,             // Create API endpoints</span>
<span class="fc" id="L336">    CREATE_CONFIG,          // Create configuration files</span>
<span class="fc" id="L337">    CREATE_TESTS,           // Create test files</span>
<span class="fc" id="L338">    CREATE_SERVICE,         // Create service/microservice</span>
<span class="fc" id="L339">    CREATE_SYSTEM,          // Create complete system</span>
<span class="fc" id="L340">    CREATE_FORM,            // Create forms/UI components</span>
<span class="fc" id="L341">    CREATE_DOCUMENTATION,   // Create documentation</span>
<span class="fc" id="L342">    REFACTOR_CODE,          // Refactor existing code</span>
<span class="fc" id="L343">    IMPROVE_CODE,           // General code improvement</span>
<span class="fc" id="L344">    OPTIMIZE_PERFORMANCE,   // Performance optimization</span>
<span class="fc" id="L345">    FIX_BUG,               // Bug fixing</span>
<span class="fc" id="L346">    ADD_FEATURE,           // Add new feature</span>
<span class="fc" id="L347">    REMOVE_FEATURE,        // Remove feature</span>
<span class="fc" id="L348">    UPDATE_DEPENDENCY,     // Update dependencies</span>
<span class="fc" id="L349">    UNKNOWN                // Unknown intent</span>
<span class="fc" id="L350">}</span>
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>