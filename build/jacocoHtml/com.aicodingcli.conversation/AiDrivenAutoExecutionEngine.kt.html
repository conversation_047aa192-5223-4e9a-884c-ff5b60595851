<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiDrivenAutoExecutionEngine.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">AiDrivenAutoExecutionEngine.kt</span></div><h1>AiDrivenAutoExecutionEngine.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

import kotlin.time.measureTime

/**
 * AI-driven automatic execution engine that uses AI prompts to make decisions
 */
<span class="fc" id="L8">class AiDrivenAutoExecutionEngine(</span>
<span class="fc" id="L9">    private val conversationStateManager: ConversationStateManager,</span>
<span class="fc" id="L10">    private val aiPromptEngine: AiPromptEngine,</span>
<span class="fc" id="L11">    private val toolExecutor: ToolExecutor,</span>
<span class="fc" id="L12">    private val strategy: AiExecutionStrategy = AiExecutionStrategy()</span>
<span class="fc" id="L13">) : AutoExecutionEngine {</span>
    
<span class="fc" id="L15">    override suspend fun executeConversation(requirement: String): ExecutionResult {</span>
<span class="fc" id="L16">        val startTime = kotlin.time.TimeSource.Monotonic.markNow()</span>
        
<span class="fc" id="L18">        try {</span>
            // Handle empty requirement
<span class="fc bfc" id="L20" title="All 2 branches covered.">            if (requirement.isBlank()) {</span>
<span class="fc" id="L21">                return ExecutionResult.success(</span>
<span class="fc" id="L22">                    sessionId = &quot;empty-session&quot;,</span>
<span class="fc" id="L23">                    finalStatus = ConversationStatus.COMPLETED,</span>
<span class="fc" id="L24">                    executedSteps = emptyList(),</span>
<span class="fc" id="L25">                    executionRounds = 0,</span>
<span class="fc" id="L26">                    executionTime = startTime.elapsedNow(),</span>
<span class="fc" id="L27">                    summary = &quot;Empty requirement - no action taken&quot;</span>
                )
            }
            
            // Create conversation session
<span class="fc" id="L32">            val session = conversationStateManager.createSession(requirement)</span>
            
            // Create execution context
<span class="fc" id="L35">            val context = ExecutionContext(</span>
<span class="fc" id="L36">                projectPath = &quot;&quot;,</span>
<span class="fc" id="L37">                language = &quot;kotlin&quot;,</span>
<span class="fc" id="L38">                framework = &quot;spring-boot&quot;,</span>
<span class="fc" id="L39">                buildTool = &quot;gradle&quot;</span>
            )
            
            // Start AI-driven execution
<span class="fc" id="L43">            return executeWithAiGuidance(session, context, startTime)</span>
            
<span class="pc" id="L45">        } catch (e: Exception) {</span>
<span class="nc" id="L46">            return ExecutionResult.failure(</span>
<span class="nc" id="L47">                sessionId = null,</span>
<span class="nc" id="L48">                finalStatus = ConversationStatus.FAILED,</span>
<span class="nc" id="L49">                executedSteps = emptyList(),</span>
<span class="nc" id="L50">                executionRounds = 0,</span>
<span class="nc" id="L51">                executionTime = startTime.elapsedNow(),</span>
<span class="nc" id="L52">                error = &quot;Failed to execute conversation: ${e.message}&quot;</span>
            )
        }
    }
    
    override suspend fun continueConversation(sessionId: String): ExecutionResult {
<span class="fc" id="L58">        val startTime = kotlin.time.TimeSource.Monotonic.markNow()</span>
        
<span class="fc" id="L60">        try {</span>
<span class="fc bfc" id="L61" title="All 2 branches covered.">            val session = conversationStateManager.getSession(sessionId)</span>
<span class="fc" id="L62">                ?: return ExecutionResult.failure(</span>
<span class="fc" id="L63">                    sessionId = sessionId,</span>
<span class="fc" id="L64">                    finalStatus = ConversationStatus.FAILED,</span>
<span class="fc" id="L65">                    executedSteps = emptyList(),</span>
<span class="fc" id="L66">                    executionRounds = 0,</span>
<span class="fc" id="L67">                    executionTime = startTime.elapsedNow(),</span>
<span class="fc" id="L68">                    error = &quot;Session not found: $sessionId&quot;</span>
                )
            
<span class="fc" id="L71">            val context = ExecutionContext(</span>
<span class="fc" id="L72">                projectPath = &quot;&quot;,</span>
<span class="fc" id="L73">                language = &quot;kotlin&quot;,</span>
<span class="fc" id="L74">                framework = &quot;spring-boot&quot;,</span>
<span class="fc" id="L75">                buildTool = &quot;gradle&quot;</span>
            )
            
<span class="fc" id="L78">            return executeWithAiGuidance(session, context, startTime)</span>
            
<span class="pc" id="L80">        } catch (e: Exception) {</span>
<span class="nc" id="L81">            return ExecutionResult.failure(</span>
<span class="nc" id="L82">                sessionId = sessionId,</span>
<span class="nc" id="L83">                finalStatus = ConversationStatus.FAILED,</span>
<span class="nc" id="L84">                executedSteps = emptyList(),</span>
<span class="nc" id="L85">                executionRounds = 0,</span>
<span class="nc" id="L86">                executionTime = startTime.elapsedNow(),</span>
<span class="nc" id="L87">                error = &quot;Failed to continue conversation: ${e.message}&quot;</span>
            )
        }
    }
    
<span class="fc" id="L92">    override suspend fun executeStep(step: ExecutionStep): StepResult {</span>
<span class="fc" id="L93">        try {</span>
<span class="fc" id="L94">            val toolResult = toolExecutor.execute(step.toolCall)</span>
<span class="fc" id="L95">            return StepResult.success(toolResult)</span>
<span class="nc" id="L96">        } catch (e: Exception) {</span>
<span class="nc" id="L97">            return StepResult.failure(&quot;Failed to execute step: ${e.message}&quot;)</span>
        }
    }
    
    override fun setMaxExecutionRounds(maxRounds: Int) {
<span class="nc bnc" id="L102" title="All 2 branches missed.">        if (maxRounds &lt;= 0) {</span>
<span class="nc" id="L103">            throw IllegalArgumentException(&quot;Max execution rounds must be positive, got: $maxRounds&quot;)</span>
        }
        // Update strategy with new max rounds
        // Note: This would require making strategy mutable or creating a new one
<span class="nc" id="L107">    }</span>
    
<span class="fc" id="L109">    private suspend fun executeWithAiGuidance(</span>
        session: ConversationSession,
        context: ExecutionContext,
        startTime: kotlin.time.TimeMark
    ): ExecutionResult {
<span class="fc" id="L114">        val executedSteps = session.executionHistory.toMutableList()</span>
<span class="fc" id="L115">        val executionRounds = mutableListOf&lt;AiExecutionRound&gt;()</span>
<span class="fc" id="L116">        var currentSession = session</span>
<span class="fc" id="L117">        var roundNumber = currentSession.state.executionRound</span>
        
        // Update session status to executing
<span class="fc" id="L120">        currentSession = conversationStateManager.updateState(</span>
<span class="fc" id="L121">            currentSession.id,</span>
<span class="fc" id="L122">            currentSession.state.copy(status = ConversationStatus.EXECUTING)</span>
        )
        
        // Initial requirement analysis
<span class="fc" id="L126">        val analysisResult = aiPromptEngine.analyzeRequirement(currentSession.requirement, context)</span>
        
        // Main execution loop
<span class="fc bfc" id="L129" title="All 2 branches covered.">        while (roundNumber &lt; strategy.maxRounds) {</span>
<span class="fc" id="L130">            roundNumber++</span>
            
            // Get available tools
<span class="fc" id="L133">            val availableTools = toolExecutor.getSupportedTools()</span>
<span class="fc" id="L134">                .filter { strategy.isToolAllowed(it.name) }</span>
            
            // AI decides next action
<span class="fc" id="L137">            val actionDecision = aiPromptEngine.decideNextAction(</span>
<span class="fc" id="L138">                requirement = currentSession.requirement,</span>
<span class="fc" id="L139">                executionHistory = executedSteps,</span>
<span class="fc" id="L140">                availableTools = availableTools,</span>
<span class="fc" id="L141">                context = context</span>
            )
            
<span class="fc" id="L144">            val round = AiExecutionRound(</span>
<span class="fc" id="L145">                roundNumber = roundNumber,</span>
<span class="fc" id="L146">                requirement = currentSession.requirement,</span>
<span class="fc bfc" id="L147" title="All 2 branches covered.">                analysisResult = if (roundNumber == 1) analysisResult else null,</span>
<span class="fc" id="L148">                actionDecision = actionDecision,</span>
<span class="fc" id="L149">                executionStep = null,</span>
<span class="fc" id="L150">                safetyCheck = null,</span>
<span class="fc" id="L151">                completionEvaluation = null</span>
            )
            
<span class="fc" id="L154">            when (actionDecision) {</span>
<span class="fc bfc" id="L155" title="All 2 branches covered.">                is ActionDecision.ExecuteTool -&gt; {</span>
                    // Perform safety check
<span class="fc" id="L157">                    val safetyCheck = performSafetyCheck(actionDecision.toolName, actionDecision.parameters)</span>
<span class="fc" id="L158">                    val updatedRound = round.copy(safetyCheck = safetyCheck)</span>
                    
<span class="fc bfc" id="L160" title="All 2 branches covered.">                    if (!safetyCheck.allowed) {</span>
<span class="fc" id="L161">                        executionRounds.add(updatedRound)</span>
<span class="fc" id="L162">                        return createFailureResult(</span>
<span class="fc" id="L163">                            currentSession,</span>
<span class="fc" id="L164">                            executedSteps,</span>
<span class="fc" id="L165">                            roundNumber,</span>
<span class="fc" id="L166">                            startTime,</span>
<span class="fc" id="L167">                            &quot;Safety check failed: ${safetyCheck.reason}&quot;</span>
                        )
                    }
                    
<span class="fc bfc" id="L171" title="All 2 branches covered.">                    if (safetyCheck.requiresConfirmation) {</span>
<span class="fc" id="L172">                        executionRounds.add(updatedRound)</span>
<span class="fc" id="L173">                        return createWaitingResult(</span>
<span class="fc" id="L174">                            currentSession,</span>
<span class="fc" id="L175">                            executedSteps,</span>
<span class="fc" id="L176">                            roundNumber,</span>
<span class="fc" id="L177">                            startTime,</span>
<span class="fc" id="L178">                            &quot;User confirmation required for ${actionDecision.toolName}: ${safetyCheck.reason}&quot;</span>
                        )
                    }
                    
                    // Execute the tool
<span class="fc" id="L183">                    val toolCall = ToolCall(</span>
<span class="fc" id="L184">                        toolName = actionDecision.toolName,</span>
<span class="fc" id="L185">                        parameters = actionDecision.parameters</span>
                    )
                    
<span class="fc" id="L188">                    val stepStartTime = kotlin.time.TimeSource.Monotonic.markNow()</span>
<span class="fc" id="L189">                    val toolResult = toolExecutor.execute(toolCall)</span>
<span class="fc" id="L190">                    val stepDuration = stepStartTime.elapsedNow()</span>
                    
<span class="fc" id="L192">                    val executionStep = ExecutionStep(</span>
<span class="fc" id="L193">                        taskId = &quot;ai-driven-task&quot;,</span>
<span class="fc" id="L194">                        toolCall = toolCall,</span>
<span class="fc" id="L195">                        result = toolResult,</span>
<span class="fc" id="L196">                        duration = stepDuration</span>
                    )
                    
<span class="fc" id="L199">                    executedSteps.add(executionStep)</span>
                    
<span class="fc" id="L201">                    val finalRound = updatedRound.copy(executionStep = executionStep)</span>
<span class="fc" id="L202">                    executionRounds.add(finalRound)</span>
                    
                    // Update session with new step
<span class="fc" id="L205">                    currentSession = currentSession.copy(</span>
<span class="fc" id="L206">                        executionHistory = currentSession.executionHistory + executionStep,</span>
<span class="fc" id="L207">                        state = currentSession.state.copy(executionRound = roundNumber)</span>
                    )
<span class="fc" id="L209">                    conversationStateManager.updateSession(currentSession)</span>
                    
                    // If tool execution failed, decide whether to continue or stop
<span class="pc bpc" id="L212" title="1 of 4 branches missed.">                    if (!toolResult.success &amp;&amp; actionDecision.confidence &lt; strategy.confidenceThreshold) {</span>
<span class="nc" id="L213">                        return createFailureResult(</span>
<span class="nc" id="L214">                            currentSession,</span>
<span class="nc" id="L215">                            executedSteps,</span>
<span class="nc" id="L216">                            roundNumber,</span>
<span class="nc" id="L217">                            startTime,</span>
<span class="nc" id="L218">                            &quot;Tool execution failed with low confidence: ${toolResult.error}&quot;</span>
                        )
                    }
                }
                
<span class="fc bfc" id="L223" title="All 2 branches covered.">                is ActionDecision.Complete -&gt; {</span>
<span class="fc" id="L224">                    executionRounds.add(round)</span>
                    
                    // Evaluate completion
<span class="fc" id="L227">                    val completionEvaluation = aiPromptEngine.evaluateCompletion(</span>
<span class="fc" id="L228">                        requirement = currentSession.requirement,</span>
<span class="fc" id="L229">                        executionHistory = executedSteps,</span>
<span class="fc" id="L230">                        context = context</span>
                    )
                    
<span class="fc" id="L233">                    val finalRound = round.copy(completionEvaluation = completionEvaluation)</span>
<span class="fc" id="L234">                    executionRounds.add(finalRound)</span>
                    
<span class="fc" id="L236">                    return createSuccessResult(</span>
<span class="fc" id="L237">                        currentSession,</span>
<span class="fc" id="L238">                        executedSteps,</span>
<span class="fc" id="L239">                        roundNumber,</span>
<span class="fc" id="L240">                        startTime,</span>
<span class="fc" id="L241">                        actionDecision.reasoning,</span>
<span class="fc" id="L242">                        completionEvaluation</span>
                    )
                }
                
<span class="pc bpc" id="L246" title="1 of 2 branches missed.">                is ActionDecision.WaitUser -&gt; {</span>
<span class="nc" id="L247">                    executionRounds.add(round)</span>
<span class="nc" id="L248">                    return createWaitingResult(</span>
<span class="nc" id="L249">                        currentSession,</span>
<span class="nc" id="L250">                        executedSteps,</span>
<span class="nc" id="L251">                        roundNumber,</span>
<span class="nc" id="L252">                        startTime,</span>
<span class="nc" id="L253">                        actionDecision.reasoning</span>
                    )
                }
                
<span class="pc bpc" id="L257" title="1 of 2 branches missed.">                is ActionDecision.Fail -&gt; {</span>
<span class="nc" id="L258">                    executionRounds.add(round)</span>
<span class="nc" id="L259">                    return createFailureResult(</span>
<span class="nc" id="L260">                        currentSession,</span>
<span class="nc" id="L261">                        executedSteps,</span>
<span class="nc" id="L262">                        roundNumber,</span>
<span class="nc" id="L263">                        startTime,</span>
<span class="nc" id="L264">                        actionDecision.reasoning</span>
                    )
                }
                
<span class="pc" id="L268">                is ActionDecision.Failure -&gt; {</span>
<span class="fc" id="L269">                    executionRounds.add(round)</span>
<span class="fc" id="L270">                    return createFailureResult(</span>
<span class="fc" id="L271">                        currentSession,</span>
<span class="fc" id="L272">                        executedSteps,</span>
<span class="fc" id="L273">                        roundNumber,</span>
<span class="fc" id="L274">                        startTime,</span>
<span class="fc" id="L275">                        &quot;AI decision failed: ${actionDecision.error}&quot;</span>
                    )
                }
            }
        }
        
        // Reached max rounds
<span class="fc" id="L282">        return createWaitingResult(</span>
<span class="fc" id="L283">            currentSession,</span>
<span class="fc" id="L284">            executedSteps,</span>
<span class="fc" id="L285">            roundNumber,</span>
<span class="fc" id="L286">            startTime,</span>
<span class="fc" id="L287">            &quot;Reached maximum execution rounds ($roundNumber). Use continueConversation to resume.&quot;</span>
        )
    }
    
    private fun performSafetyCheck(toolName: String, parameters: Map&lt;String, String&gt;): SafetyCheckResult {
<span class="pc bpc" id="L292" title="1 of 2 branches missed.">        if (!strategy.enableSafetyChecks) {</span>
<span class="nc" id="L293">            return SafetyCheckResult.allowed()</span>
        }
        
<span class="fc bfc" id="L296" title="All 2 branches covered.">        if (!strategy.isToolAllowed(toolName)) {</span>
<span class="fc" id="L297">            return SafetyCheckResult.denied(&quot;Tool $toolName is not allowed by current strategy&quot;)</span>
        }
        
<span class="fc bfc" id="L300" title="All 2 branches covered.">        if (strategy.shouldRequireConfirmation(toolName)) {</span>
<span class="fc" id="L301">            return SafetyCheckResult.requiresConfirmation(&quot;Tool $toolName requires user confirmation&quot;)</span>
        }
        
        // Additional safety checks based on parameters
<span class="fc" id="L305">        when (toolName) {</span>
<span class="pc bpc" id="L306" title="1 of 2 branches missed.">            &quot;remove-files&quot; -&gt; {</span>
<span class="nc" id="L307">                val paths = parameters[&quot;file_paths&quot;]</span>
<span class="nc bnc" id="L308" title="All 6 branches missed.">                if (paths?.contains(&quot;..&quot;) == true) {</span>
<span class="nc" id="L309">                    return SafetyCheckResult.denied(&quot;Path traversal detected in remove-files operation&quot;)</span>
                }
            }
<span class="pc bpc" id="L312" title="1 of 2 branches missed.">            &quot;launch-process&quot; -&gt; {</span>
<span class="nc" id="L313">                val command = parameters[&quot;command&quot;]</span>
<span class="nc bnc" id="L314" title="All 12 branches missed.">                if (command?.contains(&quot;rm -rf&quot;) == true || command?.contains(&quot;del /f&quot;) == true) {</span>
<span class="nc" id="L315">                    return SafetyCheckResult.denied(&quot;Dangerous command detected in launch-process&quot;)</span>
                }
            }
        }
        
<span class="fc" id="L320">        return SafetyCheckResult.allowed()</span>
    }
    
    private suspend fun createSuccessResult(
        session: ConversationSession,
        executedSteps: List&lt;ExecutionStep&gt;,
        rounds: Int,
        startTime: kotlin.time.TimeMark,
        reasoning: String,
        completionEvaluation: CompletionEvaluation?
    ): ExecutionResult {
<span class="fc" id="L331">        val summary = when (completionEvaluation) {</span>
<span class="fc bfc" id="L332" title="All 2 branches covered.">            is CompletionEvaluation.Success -&gt; completionEvaluation.summary</span>
<span class="fc" id="L333">            else -&gt; reasoning</span>
        }

        // Update session status to completed
<span class="fc" id="L337">        val completedSession = session.copy(</span>
<span class="fc" id="L338">            state = session.state.copy(status = ConversationStatus.COMPLETED),</span>
<span class="fc" id="L339">            executionHistory = executedSteps</span>
        )
<span class="fc" id="L341">        conversationStateManager.updateSession(completedSession)</span>

<span class="fc" id="L343">        return ExecutionResult.success(</span>
<span class="fc" id="L344">            sessionId = session.id,</span>
<span class="fc" id="L345">            finalStatus = ConversationStatus.COMPLETED,</span>
<span class="fc" id="L346">            executedSteps = executedSteps,</span>
<span class="fc" id="L347">            executionRounds = rounds,</span>
<span class="fc" id="L348">            executionTime = startTime.elapsedNow(),</span>
<span class="fc" id="L349">            summary = summary</span>
        )
    }
    
    private suspend fun createWaitingResult(
        session: ConversationSession,
        executedSteps: List&lt;ExecutionStep&gt;,
        rounds: Int,
        startTime: kotlin.time.TimeMark,
        reason: String
    ): ExecutionResult {
        // Update session status to waiting for user
<span class="fc" id="L361">        val waitingSession = session.copy(</span>
<span class="fc" id="L362">            state = session.state.copy(status = ConversationStatus.WAITING_USER),</span>
<span class="fc" id="L363">            executionHistory = executedSteps</span>
        )
<span class="fc" id="L365">        conversationStateManager.updateSession(waitingSession)</span>

<span class="fc" id="L367">        return ExecutionResult.success(</span>
<span class="fc" id="L368">            sessionId = session.id,</span>
<span class="fc" id="L369">            finalStatus = ConversationStatus.WAITING_USER,</span>
<span class="fc" id="L370">            executedSteps = executedSteps,</span>
<span class="fc" id="L371">            executionRounds = rounds,</span>
<span class="fc" id="L372">            executionTime = startTime.elapsedNow(),</span>
<span class="fc" id="L373">            summary = reason</span>
        )
    }
    
    private suspend fun createFailureResult(
        session: ConversationSession,
        executedSteps: List&lt;ExecutionStep&gt;,
        rounds: Int,
        startTime: kotlin.time.TimeMark,
        error: String
    ): ExecutionResult {
        // Update session status to failed
<span class="fc" id="L385">        val failedSession = session.copy(</span>
<span class="fc" id="L386">            state = session.state.copy(status = ConversationStatus.FAILED),</span>
<span class="fc" id="L387">            executionHistory = executedSteps</span>
        )
<span class="fc" id="L389">        conversationStateManager.updateSession(failedSession)</span>

<span class="fc" id="L391">        return ExecutionResult.failure(</span>
<span class="fc" id="L392">            sessionId = session.id,</span>
<span class="fc" id="L393">            finalStatus = ConversationStatus.FAILED,</span>
<span class="fc" id="L394">            executedSteps = executedSteps,</span>
<span class="fc" id="L395">            executionRounds = rounds,</span>
<span class="fc" id="L396">            executionTime = startTime.elapsedNow(),</span>
<span class="fc" id="L397">            error = error</span>
        )
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>