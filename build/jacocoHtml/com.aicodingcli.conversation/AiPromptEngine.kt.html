<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiPromptEngine.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">AiPromptEngine.kt</span></div><h1>AiPromptEngine.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

import com.aicodingcli.ai.AiService
import com.aicodingcli.ai.AiRequest
import com.aicodingcli.ai.AiMessage
import com.aicodingcli.ai.MessageRole
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * AI Prompt Engine for driving automatic tool calls
 */
interface AiPromptEngine {
    /**
     * Analyze requirement and suggest next actions
     */
    suspend fun analyzeRequirement(requirement: String, context: ExecutionContext): AnalysisResult
    
    /**
     * Decide next tool call based on current state
     */
    suspend fun decideNextAction(
        requirement: String,
        executionHistory: List&lt;ExecutionStep&gt;,
        availableTools: List&lt;ToolMetadata&gt;,
        context: ExecutionContext
    ): ActionDecision
    
    /**
     * Evaluate if the requirement has been completed
     */
    suspend fun evaluateCompletion(
        requirement: String,
        executionHistory: List&lt;ExecutionStep&gt;,
        context: ExecutionContext
    ): CompletionEvaluation
}

/**
 * Default implementation of AI Prompt Engine
 */
<span class="fc" id="L42">class DefaultAiPromptEngine(</span>
<span class="fc" id="L43">    private val aiService: AiService,</span>
<span class="fc" id="L44">    private val json: Json = Json { ignoreUnknownKeys = true }</span>
<span class="fc" id="L45">) : AiPromptEngine {</span>
    
<span class="fc" id="L47">    override suspend fun analyzeRequirement(requirement: String, context: ExecutionContext): AnalysisResult {</span>
<span class="fc" id="L48">        val prompt = buildAnalysisPrompt(requirement, context)</span>
        
<span class="fc" id="L50">        try {</span>
<span class="fc" id="L51">            val response = aiService.chat(AiRequest(</span>
<span class="fc" id="L52">                messages = listOf(AiMessage(MessageRole.USER, prompt)),</span>
<span class="fc" id="L53">                model = &quot;gpt-4&quot;,</span>
<span class="fc" id="L54">                temperature = 0.3f</span>
            ))
            
<span class="fc" id="L57">            return parseAnalysisResponse(response.content)</span>
<span class="fc" id="L58">        } catch (e: Exception) {</span>
<span class="fc" id="L59">            return AnalysisResult.failure(&quot;Failed to analyze requirement: ${e.message}&quot;)</span>
        }
    }
    
<span class="fc" id="L63">    override suspend fun decideNextAction(</span>
        requirement: String,
        executionHistory: List&lt;ExecutionStep&gt;,
        availableTools: List&lt;ToolMetadata&gt;,
        context: ExecutionContext
    ): ActionDecision {
<span class="fc" id="L69">        val prompt = buildActionDecisionPrompt(requirement, executionHistory, availableTools, context)</span>
        
<span class="fc" id="L71">        try {</span>
<span class="fc" id="L72">            val response = aiService.chat(AiRequest(</span>
<span class="fc" id="L73">                messages = listOf(AiMessage(MessageRole.USER, prompt)),</span>
<span class="fc" id="L74">                model = &quot;gpt-4&quot;,</span>
<span class="fc" id="L75">                temperature = 0.2f</span>
            ))
            
<span class="fc" id="L78">            return parseActionDecisionResponse(response.content)</span>
<span class="fc" id="L79">        } catch (e: Exception) {</span>
<span class="fc" id="L80">            return ActionDecision.failure(&quot;Failed to decide next action: ${e.message}&quot;)</span>
        }
    }
    
<span class="fc" id="L84">    override suspend fun evaluateCompletion(</span>
        requirement: String,
        executionHistory: List&lt;ExecutionStep&gt;,
        context: ExecutionContext
    ): CompletionEvaluation {
<span class="fc" id="L89">        val prompt = buildCompletionEvaluationPrompt(requirement, executionHistory, context)</span>
        
<span class="fc" id="L91">        try {</span>
<span class="fc" id="L92">            val response = aiService.chat(AiRequest(</span>
<span class="fc" id="L93">                messages = listOf(AiMessage(MessageRole.USER, prompt)),</span>
<span class="fc" id="L94">                model = &quot;gpt-4&quot;,</span>
<span class="fc" id="L95">                temperature = 0.1f</span>
            ))
            
<span class="fc" id="L98">            return parseCompletionEvaluationResponse(response.content)</span>
<span class="nc" id="L99">        } catch (e: Exception) {</span>
<span class="nc" id="L100">            return CompletionEvaluation.failure(&quot;Failed to evaluate completion: ${e.message}&quot;)</span>
        }
    }
    
    private fun buildAnalysisPrompt(requirement: String, context: ExecutionContext): String {
<span class="fc" id="L105">        return &quot;&quot;&quot;</span>
            You are an AI assistant that analyzes programming requirements and breaks them down into actionable steps.
            
<span class="fc" id="L108">            **Requirement**: $requirement</span>
            
            **Context**:
<span class="fc" id="L111">            - Project Path: ${context.projectPath}</span>
<span class="fc" id="L112">            - Language: ${context.language}</span>
<span class="fc" id="L113">            - Framework: ${context.framework}</span>
<span class="fc" id="L114">            - Build Tool: ${context.buildTool}</span>
            
            Please analyze this requirement and provide:
            1. **Intent**: What is the user trying to achieve?
            2. **Complexity**: How complex is this requirement? (SIMPLE, MODERATE, COMPLEX)
            3. **Category**: What category does this fall into? (FILE_OPERATION, CODE_GENERATION, TESTING, REFACTORING, ANALYSIS, OTHER)
            4. **Prerequisites**: What needs to exist before this can be completed?
            5. **Estimated Steps**: How many steps do you estimate this will take?
            
            Respond in JSON format:
            {
                &quot;intent&quot;: &quot;string&quot;,
                &quot;complexity&quot;: &quot;SIMPLE|MODERATE|COMPLEX&quot;,
                &quot;category&quot;: &quot;FILE_OPERATION|CODE_GENERATION|TESTING|REFACTORING|ANALYSIS|OTHER&quot;,
                &quot;prerequisites&quot;: [&quot;string&quot;],
                &quot;estimatedSteps&quot;: number,
                &quot;reasoning&quot;: &quot;string&quot;
            }
<span class="fc" id="L132">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun buildActionDecisionPrompt(
        requirement: String,
        executionHistory: List&lt;ExecutionStep&gt;,
        availableTools: List&lt;ToolMetadata&gt;,
        context: ExecutionContext
    ): String {
<span class="fc bfc" id="L141" title="All 2 branches covered.">        val historyText = if (executionHistory.isEmpty()) {</span>
<span class="fc" id="L142">            &quot;No previous actions taken.&quot;</span>
        } else {
<span class="fc" id="L144">            executionHistory.takeLast(5).joinToString(&quot;\n&quot;) { step -&gt;</span>
<span class="fc" id="L145">                &quot;- ${step.toolCall.toolName}: ${step.result.success} - ${step.result.output.take(100)}&quot;</span>
            }
        }
        
<span class="fc" id="L149">        val toolsText = availableTools.joinToString(&quot;\n&quot;) { tool -&gt;</span>
<span class="fc" id="L150">            &quot;- ${tool.name}: ${tool.description}&quot;</span>
        }
        
<span class="fc" id="L153">        return &quot;&quot;&quot;</span>
            You are an AI assistant that decides the next action to take in completing a programming requirement.
            
<span class="fc" id="L156">            **Original Requirement**: $requirement</span>
            
            **Execution History** (last 5 actions):
<span class="fc" id="L159">            $historyText</span>
            
            **Available Tools**:
<span class="fc" id="L162">            $toolsText</span>
            
            **Context**:
<span class="fc" id="L165">            - Project Path: ${context.projectPath}</span>
<span class="fc" id="L166">            - Language: ${context.language}</span>
<span class="fc" id="L167">            - Framework: ${context.framework}</span>
            
            Based on the requirement and execution history, decide the next action to take.
            
            Respond in JSON format:
            {
                &quot;action&quot;: &quot;EXECUTE_TOOL|COMPLETE|WAIT_USER|FAIL&quot;,
                &quot;toolName&quot;: &quot;string (if action is EXECUTE_TOOL)&quot;,
                &quot;parameters&quot;: {&quot;key&quot;: &quot;value&quot;} (if action is EXECUTE_TOOL),
                &quot;reasoning&quot;: &quot;string&quot;,
                &quot;confidence&quot;: number (0.0 to 1.0)
            }
            
            Guidelines:
            - If the requirement seems complete, use action &quot;COMPLETE&quot;
            - If you need user input or clarification, use action &quot;WAIT_USER&quot;
            - If something went wrong and cannot be fixed, use action &quot;FAIL&quot;
            - Otherwise, use action &quot;EXECUTE_TOOL&quot; with the appropriate tool and parameters
            - Be conservative with file operations and always check if files exist first
            - Prefer using 'view' tool to understand current state before making changes
<span class="fc" id="L187">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun buildCompletionEvaluationPrompt(
        requirement: String,
        executionHistory: List&lt;ExecutionStep&gt;,
        context: ExecutionContext
    ): String {
<span class="fc" id="L195">        val historyText = executionHistory.joinToString(&quot;\n&quot;) { step -&gt;</span>
<span class="pc bpc" id="L196" title="1 of 2 branches missed.">            &quot;- ${step.toolCall.toolName}: ${if (step.result.success) &quot;SUCCESS&quot; else &quot;FAILED&quot;} - ${step.result.output.take(100)}&quot;</span>
        }
        
<span class="fc" id="L199">        return &quot;&quot;&quot;</span>
            You are an AI assistant that evaluates whether a programming requirement has been completed.
            
<span class="fc" id="L202">            **Original Requirement**: $requirement</span>
            
            **Execution History**:
<span class="fc" id="L205">            $historyText</span>
            
            **Context**:
<span class="fc" id="L208">            - Project Path: ${context.projectPath}</span>
<span class="fc" id="L209">            - Language: ${context.language}</span>
<span class="fc" id="L210">            - Framework: ${context.framework}</span>
            
            Evaluate whether the original requirement has been successfully completed based on the execution history.
            
            Respond in JSON format:
            {
                &quot;completed&quot;: boolean,
                &quot;completionPercentage&quot;: number (0 to 100),
                &quot;missingItems&quot;: [&quot;string&quot;],
                &quot;summary&quot;: &quot;string&quot;,
                &quot;nextSteps&quot;: [&quot;string&quot;] (if not completed)
            }
<span class="fc" id="L222">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun parseAnalysisResponse(response: String): AnalysisResult {
<span class="fc" id="L226">        return try {</span>
<span class="fc" id="L227">            val jsonResponse = json.decodeFromString&lt;AnalysisResponseJson&gt;(response)</span>
<span class="fc" id="L228">            AnalysisResult.success(</span>
<span class="fc" id="L229">                intent = jsonResponse.intent,</span>
<span class="fc" id="L230">                complexity = RequirementComplexity.valueOf(jsonResponse.complexity),</span>
<span class="fc" id="L231">                category = RequirementCategory.valueOf(jsonResponse.category),</span>
<span class="fc" id="L232">                prerequisites = jsonResponse.prerequisites,</span>
<span class="fc" id="L233">                estimatedSteps = jsonResponse.estimatedSteps,</span>
<span class="fc" id="L234">                reasoning = jsonResponse.reasoning</span>
            )
<span class="fc" id="L236">        } catch (e: Exception) {</span>
<span class="fc" id="L237">            AnalysisResult.failure(&quot;Failed to parse analysis response: ${e.message}&quot;)</span>
        }
    }
    
    private fun parseActionDecisionResponse(response: String): ActionDecision {
<span class="fc" id="L242">        return try {</span>
<span class="fc" id="L243">            val jsonResponse = json.decodeFromString&lt;ActionDecisionResponseJson&gt;(response)</span>
<span class="pc bpc" id="L244" title="1 of 5 branches missed.">            when (jsonResponse.action) {</span>
<span class="fc" id="L245">                &quot;EXECUTE_TOOL&quot; -&gt; ActionDecision.executeTool(</span>
<span class="pc bpc" id="L246" title="1 of 2 branches missed.">                    toolName = jsonResponse.toolName ?: &quot;&quot;,</span>
<span class="pc bpc" id="L247" title="1 of 2 branches missed.">                    parameters = jsonResponse.parameters ?: emptyMap(),</span>
<span class="fc" id="L248">                    reasoning = jsonResponse.reasoning,</span>
<span class="fc" id="L249">                    confidence = jsonResponse.confidence</span>
                )
<span class="fc" id="L251">                &quot;COMPLETE&quot; -&gt; ActionDecision.complete(jsonResponse.reasoning)</span>
<span class="fc" id="L252">                &quot;WAIT_USER&quot; -&gt; ActionDecision.waitUser(jsonResponse.reasoning)</span>
<span class="fc" id="L253">                &quot;FAIL&quot; -&gt; ActionDecision.fail(jsonResponse.reasoning)</span>
<span class="pc" id="L254">                else -&gt; ActionDecision.failure(&quot;Unknown action: ${jsonResponse.action}&quot;)</span>
            }
<span class="nc" id="L256">        } catch (e: Exception) {</span>
<span class="pc" id="L257">            ActionDecision.failure(&quot;Failed to parse action decision response: ${e.message}&quot;)</span>
        }
    }
    
    private fun parseCompletionEvaluationResponse(response: String): CompletionEvaluation {
<span class="fc" id="L262">        return try {</span>
<span class="fc" id="L263">            val jsonResponse = json.decodeFromString&lt;CompletionEvaluationResponseJson&gt;(response)</span>
<span class="fc" id="L264">            CompletionEvaluation.success(</span>
<span class="fc" id="L265">                completed = jsonResponse.completed,</span>
<span class="fc" id="L266">                completionPercentage = jsonResponse.completionPercentage,</span>
<span class="fc" id="L267">                missingItems = jsonResponse.missingItems,</span>
<span class="fc" id="L268">                summary = jsonResponse.summary,</span>
<span class="fc" id="L269">                nextSteps = jsonResponse.nextSteps</span>
            )
<span class="fc" id="L271">        } catch (e: Exception) {</span>
<span class="fc" id="L272">            CompletionEvaluation.failure(&quot;Failed to parse completion evaluation response: ${e.message}&quot;)</span>
        }
    }
}

// JSON response data classes
<span class="pc bpc" id="L278" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L279">private data class AnalysisResponseJson(</span>
<span class="pc" id="L280">    val intent: String,</span>
<span class="pc" id="L281">    val complexity: String,</span>
<span class="pc" id="L282">    val category: String,</span>
<span class="pc" id="L283">    val prerequisites: List&lt;String&gt;,</span>
<span class="pc" id="L284">    val estimatedSteps: Int,</span>
<span class="pc" id="L285">    val reasoning: String</span>
)

<span class="pc bpc" id="L288" title="13 of 18 branches missed.">@Serializable</span>
<span class="nc" id="L289">private data class ActionDecisionResponseJson(</span>
<span class="pc" id="L290">    val action: String,</span>
<span class="pc" id="L291">    val toolName: String? = null,</span>
<span class="pc" id="L292">    val parameters: Map&lt;String, String&gt;? = null,</span>
<span class="pc" id="L293">    val reasoning: String,</span>
<span class="pc" id="L294">    val confidence: Double</span>
<span class="nc" id="L295">)</span>

<span class="pc bpc" id="L297" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L298">private data class CompletionEvaluationResponseJson(</span>
<span class="pc" id="L299">    val completed: Boolean,</span>
<span class="pc" id="L300">    val completionPercentage: Int,</span>
<span class="pc" id="L301">    val missingItems: List&lt;String&gt;,</span>
<span class="pc" id="L302">    val summary: String,</span>
<span class="pc" id="L303">    val nextSteps: List&lt;String&gt;</span>
)
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>