<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DefaultRequirementParser</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_class">DefaultRequirementParser</span></div><h1>DefaultRequirementParser</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">675 of 2,816</td><td class="ctr2">76%</td><td class="bar">180 of 422</td><td class="ctr2">57%</td><td class="ctr1">142</td><td class="ctr2">253</td><td class="ctr1">47</td><td class="ctr2">278</td><td class="ctr1">2</td><td class="ctr2">37</td></tr></tfoot><tbody><tr><td id="a15"><a href="RequirementParser.kt.html#L382" class="el_method">extractRefactorParameters(String, Map)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="158" alt="158"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="26" alt="26"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f0">14</td><td class="ctr2" id="g2">14</td><td class="ctr1" id="h0">20</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="RequirementParser.kt.html#L447" class="el_method">extractClassName(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="100" alt="100"/><img src="../jacoco-resources/greenbar.gif" width="88" height="10" title="284" alt="284"/></td><td class="ctr2" id="c25">73%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="66" height="10" title="20" alt="20"/></td><td class="ctr2" id="e20">55%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g0">19</td><td class="ctr1" id="h5">2</td><td class="ctr2" id="i1">20</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="RequirementParser.kt.html#L357" class="el_method">extractDocumentationParameters(String, Map)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="80" alt="80"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="43" alt="43"/></td><td class="ctr2" id="c34">34%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="e30">18%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g6">9</td><td class="ctr1" id="h1">8</td><td class="ctr2" id="i5">15</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a20"><a href="RequirementParser.kt.html#L262" class="el_method">extractTestParameters(String, Map)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="49" alt="49"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="40" alt="40"/></td><td class="ctr2" id="c30">44%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="e28">25%</td><td class="ctr1" id="f5">6</td><td class="ctr2" id="g14">7</td><td class="ctr1" id="h2">5</td><td class="ctr2" id="i8">13</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="RequirementParser.kt.html#L331" class="el_method">extractFormParameters(String, Map)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="32" alt="32"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="87" alt="87"/></td><td class="ctr2" id="c26">73%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="7" alt="7"/></td><td class="ctr2" id="e26">38%</td><td class="ctr1" id="f2">9</td><td class="ctr2" id="g5">10</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i6">15</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a11"><a href="RequirementParser.kt.html#L416" class="el_method">extractOptimizationParameters(String, Map)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="31" alt="31"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="37" alt="37"/></td><td class="ctr2" id="c28">54%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="e27">30%</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g18">6</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i10">12</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a16"><a href="RequirementParser.kt.html#L285" class="el_method">extractServiceParameters(String, Map)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="118" alt="118"/></td><td class="ctr2" id="c21">85%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="16" alt="16"/></td><td class="ctr2" id="e6">80%</td><td class="ctr1" id="f8">4</td><td class="ctr2" id="g4">11</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i4">16</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="RequirementParser.kt.html#L242" class="el_method">extractConfigParameters(String, Map)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="88" alt="88"/></td><td class="ctr2" id="c24">81%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="12" alt="12"/></td><td class="ctr2" id="e10">75%</td><td class="ctr1" id="f9">4</td><td class="ctr2" id="g7">9</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">12</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a27"><a href="RequirementParser.kt.html#L177" class="el_method">isFeatureAddition(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c31">35%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e31">16%</td><td class="ctr1" id="f16">3</td><td class="ctr2" id="g26">4</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i26">2</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a28"><a href="RequirementParser.kt.html#L182" class="el_method">isFeatureRemoval(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c32">35%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e32">16%</td><td class="ctr1" id="f17">3</td><td class="ctr2" id="g27">4</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i27">2</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a25"><a href="RequirementParser.kt.html#L187" class="el_method">isDependencyUpdate(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c33">35%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e33">16%</td><td class="ctr1" id="f18">3</td><td class="ctr2" id="g28">4</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i28">2</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a19"><a href="RequirementParser.kt.html#L515" class="el_method">extractTargetFile(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="19" alt="19"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f27">2</td><td class="ctr2" id="g30">2</td><td class="ctr1" id="h6">2</td><td class="ctr2" id="i29">2</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a3"><a href="RequirementParser.kt.html#L194" class="el_method">extractClassParameters(String, Map)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="80" alt="80"/></td><td class="ctr2" id="c23">81%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="11" alt="11"/></td><td class="ctr2" id="e8">78%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g11">8</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i9">13</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a32"><a href="RequirementParser.kt.html#L156" class="el_method">isRefactoring(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="31" alt="31"/></td><td class="ctr2" id="c27">63%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="e25">40%</td><td class="ctr1" id="f10">4</td><td class="ctr2" id="g19">6</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i18">3</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a9"><a href="RequirementParser.kt.html#L437" class="el_method">extractImprovementParameters(String, Map)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="15" alt="15"/></td><td class="ctr2" id="c29">45%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e29">25%</td><td class="ctr1" id="f28">2</td><td class="ctr2" id="g29">3</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i16">5</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a17"><a href="RequirementParser.kt.html#L312" class="el_method">extractSystemParameters(String, Map)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="92" alt="92"/></td><td class="ctr2" id="c18">90%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="8" alt="8"/></td><td class="ctr2" id="e21">50%</td><td class="ctr1" id="f4">8</td><td class="ctr2" id="g8">9</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i13">11</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a34"><a href="RequirementParser.kt.html#L138" class="el_method">isSystemCreation(String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="49" alt="49"/></td><td class="ctr2" id="c22">84%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="8" alt="8"/></td><td class="ctr2" id="e15">66%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g15">7</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i19">3</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a10"><a href="RequirementParser.kt.html#L48" class="el_method">extractIntent(String, Continuation)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="92" alt="92"/></td><td class="ctr2" id="c17">92%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="86" height="10" title="26" alt="26"/></td><td class="ctr2" id="e4">86%</td><td class="ctr1" id="f11">4</td><td class="ctr2" id="g1">16</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i2">18</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a12"><a href="RequirementParser.kt.html#L75" class="el_method">extractParameters(String, Intent, Continuation)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="86" alt="86"/></td><td class="ctr2" id="c16">92%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="43" height="10" title="13" alt="13"/></td><td class="ctr2" id="e5">81%</td><td class="ctr1" id="f21">3</td><td class="ctr2" id="g3">14</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i3">18</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a7"><a href="RequirementParser.kt.html#L487" class="el_method">extractEntityName(String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="110" alt="110"/></td><td class="ctr2" id="c14">94%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="6" alt="6"/></td><td class="ctr2" id="e19">60%</td><td class="ctr1" id="f12">4</td><td class="ctr2" id="g20">6</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i20">3</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a1"><a href="RequirementParser.kt.html#L217" class="el_method">extractApiParameters(String, Map)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="100" alt="100"/></td><td class="ctr2" id="c15">94%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="12" alt="12"/></td><td class="ctr2" id="e11">75%</td><td class="ctr1" id="f13">4</td><td class="ctr2" id="g9">9</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i7">14</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a22"><a href="RequirementParser.kt.html#L172" class="el_method">isBugFix(String)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="38" alt="38"/></td><td class="ctr2" id="c13">95%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="4" alt="4"/></td><td class="ctr2" id="e22">50%</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g22">5</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i30">2</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a18"><a href="RequirementParser.kt.html#L509" class="el_method">extractTargetClass(String)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="18" alt="18"/></td><td class="ctr2" id="c19">90%</td><td class="bar" id="d31"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e23">50%</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g31">2</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i31">2</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a13"><a href="RequirementParser.kt.html#L534" class="el_method">extractPerformanceGoal(String)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="18" alt="18"/></td><td class="ctr2" id="c20">90%</td><td class="bar" id="d32"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e24">50%</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g32">2</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i32">2</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a23"><a href="RequirementParser.kt.html#L108" class="el_method">isClassCreation(String)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="76" alt="76"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="10" alt="10"/></td><td class="ctr2" id="e16">62%</td><td class="ctr1" id="f6">6</td><td class="ctr2" id="g10">9</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i17">4</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a14"><a href="RequirementParser.kt.html#L494" class="el_method">extractProperties(String)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="71" alt="71"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d33"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f33">0</td><td class="ctr2" id="g33">2</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i14">8</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a21"><a href="RequirementParser.kt.html#L115" class="el_method">isApiCreation(String)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="67" alt="67"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="11" alt="11"/></td><td class="ctr2" id="e9">78%</td><td class="ctr1" id="f22">3</td><td class="ctr2" id="g12">8</td><td class="ctr1" id="h26">0</td><td class="ctr2" id="i21">3</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a35"><a href="RequirementParser.kt.html#L127" class="el_method">isTestCreation(String)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="67" alt="67"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="10" alt="10"/></td><td class="ctr2" id="e14">71%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g13">8</td><td class="ctr1" id="h27">0</td><td class="ctr2" id="i22">3</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a24"><a href="RequirementParser.kt.html#L121" class="el_method">isConfigCreation(String)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="58" alt="58"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="9" alt="9"/></td><td class="ctr2" id="e12">75%</td><td class="ctr1" id="f23">3</td><td class="ctr2" id="g16">7</td><td class="ctr1" id="h28">0</td><td class="ctr2" id="i23">3</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a26"><a href="RequirementParser.kt.html#L150" class="el_method">isDocumentationCreation(String)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="58" alt="58"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="9" alt="9"/></td><td class="ctr2" id="e13">75%</td><td class="ctr1" id="f24">3</td><td class="ctr2" id="g17">7</td><td class="ctr1" id="h29">0</td><td class="ctr2" id="i24">3</td><td class="ctr1" id="j29">0</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a6"><a href="RequirementParser.kt.html#L520" class="el_method">extractEntities(String)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="58" alt="58"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d34"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f34">0</td><td class="ctr2" id="g34">2</td><td class="ctr1" id="h30">0</td><td class="ctr2" id="i15">7</td><td class="ctr1" id="j30">0</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a36"><a href="RequirementParser.kt.html#L28" class="el_method">parse(String, Continuation)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="55" alt="55"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d35"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f35">0</td><td class="ctr2" id="g35">2</td><td class="ctr1" id="h31">0</td><td class="ctr2" id="i12">12</td><td class="ctr1" id="j31">0</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a29"><a href="RequirementParser.kt.html#L144" class="el_method">isFormCreation(String)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="49" alt="49"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="8" alt="8"/></td><td class="ctr2" id="e7">80%</td><td class="ctr1" id="f29">2</td><td class="ctr2" id="g21">6</td><td class="ctr1" id="h32">0</td><td class="ctr2" id="i25">3</td><td class="ctr1" id="j32">0</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a33"><a href="RequirementParser.kt.html#L133" class="el_method">isServiceCreation(String)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="40" alt="40"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="7" alt="7"/></td><td class="ctr2" id="e3">87%</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g23">5</td><td class="ctr1" id="h33">0</td><td class="ctr2" id="i33">2</td><td class="ctr1" id="j33">0</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a30"><a href="RequirementParser.kt.html#L162" class="el_method">isImprovement(String)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="40" alt="40"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="5" alt="5"/></td><td class="ctr2" id="e17">62%</td><td class="ctr1" id="f25">3</td><td class="ctr2" id="g24">5</td><td class="ctr1" id="h34">0</td><td class="ctr2" id="i34">2</td><td class="ctr1" id="j34">0</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a31"><a href="RequirementParser.kt.html#L167" class="el_method">isOptimization(String)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="40" alt="40"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="5" alt="5"/></td><td class="ctr2" id="e18">62%</td><td class="ctr1" id="f26">3</td><td class="ctr2" id="g25">5</td><td class="ctr1" id="h35">0</td><td class="ctr2" id="i35">2</td><td class="ctr1" id="j35">0</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a0"><a href="RequirementParser.kt.html#L26" class="el_method">DefaultRequirementParser()</a></td><td class="bar" id="b36"/><td class="ctr2" id="c12">100%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">0</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">0</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j36">0</td><td class="ctr2" id="k36">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>