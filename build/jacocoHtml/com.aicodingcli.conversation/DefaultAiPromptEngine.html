<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DefaultAiPromptEngine</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_class">DefaultAiPromptEngine</span></div><h1>DefaultAiPromptEngine</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">24 of 413</td><td class="ctr2">94%</td><td class="bar">4 of 13</td><td class="ctr2">69%</td><td class="ctr1">4</td><td class="ctr2">23</td><td class="ctr1">3</td><td class="ctr2">99</td><td class="ctr1">0</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a12"><a href="AiPromptEngine.kt.html#L242" class="el_method">parseActionDecisionResponse(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="88" height="10" title="45" alt="45"/></td><td class="ctr2" id="c14">73%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">66%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i0">14</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a11"><a href="AiPromptEngine.kt.html#L84" class="el_method">evaluateCompletion(String, List, ExecutionContext, Continuation)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="76" height="10" title="39" alt="39"/></td><td class="ctr2" id="c13">84%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h0">2</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="AiPromptEngine.kt.html#L196" class="el_method">buildCompletionEvaluationPrompt$lambda$3(ExecutionStep)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="17" alt="17"/></td><td class="ctr2" id="c12">94%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="AiPromptEngine.kt.html#L63" class="el_method">decideNextAction(String, List, List, ExecutionContext, Continuation)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="92" height="10" title="47" alt="47"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="AiPromptEngine.kt.html#L47" class="el_method">analyzeRequirement(String, ExecutionContext, Continuation)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="88" height="10" title="45" alt="45"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="AiPromptEngine.kt.html#L141" class="el_method">buildActionDecisionPrompt(String, List, List, ExecutionContext)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="88" height="10" title="45" alt="45"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a13"><a href="AiPromptEngine.kt.html#L226" class="el_method">parseAnalysisResponse(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="68" height="10" title="35" alt="35"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a14"><a href="AiPromptEngine.kt.html#L262" class="el_method">parseCompletionEvaluationResponse(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="31" alt="31"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i6">10</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="AiPromptEngine.kt.html#L195" class="el_method">buildCompletionEvaluationPrompt(String, List, ExecutionContext)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="47" height="10" title="24" alt="24"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="AiPromptEngine.kt.html#L42" class="el_method">DefaultAiPromptEngine(AiService, Json, int, DefaultConstructorMarker)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="14" alt="14"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a2"><a href="AiPromptEngine.kt.html#L145" class="el_method">buildActionDecisionPrompt$lambda$1(ExecutionStep)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="14" alt="14"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a5"><a href="AiPromptEngine.kt.html#L105" class="el_method">buildAnalysisPrompt(String, ExecutionContext)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="12" alt="12"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="AiPromptEngine.kt.html#L42" class="el_method">DefaultAiPromptEngine(AiService, Json)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="9" alt="9"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a3"><a href="AiPromptEngine.kt.html#L150" class="el_method">buildActionDecisionPrompt$lambda$2(ToolMetadata)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="7" alt="7"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a0"><a href="AiPromptEngine.kt.html#L44" class="el_method">_init_$lambda$0(JsonBuilder)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>