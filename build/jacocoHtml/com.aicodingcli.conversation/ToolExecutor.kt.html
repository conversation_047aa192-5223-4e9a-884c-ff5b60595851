<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ToolExecutor.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">ToolExecutor.kt</span></div><h1>ToolExecutor.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

import com.aicodingcli.conversation.tools.*
import java.time.Instant
import kotlin.time.measureTime

/**
 * Interface for executing tools
 */
interface ToolExecutor {
    /**
     * Execute a tool call
     */
    suspend fun execute(tool: ToolCall): ToolResult
    
    /**
     * Get supported tools metadata
     */
    fun getSupportedTools(): List&lt;ToolMetadata&gt;
    
    /**
     * Validate tool parameters
     */
    suspend fun validateParameters(tool: ToolCall): ValidationResult
}

/**
 * Default implementation of ToolExecutor using handler-based architecture
 */
<span class="fc" id="L30">class DefaultToolExecutor(</span>
<span class="pc" id="L31">    private val workingDirectory: String = System.getProperty(&quot;user.dir&quot;),</span>
<span class="fc" id="L32">    private val handlerRegistry: ToolHandlerRegistry = DefaultToolHandlerFactory.createDefaultRegistry(),</span>
<span class="fc" id="L33">    private val metadataManager: ExecutionMetadataManager = ExecutionMetadataManager(),</span>
<span class="fc" id="L34">    private val statsCollector: ToolExecutionStatsCollector = ToolExecutionStatsCollector()</span>
<span class="fc" id="L35">) : ToolExecutor {</span>
    
<span class="fc" id="L37">    override suspend fun execute(tool: ToolCall): ToolResult {</span>
<span class="fc" id="L38">        val startTime = Instant.now()</span>

<span class="fc" id="L40">        try {</span>
            // Get handler for the tool
<span class="fc bfc" id="L42" title="All 2 branches covered.">            val handler = handlerRegistry.getHandler(tool.toolName)</span>
<span class="fc" id="L43">                ?: return ToolResult.failure(&quot;Unsupported tool: ${tool.toolName}&quot;)</span>

            // Measure execution time and execute the tool
<span class="fc" id="L46">            var result: ToolResult</span>
<span class="fc" id="L47">            val executionTime = measureTime {</span>
<span class="fc" id="L48">                result = handler.execute(tool.parameters, workingDirectory)</span>
<span class="fc" id="L49">            }</span>

            // Record statistics
<span class="fc" id="L52">            statsCollector.recordExecution(</span>
<span class="fc" id="L53">                toolName = tool.toolName,</span>
<span class="fc" id="L54">                success = result.success,</span>
<span class="fc" id="L55">                executionTime = executionTime</span>
            )

            // Enhance with metadata
<span class="fc" id="L59">            return metadataManager.enhanceWithMetadata(tool, result, startTime, executionTime)</span>

<span class="nc" id="L61">        } catch (e: Exception) {</span>
<span class="nc" id="L62">            val executionTime = measureTime { /* Exception occurred */ }</span>
<span class="nc" id="L63">            statsCollector.recordExecution(</span>
<span class="nc" id="L64">                toolName = tool.toolName,</span>
<span class="nc" id="L65">                success = false,</span>
<span class="nc" id="L66">                executionTime = executionTime</span>
            )

<span class="nc" id="L69">            return ToolResult.failure(</span>
<span class="nc" id="L70">                error = &quot;Tool execution failed: ${e.message}&quot;,</span>
<span class="nc" id="L71">                output = e.stackTraceToString()</span>
            )
        }
    }
    
    override fun getSupportedTools(): List&lt;ToolMetadata&gt; {
<span class="fc" id="L77">        return handlerRegistry.getAllToolMetadata()</span>
    }
    
    override suspend fun validateParameters(tool: ToolCall): ValidationResult {
<span class="pc bpc" id="L81" title="1 of 2 branches missed.">        val handler = handlerRegistry.getHandler(tool.toolName)</span>
<span class="nc" id="L82">            ?: return ValidationResult.invalid(&quot;Unsupported tool: ${tool.toolName}&quot;)</span>

<span class="fc" id="L84">        return handler.validateParameters(tool.parameters)</span>
    }

    /**
     * Get execution statistics for tools
     */
    fun getExecutionStats(): List&lt;ToolExecutionStats&gt; {
<span class="fc" id="L91">        return statsCollector.getAllStats()</span>
    }

    /**
     * Get execution statistics for a specific tool
     */
    fun getExecutionStatsForTool(toolName: String): ToolExecutionStats? {
<span class="fc" id="L98">        return statsCollector.getStatsForTool(toolName)</span>
    }

    /**
     * Clear execution statistics
     */
    fun clearExecutionStats() {
<span class="fc" id="L105">        statsCollector.clearStats()</span>
<span class="fc" id="L106">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>