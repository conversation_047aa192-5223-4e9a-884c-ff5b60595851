<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AutoExecutionEngine.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">AutoExecutionEngine.kt</span></div><h1>AutoExecutionEngine.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

import kotlin.time.measureTime

/**
 * Interface for automatic execution of conversations
 */
interface AutoExecutionEngine {
    /**
     * Execute a conversation from a requirement
     */
    suspend fun executeConversation(requirement: String): ExecutionResult
    
    /**
     * Continue an existing conversation
     */
    suspend fun continueConversation(sessionId: String): ExecutionResult
    
    /**
     * Execute a single step
     */
    suspend fun executeStep(step: ExecutionStep): StepResult
    
    /**
     * Set maximum execution rounds
     */
    fun setMaxExecutionRounds(maxRounds: Int)
}

/**
 * Default implementation of AutoExecutionEngine
 */
<span class="pc" id="L33">class DefaultAutoExecutionEngine(</span>
<span class="fc" id="L34">    private val conversationStateManager: ConversationStateManager,</span>
<span class="fc" id="L35">    private val taskDecomposer: TaskDecomposer,</span>
<span class="fc" id="L36">    private val requirementParser: RequirementParser,</span>
<span class="fc" id="L37">    private val toolExecutor: ToolExecutor,</span>
<span class="pc" id="L38">    private var maxExecutionRounds: Int = 25</span>
<span class="nc" id="L39">) : AutoExecutionEngine {</span>
    
<span class="fc" id="L41">    override suspend fun executeConversation(requirement: String): ExecutionResult {</span>
<span class="fc" id="L42">        val startTime = kotlin.time.TimeSource.Monotonic.markNow()</span>

<span class="fc" id="L44">        try {</span>


            // Handle empty requirement
<span class="fc bfc" id="L48" title="All 2 branches covered.">            if (requirement.isBlank()) {</span>

<span class="fc" id="L50">                return ExecutionResult.success(</span>
<span class="fc" id="L51">                    sessionId = &quot;empty-session&quot;,</span>
<span class="fc" id="L52">                    finalStatus = ConversationStatus.COMPLETED,</span>
<span class="fc" id="L53">                    executedSteps = emptyList(),</span>
<span class="fc" id="L54">                    executionRounds = 0,</span>
<span class="fc" id="L55">                    executionTime = startTime.elapsedNow(),</span>
<span class="fc" id="L56">                    summary = &quot;Empty requirement - no action taken&quot;</span>
                )
            }

            // Create conversation session
<span class="fc" id="L61">            val session = conversationStateManager.createSession(requirement)</span>
            
            // Parse requirement (for future use)
<span class="fc" id="L64">            requirementParser.parse(requirement)</span>
            
            // Create project context (simplified for now)
<span class="fc" id="L67">            val projectContext = ProjectContext(</span>
<span class="fc" id="L68">                projectPath = &quot;&quot;,  // Use empty path so files are created in working directory</span>
<span class="fc" id="L69">                language = &quot;kotlin&quot;,</span>
<span class="fc" id="L70">                framework = &quot;spring-boot&quot;,</span>
<span class="fc" id="L71">                buildTool = &quot;gradle&quot;</span>
            )
            
            // Decompose into tasks
<span class="fc" id="L75">            val tasks = taskDecomposer.decompose(requirement, projectContext)</span>

            // Update session with tasks
<span class="fc" id="L78">            val sessionWithTasks = session.withTasks(tasks)</span>

            // Save the session with tasks
<span class="fc" id="L81">            conversationStateManager.updateSession(sessionWithTasks)</span>

            // Then update the status
<span class="fc" id="L84">            val updatedSession = conversationStateManager.updateState(</span>
<span class="fc" id="L85">                sessionWithTasks.id,</span>
<span class="fc" id="L86">                sessionWithTasks.state.copy(status = ConversationStatus.PLANNING)</span>
            )

            // Execute tasks
<span class="fc" id="L90">            return executeTasksInSession(updatedSession, startTime)</span>
            
<span class="pc" id="L92">        } catch (e: Exception) {</span>
<span class="nc" id="L93">            return ExecutionResult.failure(</span>
<span class="nc" id="L94">                sessionId = null,</span>
<span class="nc" id="L95">                finalStatus = ConversationStatus.FAILED,</span>
<span class="nc" id="L96">                executedSteps = emptyList(),</span>
<span class="nc" id="L97">                executionRounds = 0,</span>
<span class="nc" id="L98">                executionTime = startTime.elapsedNow(),</span>
<span class="nc" id="L99">                error = &quot;Failed to execute conversation: ${e.message}&quot;</span>
            )
        }
    }
    
    override suspend fun continueConversation(sessionId: String): ExecutionResult {
<span class="fc" id="L105">        val startTime = kotlin.time.TimeSource.Monotonic.markNow()</span>
        
<span class="fc" id="L107">        try {</span>
<span class="pc bpc" id="L108" title="1 of 2 branches missed.">            val session = conversationStateManager.getSession(sessionId)</span>
<span class="nc" id="L109">                ?: return ExecutionResult.failure(</span>
<span class="nc" id="L110">                    sessionId = sessionId,</span>
<span class="nc" id="L111">                    finalStatus = ConversationStatus.FAILED,</span>
<span class="nc" id="L112">                    executedSteps = emptyList(),</span>
<span class="nc" id="L113">                    executionRounds = 0,</span>
<span class="nc" id="L114">                    executionTime = startTime.elapsedNow(),</span>
<span class="nc" id="L115">                    error = &quot;Session not found: $sessionId&quot;</span>
                )
            
<span class="fc" id="L118">            return executeTasksInSession(session, startTime)</span>
            
<span class="pc" id="L120">        } catch (e: Exception) {</span>
<span class="nc" id="L121">            return ExecutionResult.failure(</span>
<span class="nc" id="L122">                sessionId = sessionId,</span>
<span class="nc" id="L123">                finalStatus = ConversationStatus.FAILED,</span>
<span class="nc" id="L124">                executedSteps = emptyList(),</span>
<span class="nc" id="L125">                executionRounds = 0,</span>
<span class="nc" id="L126">                executionTime = startTime.elapsedNow(),</span>
<span class="nc" id="L127">                error = &quot;Failed to continue conversation: ${e.message}&quot;</span>
            )
        }
    }
    
<span class="fc" id="L132">    override suspend fun executeStep(step: ExecutionStep): StepResult {</span>
<span class="fc" id="L133">        try {</span>
<span class="fc" id="L134">            val toolResult = toolExecutor.execute(step.toolCall)</span>
<span class="fc" id="L135">            return StepResult.success(toolResult)</span>
<span class="nc" id="L136">        } catch (e: Exception) {</span>
<span class="nc" id="L137">            return StepResult.failure(&quot;Failed to execute step: ${e.message}&quot;)</span>
        }
    }
    
    override fun setMaxExecutionRounds(maxRounds: Int) {
<span class="fc bfc" id="L142" title="All 2 branches covered.">        if (maxRounds &lt;= 0) {</span>
<span class="fc" id="L143">            throw IllegalArgumentException(&quot;Max execution rounds must be positive, got: $maxRounds&quot;)</span>
        }
<span class="fc" id="L145">        this.maxExecutionRounds = maxRounds</span>
<span class="fc" id="L146">    }</span>
    
<span class="pc" id="L148">    private suspend fun executeTasksInSession(</span>
        session: ConversationSession,
        startTime: kotlin.time.TimeMark
    ): ExecutionResult {
<span class="fc" id="L152">        val executedSteps = mutableListOf&lt;ExecutionStep&gt;()</span>
<span class="fc" id="L153">        var currentSession = session</span>
<span class="fc" id="L154">        var executionRounds = currentSession.state.executionRound</span>
        
<span class="fc" id="L156">        try {</span>
            // Update status to executing
<span class="fc" id="L158">            currentSession = conversationStateManager.updateState(</span>
<span class="fc" id="L159">                currentSession.id,</span>
<span class="fc" id="L160">                currentSession.state.copy(status = ConversationStatus.EXECUTING)</span>
            )
            
            // Execute tasks
<span class="fc bfc" id="L164" title="All 2 branches covered.">            for (task in currentSession.tasks) {</span>
<span class="pc bpc" id="L165" title="1 of 2 branches missed.">                if (executionRounds &gt;= maxExecutionRounds) {</span>
                    // Reached max rounds, pause execution
<span class="nc" id="L167">                    val pausedSession = conversationStateManager.updateState(</span>
<span class="nc" id="L168">                        currentSession.id,</span>
<span class="nc" id="L169">                        currentSession.state.copy(</span>
<span class="nc" id="L170">                            status = ConversationStatus.WAITING_USER,</span>
<span class="nc" id="L171">                            executionRound = executionRounds</span>
                        )
                    )
                    
<span class="nc" id="L175">                    return ExecutionResult.success(</span>
<span class="nc" id="L176">                        sessionId = pausedSession.id,</span>
<span class="nc" id="L177">                        finalStatus = ConversationStatus.WAITING_USER,</span>
<span class="nc" id="L178">                        executedSteps = executedSteps,</span>
<span class="nc" id="L179">                        executionRounds = executionRounds,</span>
<span class="nc" id="L180">                        executionTime = startTime.elapsedNow(),</span>
<span class="nc" id="L181">                        summary = &quot;Execution paused after $executionRounds rounds. Use continueConversation to resume.&quot;</span>
                    )
                }
                
                // Execute each tool call in the task
<span class="fc bfc" id="L186" title="All 2 branches covered.">                for (toolCall in task.toolCalls) {</span>
<span class="fc" id="L187">                    executionRounds++</span>
                    
<span class="fc" id="L189">                    val stepStartTime = kotlin.time.TimeSource.Monotonic.markNow()</span>
<span class="fc" id="L190">                    val toolResult = toolExecutor.execute(toolCall)</span>
<span class="fc" id="L191">                    val stepDuration = stepStartTime.elapsedNow()</span>
                    
<span class="fc" id="L193">                    val executionStep = ExecutionStep(</span>
<span class="fc" id="L194">                        taskId = task.id,</span>
<span class="fc" id="L195">                        toolCall = toolCall,</span>
<span class="fc" id="L196">                        result = toolResult,</span>
<span class="fc" id="L197">                        duration = stepDuration</span>
                    )
                    
<span class="fc" id="L200">                    executedSteps.add(executionStep)</span>
<span class="fc" id="L201">                    currentSession = currentSession.addExecutionStep(executionStep)</span>
                    
                    // Check if tool execution failed
<span class="fc bfc" id="L204" title="All 2 branches covered.">                    if (!toolResult.success) {</span>
<span class="fc" id="L205">                        val failedSession = conversationStateManager.updateState(</span>
<span class="fc" id="L206">                            currentSession.id,</span>
<span class="fc" id="L207">                            currentSession.state.copy(</span>
<span class="fc" id="L208">                                status = ConversationStatus.FAILED,</span>
<span class="fc" id="L209">                                executionRound = executionRounds</span>
<span class="fc" id="L210">                            ).withError(ExecutionError(</span>
<span class="fc" id="L211">                                message = &quot;Tool execution failed: ${toolResult.error}&quot;,</span>
<span class="fc" id="L212">                                code = &quot;TOOL_EXECUTION_FAILED&quot;</span>
                            ))
                        )
                        
<span class="fc" id="L216">                        return ExecutionResult.failure(</span>
<span class="fc" id="L217">                            sessionId = failedSession.id,</span>
<span class="fc" id="L218">                            finalStatus = ConversationStatus.FAILED,</span>
<span class="fc" id="L219">                            executedSteps = executedSteps,</span>
<span class="fc" id="L220">                            executionRounds = executionRounds,</span>
<span class="fc" id="L221">                            executionTime = startTime.elapsedNow(),</span>
<span class="pc bpc" id="L222" title="1 of 2 branches missed.">                            error = toolResult.error ?: &quot;Tool execution failed&quot;</span>
                        )
                    }
                    
                    // Check max rounds again after each tool call
<span class="fc bfc" id="L227" title="All 2 branches covered.">                    if (executionRounds &gt;= maxExecutionRounds) {</span>
<span class="fc" id="L228">                        val pausedSession = conversationStateManager.updateState(</span>
<span class="fc" id="L229">                            currentSession.id,</span>
<span class="fc" id="L230">                            currentSession.state.copy(</span>
<span class="fc" id="L231">                                status = ConversationStatus.WAITING_USER,</span>
<span class="fc" id="L232">                                executionRound = executionRounds</span>
                            )
                        )
                        
<span class="fc" id="L236">                        return ExecutionResult.success(</span>
<span class="fc" id="L237">                            sessionId = pausedSession.id,</span>
<span class="fc" id="L238">                            finalStatus = ConversationStatus.WAITING_USER,</span>
<span class="fc" id="L239">                            executedSteps = executedSteps,</span>
<span class="fc" id="L240">                            executionRounds = executionRounds,</span>
<span class="fc" id="L241">                            executionTime = startTime.elapsedNow(),</span>
<span class="fc" id="L242">                            summary = &quot;Execution paused after $executionRounds rounds. Use continueConversation to resume.&quot;</span>
                        )
                    }
                }
            }
            
            // All tasks completed successfully
<span class="fc" id="L249">            val completedSession = conversationStateManager.updateState(</span>
<span class="fc" id="L250">                currentSession.id,</span>
<span class="fc" id="L251">                currentSession.state.copy(</span>
<span class="fc" id="L252">                    status = ConversationStatus.COMPLETED,</span>
<span class="fc" id="L253">                    executionRound = executionRounds</span>
                )
            )
            
<span class="fc" id="L257">            return ExecutionResult.success(</span>
<span class="fc" id="L258">                sessionId = completedSession.id,</span>
<span class="fc" id="L259">                finalStatus = ConversationStatus.COMPLETED,</span>
<span class="fc" id="L260">                executedSteps = executedSteps,</span>
<span class="fc" id="L261">                executionRounds = executionRounds,</span>
<span class="fc" id="L262">                executionTime = startTime.elapsedNow(),</span>
<span class="fc" id="L263">                summary = &quot;Successfully completed ${currentSession.tasks.size} tasks in $executionRounds rounds&quot;</span>
            )
            
<span class="nc" id="L266">        } catch (e: Exception) {</span>
<span class="nc" id="L267">            val failedSession = conversationStateManager.updateState(</span>
<span class="nc" id="L268">                currentSession.id,</span>
<span class="nc" id="L269">                currentSession.state.copy(</span>
<span class="nc" id="L270">                    status = ConversationStatus.FAILED,</span>
<span class="nc" id="L271">                    executionRound = executionRounds</span>
<span class="nc" id="L272">                ).withError(ExecutionError(</span>
<span class="nc" id="L273">                    message = &quot;Execution failed: ${e.message}&quot;,</span>
<span class="nc" id="L274">                    code = &quot;EXECUTION_ERROR&quot;</span>
                ))
            )
            
<span class="nc" id="L278">            return ExecutionResult.failure(</span>
<span class="nc" id="L279">                sessionId = failedSession.id,</span>
<span class="nc" id="L280">                finalStatus = ConversationStatus.FAILED,</span>
<span class="nc" id="L281">                executedSteps = executedSteps,</span>
<span class="nc" id="L282">                executionRounds = executionRounds,</span>
<span class="nc" id="L283">                executionTime = startTime.elapsedNow(),</span>
<span class="nc" id="L284">                error = &quot;Execution failed: ${e.message}&quot;</span>
            )
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>