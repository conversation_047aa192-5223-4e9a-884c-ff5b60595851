<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConversationSerializers.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">ConversationSerializers.kt</span></div><h1>ConversationSerializers.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import java.time.Duration
import java.time.Instant
import kotlin.time.Duration.Companion.milliseconds

/**
 * Serializer for Instant objects
 */
object InstantSerializer : KSerializer&lt;Instant&gt; {
<span class="pc" id="L17">    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor(&quot;Instant&quot;, PrimitiveKind.LONG)</span>

    override fun serialize(encoder: Encoder, value: Instant) {
<span class="fc" id="L20">        encoder.encodeLong(value.epochSecond)</span>
<span class="fc" id="L21">    }</span>

    override fun deserialize(decoder: Decoder): Instant {
<span class="fc" id="L24">        return Instant.ofEpochSecond(decoder.decodeLong())</span>
    }
}

/**
 * Serializer for java.time.Duration objects
 */
object DurationSerializer : KSerializer&lt;Duration&gt; {
<span class="nc" id="L32">    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor(&quot;Duration&quot;, PrimitiveKind.LONG)</span>

    override fun serialize(encoder: Encoder, value: Duration) {
<span class="nc" id="L35">        encoder.encodeLong(value.toMillis())</span>
<span class="nc" id="L36">    }</span>

    override fun deserialize(decoder: Decoder): Duration {
<span class="nc" id="L39">        return Duration.ofMillis(decoder.decodeLong())</span>
    }
}

/**
 * Serializer for kotlin.time.Duration objects
 */
object KotlinDurationSerializer : KSerializer&lt;kotlin.time.Duration&gt; {
<span class="pc" id="L47">    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor(&quot;KotlinDuration&quot;, PrimitiveKind.LONG)</span>

    override fun serialize(encoder: Encoder, value: kotlin.time.Duration) {
<span class="fc" id="L50">        encoder.encodeLong(value.inWholeMilliseconds)</span>
<span class="fc" id="L51">    }</span>

    override fun deserialize(decoder: Decoder): kotlin.time.Duration {
<span class="nc" id="L54">        return decoder.decodeLong().milliseconds</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>