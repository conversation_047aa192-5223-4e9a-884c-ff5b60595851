<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RequirementParser.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">RequirementParser.kt</span></div><h1>RequirementParser.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

/**
 * Interface for parsing natural language requirements
 */
interface RequirementParser {
    /**
     * Parse a requirement into intent and parameters
     */
    suspend fun parse(requirement: String): ParsedRequirement
    
    /**
     * Extract intent from requirement
     */
    suspend fun extractIntent(requirement: String): Intent
    
    /**
     * Extract parameters based on intent
     */
    suspend fun extractParameters(requirement: String, intent: Intent): Map&lt;String, String&gt;
}

/**
 * Default implementation of RequirementParser
 */
<span class="fc" id="L26">class DefaultRequirementParser : RequirementParser {</span>
    
<span class="fc" id="L28">    override suspend fun parse(requirement: String): ParsedRequirement {</span>
<span class="fc bfc" id="L29" title="All 2 branches covered.">        if (requirement.isBlank()) {</span>
<span class="fc" id="L30">            return ParsedRequirement(</span>
<span class="fc" id="L31">                intent = Intent.UNKNOWN,</span>
<span class="fc" id="L32">                parameters = emptyMap(),</span>
<span class="fc" id="L33">                originalText = requirement</span>
            )
        }
        
<span class="fc" id="L37">        val intent = extractIntent(requirement)</span>
<span class="fc" id="L38">        val parameters = extractParameters(requirement, intent)</span>
        
<span class="fc" id="L40">        return ParsedRequirement(</span>
<span class="fc" id="L41">            intent = intent,</span>
<span class="fc" id="L42">            parameters = parameters,</span>
<span class="fc" id="L43">            originalText = requirement</span>
        )
    }
    
    override suspend fun extractIntent(requirement: String): Intent {
<span class="fc" id="L48">        val requirementLower = requirement.lowercase()</span>

<span class="fc" id="L50">        return when {</span>
            // Order matters - more specific patterns first
<span class="fc bfc" id="L52" title="All 2 branches covered.">            isTestCreation(requirementLower) -&gt; Intent.CREATE_TESTS</span>
<span class="fc bfc" id="L53" title="All 2 branches covered.">            isDocumentationCreation(requirementLower) -&gt; Intent.CREATE_DOCUMENTATION</span>
<span class="fc bfc" id="L54" title="All 2 branches covered.">            isSystemCreation(requirementLower) -&gt; Intent.CREATE_SYSTEM</span>
<span class="fc bfc" id="L55" title="All 2 branches covered.">            isFormCreation(requirementLower) -&gt; Intent.CREATE_FORM</span>
<span class="fc bfc" id="L56" title="All 2 branches covered.">            isServiceCreation(requirementLower) -&gt; Intent.CREATE_SERVICE</span>
<span class="fc bfc" id="L57" title="All 2 branches covered.">            isConfigCreation(requirementLower) -&gt; Intent.CREATE_CONFIG</span>
<span class="fc bfc" id="L58" title="All 2 branches covered.">            isApiCreation(requirementLower) -&gt; Intent.CREATE_API</span>
<span class="fc bfc" id="L59" title="All 2 branches covered.">            isClassCreation(requirementLower) -&gt; Intent.CREATE_CLASS</span>

            // Modification patterns
<span class="fc bfc" id="L62" title="All 2 branches covered.">            isRefactoring(requirementLower) -&gt; Intent.REFACTOR_CODE</span>
<span class="fc bfc" id="L63" title="All 2 branches covered.">            isOptimization(requirementLower) -&gt; Intent.OPTIMIZE_PERFORMANCE</span>
<span class="fc bfc" id="L64" title="All 2 branches covered.">            isImprovement(requirementLower) -&gt; Intent.IMPROVE_CODE</span>
<span class="pc bpc" id="L65" title="1 of 2 branches missed.">            isBugFix(requirementLower) -&gt; Intent.FIX_BUG</span>
<span class="pc bpc" id="L66" title="1 of 2 branches missed.">            isFeatureAddition(requirementLower) -&gt; Intent.ADD_FEATURE</span>
<span class="pc bpc" id="L67" title="1 of 2 branches missed.">            isFeatureRemoval(requirementLower) -&gt; Intent.REMOVE_FEATURE</span>
<span class="pc bpc" id="L68" title="1 of 2 branches missed.">            isDependencyUpdate(requirementLower) -&gt; Intent.UPDATE_DEPENDENCY</span>

<span class="fc" id="L70">            else -&gt; Intent.UNKNOWN</span>
        }
    }
    
    override suspend fun extractParameters(requirement: String, intent: Intent): Map&lt;String, String&gt; {
<span class="fc" id="L75">        val parameters = mutableMapOf&lt;String, String&gt;()</span>
<span class="fc" id="L76">        val requirementLower = requirement.lowercase()</span>
        
        // Add default language
<span class="fc" id="L79">        parameters[&quot;language&quot;] = &quot;kotlin&quot;</span>
        
<span class="pc bpc" id="L81" title="1 of 12 branches missed.">        when (intent) {</span>
<span class="fc" id="L82">            Intent.CREATE_CLASS -&gt; extractClassParameters(requirement, parameters)</span>
<span class="fc" id="L83">            Intent.CREATE_API -&gt; extractApiParameters(requirement, parameters)</span>
<span class="fc" id="L84">            Intent.CREATE_CONFIG -&gt; extractConfigParameters(requirement, parameters)</span>
<span class="fc" id="L85">            Intent.CREATE_TESTS -&gt; extractTestParameters(requirement, parameters)</span>
<span class="fc" id="L86">            Intent.CREATE_SERVICE -&gt; extractServiceParameters(requirement, parameters)</span>
<span class="fc" id="L87">            Intent.CREATE_SYSTEM -&gt; extractSystemParameters(requirement, parameters)</span>
<span class="fc" id="L88">            Intent.CREATE_FORM -&gt; extractFormParameters(requirement, parameters)</span>
<span class="fc" id="L89">            Intent.CREATE_DOCUMENTATION -&gt; extractDocumentationParameters(requirement, parameters)</span>
<span class="nc" id="L90">            Intent.REFACTOR_CODE -&gt; extractRefactorParameters(requirement, parameters)</span>
<span class="fc" id="L91">            Intent.OPTIMIZE_PERFORMANCE -&gt; extractOptimizationParameters(requirement, parameters)</span>
<span class="fc" id="L92">            Intent.IMPROVE_CODE -&gt; extractImprovementParameters(requirement, parameters)</span>
            else -&gt; {
                // For unknown intents, try to extract general parameters
<span class="pc bpc" id="L95" title="2 of 4 branches missed.">                if (requirementLower.isEmpty()) {</span>
                    // Empty requirement
                } else {
<span class="fc" id="L98">                    parameters[&quot;scope&quot;] = &quot;general&quot;</span>
                }
            }
        }
        
<span class="fc" id="L103">        return parameters</span>
    }
    
    // Intent detection methods
    private fun isClassCreation(requirement: String): Boolean {
<span class="pc bpc" id="L108" title="1 of 4 branches missed.">        return (requirement.contains(&quot;create&quot;) || requirement.contains(&quot;generate&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L109" title="1 of 4 branches missed.">               (requirement.contains(&quot;class&quot;) || requirement.contains(&quot;interface&quot;) || </span>
<span class="pc bpc" id="L110" title="2 of 4 branches missed.">                requirement.contains(&quot;entity&quot;) || requirement.contains(&quot;model&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L111" title="2 of 4 branches missed.">               !requirement.contains(&quot;api&quot;) &amp;&amp; !requirement.contains(&quot;endpoint&quot;)</span>
    }
    
    private fun isApiCreation(requirement: String): Boolean {
<span class="pc bpc" id="L115" title="1 of 6 branches missed.">        return (requirement.contains(&quot;create&quot;) || requirement.contains(&quot;build&quot;) || requirement.contains(&quot;generate&quot;)) &amp;&amp;</span>
<span class="fc bfc" id="L116" title="All 4 branches covered.">               (requirement.contains(&quot;api&quot;) || requirement.contains(&quot;endpoint&quot;) || </span>
<span class="pc bpc" id="L117" title="2 of 4 branches missed.">                requirement.contains(&quot;rest&quot;) || requirement.contains(&quot;graphql&quot;))</span>
    }
    
    private fun isConfigCreation(requirement: String): Boolean {
<span class="pc bpc" id="L121" title="1 of 4 branches missed.">        return (requirement.contains(&quot;create&quot;) || requirement.contains(&quot;generate&quot;)) &amp;&amp;</span>
<span class="fc bfc" id="L122" title="All 4 branches covered.">               (requirement.contains(&quot;config&quot;) || requirement.contains(&quot;properties&quot;) || </span>
<span class="pc bpc" id="L123" title="2 of 4 branches missed.">                requirement.contains(&quot;settings&quot;) || requirement.contains(&quot;configuration&quot;))</span>
    }
    
    private fun isTestCreation(requirement: String): Boolean {
<span class="pc bpc" id="L127" title="1 of 6 branches missed.">        return (requirement.contains(&quot;generate&quot;) || requirement.contains(&quot;write&quot;) || requirement.contains(&quot;create&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L128" title="1 of 4 branches missed.">               (requirement.contains(&quot;test&quot;) || requirement.contains(&quot;spec&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L129" title="2 of 4 branches missed.">               !requirement.contains(&quot;api&quot;) &amp;&amp; !requirement.contains(&quot;endpoint&quot;)</span>
    }
    
    private fun isServiceCreation(requirement: String): Boolean {
<span class="fc bfc" id="L133" title="All 4 branches covered.">        return (requirement.contains(&quot;create&quot;) || requirement.contains(&quot;build&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L134" title="1 of 4 branches missed.">               (requirement.contains(&quot;service&quot;) || requirement.contains(&quot;microservice&quot;))</span>
    }
    
    private fun isSystemCreation(requirement: String): Boolean {
<span class="fc bfc" id="L138" title="All 4 branches covered.">        return (requirement.contains(&quot;create&quot;) || requirement.contains(&quot;build&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L139" title="1 of 4 branches missed.">               (requirement.contains(&quot;system&quot;) || requirement.contains(&quot;application&quot;) ||</span>
<span class="pc bpc" id="L140" title="3 of 4 branches missed.">                (requirement.contains(&quot;complete&quot;) &amp;&amp; requirement.contains(&quot;e-commerce&quot;)))</span>
    }
    
    private fun isFormCreation(requirement: String): Boolean {
<span class="fc bfc" id="L144" title="All 4 branches covered.">        return (requirement.contains(&quot;create&quot;) || requirement.contains(&quot;build&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L145" title="1 of 4 branches missed.">               (requirement.contains(&quot;form&quot;) || requirement.contains(&quot;registration&quot;) ||</span>
<span class="pc bpc" id="L146" title="1 of 2 branches missed.">                requirement.contains(&quot;login&quot;))</span>
    }
    
    private fun isDocumentationCreation(requirement: String): Boolean {
<span class="fc bfc" id="L150" title="All 4 branches covered.">        return (requirement.contains(&quot;generate&quot;) || requirement.contains(&quot;create&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L151" title="1 of 4 branches missed.">               (requirement.contains(&quot;documentation&quot;) || requirement.contains(&quot;docs&quot;)) &amp;&amp;</span>
<span class="pc bpc" id="L152" title="2 of 4 branches missed.">               !requirement.contains(&quot;class&quot;) &amp;&amp; !requirement.contains(&quot;api endpoint&quot;)</span>
    }
    
    private fun isRefactoring(requirement: String): Boolean {
<span class="fc bfc" id="L156" title="All 2 branches covered.">        return requirement.contains(&quot;refactor&quot;) || </span>
<span class="pc bpc" id="L157" title="3 of 4 branches missed.">               (requirement.contains(&quot;improve&quot;) &amp;&amp; requirement.contains(&quot;structure&quot;)) ||</span>
<span class="pc bpc" id="L158" title="3 of 4 branches missed.">               (requirement.contains(&quot;delete&quot;) &amp;&amp; requirement.contains(&quot;create&quot;))</span>
    }
    
    private fun isImprovement(requirement: String): Boolean {
<span class="pc bpc" id="L162" title="1 of 4 branches missed.">        return requirement.contains(&quot;improve&quot;) || requirement.contains(&quot;better&quot;) ||</span>
<span class="pc bpc" id="L163" title="2 of 4 branches missed.">               requirement.contains(&quot;enhance&quot;) || requirement.contains(&quot;make it better&quot;)</span>
    }
    
    private fun isOptimization(requirement: String): Boolean {
<span class="pc bpc" id="L167" title="1 of 4 branches missed.">        return requirement.contains(&quot;optimize&quot;) || requirement.contains(&quot;performance&quot;) ||</span>
<span class="pc bpc" id="L168" title="2 of 4 branches missed.">               requirement.contains(&quot;faster&quot;) || requirement.contains(&quot;concurrent&quot;)</span>
    }
    
    private fun isBugFix(requirement: String): Boolean {
<span class="pc bpc" id="L172" title="2 of 4 branches missed.">        return requirement.contains(&quot;fix&quot;) || requirement.contains(&quot;bug&quot;) ||</span>
<span class="pc bpc" id="L173" title="2 of 4 branches missed.">               requirement.contains(&quot;error&quot;) || requirement.contains(&quot;issue&quot;)</span>
    }
    
    private fun isFeatureAddition(requirement: String): Boolean {
<span class="pc bpc" id="L177" title="1 of 2 branches missed.">        return requirement.contains(&quot;add&quot;) &amp;&amp; </span>
<span class="pc bnc" id="L178" title="All 4 branches missed.">               (requirement.contains(&quot;feature&quot;) || requirement.contains(&quot;functionality&quot;))</span>
    }
    
    private fun isFeatureRemoval(requirement: String): Boolean {
<span class="pc bpc" id="L182" title="1 of 2 branches missed.">        return requirement.contains(&quot;remove&quot;) &amp;&amp; </span>
<span class="pc bnc" id="L183" title="All 4 branches missed.">               (requirement.contains(&quot;feature&quot;) || requirement.contains(&quot;functionality&quot;))</span>
    }
    
    private fun isDependencyUpdate(requirement: String): Boolean {
<span class="pc bpc" id="L187" title="1 of 2 branches missed.">        return requirement.contains(&quot;update&quot;) &amp;&amp; </span>
<span class="pc bnc" id="L188" title="All 4 branches missed.">               (requirement.contains(&quot;dependency&quot;) || requirement.contains(&quot;version&quot;))</span>
    }
    
    // Parameter extraction methods
    private fun extractClassParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract class name
<span class="fc" id="L194">        val className = extractClassName(requirement)</span>
<span class="fc bfc" id="L195" title="All 2 branches covered.">        if (className != null) {</span>
<span class="fc" id="L196">            parameters[&quot;className&quot;] = className</span>
        }
        
        // Extract class type
<span class="fc" id="L200">        when {</span>
<span class="pc bpc" id="L201" title="1 of 2 branches missed.">            requirement.contains(&quot;data class&quot;, ignoreCase = true) -&gt; parameters[&quot;classType&quot;] = &quot;data&quot;</span>
<span class="fc bfc" id="L202" title="All 2 branches covered.">            requirement.contains(&quot;entity&quot;, ignoreCase = true) -&gt; parameters[&quot;classType&quot;] = &quot;entity&quot;</span>
<span class="pc bpc" id="L203" title="1 of 2 branches missed.">            requirement.contains(&quot;interface&quot;, ignoreCase = true) -&gt; parameters[&quot;classType&quot;] = &quot;interface&quot;</span>
<span class="pc bpc" id="L204" title="1 of 2 branches missed.">            requirement.contains(&quot;service&quot;, ignoreCase = true) -&gt; parameters[&quot;classType&quot;] = &quot;service&quot;</span>
<span class="fc" id="L205">            else -&gt; parameters[&quot;classType&quot;] = &quot;class&quot;</span>
        }
        
        // Extract properties
<span class="fc" id="L209">        val properties = extractProperties(requirement)</span>
<span class="fc bfc" id="L210" title="All 4 branches covered.">        if (properties.isNotEmpty()) {</span>
<span class="fc" id="L211">            parameters[&quot;properties&quot;] = properties.joinToString(&quot;,&quot;)</span>
        }
<span class="fc" id="L213">    }</span>
    
    private fun extractApiParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract entity name
<span class="fc" id="L217">        val entity = extractEntityName(requirement)</span>
<span class="pc bpc" id="L218" title="1 of 2 branches missed.">        if (entity != null) {</span>
<span class="fc" id="L219">            parameters[&quot;entity&quot;] = entity</span>
        }
        
        // Extract API type
<span class="fc" id="L223">        when {</span>
<span class="fc bfc" id="L224" title="All 2 branches covered.">            requirement.contains(&quot;rest&quot;, ignoreCase = true) -&gt; parameters[&quot;apiType&quot;] = &quot;REST&quot;</span>
<span class="pc bpc" id="L225" title="1 of 2 branches missed.">            requirement.contains(&quot;graphql&quot;, ignoreCase = true) -&gt; parameters[&quot;apiType&quot;] = &quot;GraphQL&quot;</span>
<span class="nc" id="L226">            else -&gt; parameters[&quot;apiType&quot;] = &quot;REST&quot;</span>
        }
        
        // Extract operations
<span class="fc" id="L230">        val operations = mutableListOf&lt;String&gt;()</span>
<span class="fc bfc" id="L231" title="All 2 branches covered.">        if (requirement.contains(&quot;crud&quot;, ignoreCase = true)) operations.add(&quot;CRUD&quot;)</span>
<span class="fc bfc" id="L232" title="All 2 branches covered.">        if (requirement.contains(&quot;mutations&quot;, ignoreCase = true)) operations.add(&quot;mutations&quot;)</span>
<span class="fc bfc" id="L233" title="All 2 branches covered.">        if (requirement.contains(&quot;queries&quot;, ignoreCase = true)) operations.add(&quot;queries&quot;)</span>
        
<span class="pc bpc" id="L235" title="2 of 4 branches missed.">        if (operations.isNotEmpty()) {</span>
<span class="fc" id="L236">            parameters[&quot;operations&quot;] = operations.joinToString(&quot;,&quot;)</span>
        }
<span class="fc" id="L238">    }</span>
    
    private fun extractConfigParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract config type
<span class="fc" id="L242">        when {</span>
<span class="fc bfc" id="L243" title="All 2 branches covered.">            requirement.contains(&quot;database&quot;, ignoreCase = true) -&gt; parameters[&quot;configType&quot;] = &quot;database&quot;</span>
<span class="pc bpc" id="L244" title="1 of 2 branches missed.">            requirement.contains(&quot;security&quot;, ignoreCase = true) -&gt; parameters[&quot;configType&quot;] = &quot;security&quot;</span>
<span class="pc bpc" id="L245" title="1 of 2 branches missed.">            requirement.contains(&quot;logging&quot;, ignoreCase = true) -&gt; parameters[&quot;configType&quot;] = &quot;logging&quot;</span>
<span class="fc" id="L246">            else -&gt; parameters[&quot;configType&quot;] = &quot;application&quot;</span>
        }
        
        // Extract settings
<span class="fc" id="L250">        val settings = mutableListOf&lt;String&gt;()</span>
<span class="fc bfc" id="L251" title="All 2 branches covered.">        if (requirement.contains(&quot;connection&quot;, ignoreCase = true)) settings.add(&quot;connection&quot;)</span>
<span class="pc bpc" id="L252" title="1 of 2 branches missed.">        if (requirement.contains(&quot;authentication&quot;, ignoreCase = true)) settings.add(&quot;authentication&quot;)</span>
<span class="pc bpc" id="L253" title="1 of 2 branches missed.">        if (requirement.contains(&quot;logging&quot;, ignoreCase = true)) settings.add(&quot;logging&quot;)</span>
        
<span class="fc bfc" id="L255" title="All 4 branches covered.">        if (settings.isNotEmpty()) {</span>
<span class="fc" id="L256">            parameters[&quot;settings&quot;] = settings.joinToString(&quot;,&quot;)</span>
        }
<span class="fc" id="L258">    }</span>
    
    private fun extractTestParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract target class
<span class="fc" id="L262">        val targetClass = extractTargetClass(requirement)</span>
<span class="pc bpc" id="L263" title="1 of 2 branches missed.">        if (targetClass != null) {</span>
<span class="fc" id="L264">            parameters[&quot;targetClass&quot;] = targetClass</span>
        }
        
        // Extract test type
<span class="fc" id="L268">        when {</span>
<span class="pc bpc" id="L269" title="1 of 2 branches missed.">            requirement.contains(&quot;unit&quot;, ignoreCase = true) -&gt; parameters[&quot;testType&quot;] = &quot;unit&quot;</span>
<span class="nc bnc" id="L270" title="All 2 branches missed.">            requirement.contains(&quot;integration&quot;, ignoreCase = true) -&gt; parameters[&quot;testType&quot;] = &quot;integration&quot;</span>
<span class="nc bnc" id="L271" title="All 2 branches missed.">            requirement.contains(&quot;e2e&quot;, ignoreCase = true) -&gt; parameters[&quot;testType&quot;] = &quot;e2e&quot;</span>
<span class="nc" id="L272">            else -&gt; parameters[&quot;testType&quot;] = &quot;unit&quot;</span>
        }
        
        // Extract coverage
<span class="fc" id="L276">        when {</span>
<span class="pc bpc" id="L277" title="1 of 2 branches missed.">            requirement.contains(&quot;all methods&quot;, ignoreCase = true) -&gt; parameters[&quot;coverage&quot;] = &quot;all&quot;</span>
<span class="nc bnc" id="L278" title="All 2 branches missed.">            requirement.contains(&quot;complete&quot;, ignoreCase = true) -&gt; parameters[&quot;coverage&quot;] = &quot;complete&quot;</span>
<span class="nc" id="L279">            else -&gt; parameters[&quot;coverage&quot;] = &quot;basic&quot;</span>
        }
<span class="fc" id="L281">    }</span>
    
    private fun extractServiceParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract framework
<span class="fc" id="L285">        when {</span>
<span class="fc bfc" id="L286" title="All 2 branches covered.">            requirement.contains(&quot;spring boot&quot;, ignoreCase = true) -&gt; parameters[&quot;framework&quot;] = &quot;Spring Boot&quot;</span>
<span class="pc bpc" id="L287" title="1 of 2 branches missed.">            requirement.contains(&quot;ktor&quot;, ignoreCase = true) -&gt; parameters[&quot;framework&quot;] = &quot;Ktor&quot;</span>
<span class="fc" id="L288">            else -&gt; parameters[&quot;framework&quot;] = &quot;Spring Boot&quot;</span>
        }
        
        // Extract service type
<span class="fc" id="L292">        when {</span>
<span class="fc bfc" id="L293" title="All 2 branches covered.">            requirement.contains(&quot;microservice&quot;, ignoreCase = true) -&gt; parameters[&quot;serviceType&quot;] = &quot;microservice&quot;</span>
<span class="pc bpc" id="L294" title="1 of 2 branches missed.">            requirement.contains(&quot;web service&quot;, ignoreCase = true) -&gt; parameters[&quot;serviceType&quot;] = &quot;web service&quot;</span>
<span class="fc" id="L295">            else -&gt; parameters[&quot;serviceType&quot;] = &quot;service&quot;</span>
        }
        
        // Extract technologies
<span class="fc" id="L299">        val technologies = mutableListOf&lt;String&gt;()</span>
<span class="fc bfc" id="L300" title="All 2 branches covered.">        if (requirement.contains(&quot;jpa&quot;, ignoreCase = true)) technologies.add(&quot;JPA&quot;)</span>
<span class="fc bfc" id="L301" title="All 2 branches covered.">        if (requirement.contains(&quot;postgresql&quot;, ignoreCase = true)) technologies.add(&quot;PostgreSQL&quot;)</span>
<span class="pc bpc" id="L302" title="1 of 2 branches missed.">        if (requirement.contains(&quot;mysql&quot;, ignoreCase = true)) technologies.add(&quot;MySQL&quot;)</span>
<span class="pc bpc" id="L303" title="1 of 2 branches missed.">        if (requirement.contains(&quot;redis&quot;, ignoreCase = true)) technologies.add(&quot;Redis&quot;)</span>
        
<span class="fc bfc" id="L305" title="All 4 branches covered.">        if (technologies.isNotEmpty()) {</span>
<span class="fc" id="L306">            parameters[&quot;technologies&quot;] = technologies.joinToString(&quot;,&quot;)</span>
        }
<span class="fc" id="L308">    }</span>
    
    private fun extractSystemParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract entities
<span class="fc" id="L312">        val entities = extractEntities(requirement)</span>
<span class="pc bpc" id="L313" title="2 of 4 branches missed.">        if (entities.isNotEmpty()) {</span>
<span class="fc" id="L314">            parameters[&quot;entities&quot;] = entities.joinToString(&quot;,&quot;)</span>
        }
        
        // Extract features
<span class="fc" id="L318">        val features = mutableListOf&lt;String&gt;()</span>
<span class="pc bpc" id="L319" title="1 of 2 branches missed.">        if (requirement.contains(&quot;rest api&quot;, ignoreCase = true)) features.add(&quot;REST APIs&quot;)</span>
<span class="pc bpc" id="L320" title="1 of 2 branches missed.">        if (requirement.contains(&quot;database&quot;, ignoreCase = true)) features.add(&quot;database integration&quot;)</span>
<span class="pc bpc" id="L321" title="1 of 2 branches missed.">        if (requirement.contains(&quot;authentication&quot;, ignoreCase = true)) features.add(&quot;authentication&quot;)</span>
<span class="pc bpc" id="L322" title="1 of 2 branches missed.">        if (requirement.contains(&quot;authorization&quot;, ignoreCase = true)) features.add(&quot;authorization&quot;)</span>
        
<span class="pc bpc" id="L324" title="2 of 4 branches missed.">        if (features.isNotEmpty()) {</span>
<span class="fc" id="L325">            parameters[&quot;features&quot;] = features.joinToString(&quot;,&quot;)</span>
        }
<span class="fc" id="L327">    }</span>
    
    private fun extractFormParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract entity
<span class="fc" id="L331">        val entity = extractEntityName(requirement)</span>
<span class="pc bpc" id="L332" title="1 of 2 branches missed.">        if (entity != null) {</span>
<span class="fc" id="L333">            parameters[&quot;entity&quot;] = entity</span>
        }
        
        // Extract form type
<span class="fc" id="L337">        when {</span>
<span class="pc bpc" id="L338" title="1 of 2 branches missed.">            requirement.contains(&quot;registration&quot;, ignoreCase = true) -&gt; parameters[&quot;formType&quot;] = &quot;registration&quot;</span>
<span class="nc bnc" id="L339" title="All 2 branches missed.">            requirement.contains(&quot;login&quot;, ignoreCase = true) -&gt; parameters[&quot;formType&quot;] = &quot;login&quot;</span>
<span class="nc bnc" id="L340" title="All 2 branches missed.">            requirement.contains(&quot;contact&quot;, ignoreCase = true) -&gt; parameters[&quot;formType&quot;] = &quot;contact&quot;</span>
<span class="nc" id="L341">            else -&gt; parameters[&quot;formType&quot;] = &quot;form&quot;</span>
        }
        
        // Extract validations
<span class="fc" id="L345">        val validations = mutableListOf&lt;String&gt;()</span>
<span class="pc bpc" id="L346" title="1 of 2 branches missed.">        if (requirement.contains(&quot;email validation&quot;, ignoreCase = true)) validations.add(&quot;email validation&quot;)</span>
<span class="pc bpc" id="L347" title="1 of 2 branches missed.">        if (requirement.contains(&quot;password strength&quot;, ignoreCase = true)) validations.add(&quot;password strength&quot;)</span>
<span class="pc bpc" id="L348" title="1 of 2 branches missed.">        if (requirement.contains(&quot;required fields&quot;, ignoreCase = true)) validations.add(&quot;required fields&quot;)</span>
        
<span class="pc bpc" id="L350" title="2 of 4 branches missed.">        if (validations.isNotEmpty()) {</span>
<span class="fc" id="L351">            parameters[&quot;validations&quot;] = validations.joinToString(&quot;,&quot;)</span>
        }
<span class="fc" id="L353">    }</span>
    
    private fun extractDocumentationParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract doc type
<span class="fc" id="L357">        when {</span>
<span class="pc bpc" id="L358" title="1 of 2 branches missed.">            requirement.contains(&quot;api&quot;, ignoreCase = true) -&gt; parameters[&quot;docType&quot;] = &quot;API&quot;</span>
<span class="nc bnc" id="L359" title="All 2 branches missed.">            requirement.contains(&quot;user&quot;, ignoreCase = true) -&gt; parameters[&quot;docType&quot;] = &quot;User&quot;</span>
<span class="nc bnc" id="L360" title="All 2 branches missed.">            requirement.contains(&quot;technical&quot;, ignoreCase = true) -&gt; parameters[&quot;docType&quot;] = &quot;Technical&quot;</span>
<span class="nc" id="L361">            else -&gt; parameters[&quot;docType&quot;] = &quot;General&quot;</span>
        }
        
        // Extract format
<span class="fc" id="L365">        when {</span>
<span class="pc bpc" id="L366" title="1 of 2 branches missed.">            requirement.contains(&quot;openapi&quot;, ignoreCase = true) -&gt; parameters[&quot;format&quot;] = &quot;OpenAPI 3.0&quot;</span>
<span class="nc bnc" id="L367" title="All 2 branches missed.">            requirement.contains(&quot;swagger&quot;, ignoreCase = true) -&gt; parameters[&quot;format&quot;] = &quot;Swagger&quot;</span>
<span class="nc bnc" id="L368" title="All 2 branches missed.">            requirement.contains(&quot;markdown&quot;, ignoreCase = true) -&gt; parameters[&quot;format&quot;] = &quot;Markdown&quot;</span>
<span class="nc" id="L369">            else -&gt; parameters[&quot;format&quot;] = &quot;Markdown&quot;</span>
        }
        
        // Extract scope
<span class="fc" id="L373">        when {</span>
<span class="pc bpc" id="L374" title="1 of 2 branches missed.">            requirement.contains(&quot;rest endpoints&quot;, ignoreCase = true) -&gt; parameters[&quot;scope&quot;] = &quot;REST endpoints&quot;</span>
<span class="nc bnc" id="L375" title="All 2 branches missed.">            requirement.contains(&quot;all endpoints&quot;, ignoreCase = true) -&gt; parameters[&quot;scope&quot;] = &quot;all endpoints&quot;</span>
<span class="nc" id="L376">            else -&gt; parameters[&quot;scope&quot;] = &quot;general&quot;</span>
        }
<span class="fc" id="L378">    }</span>
    
    private fun extractRefactorParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract target file/class
<span class="nc" id="L382">        val targetFile = extractTargetFile(requirement)</span>
<span class="nc bnc" id="L383" title="All 2 branches missed.">        if (targetFile != null) {</span>
<span class="nc" id="L384">            parameters[&quot;targetFile&quot;] = targetFile</span>
        }
        
<span class="nc" id="L387">        val targetClass = extractTargetClass(requirement)</span>
<span class="nc bnc" id="L388" title="All 2 branches missed.">        if (targetClass != null) {</span>
<span class="nc" id="L389">            parameters[&quot;targetClass&quot;] = targetClass</span>
        }
        
        // Extract operations
<span class="nc" id="L393">        val operations = mutableListOf&lt;String&gt;()</span>
<span class="nc bnc" id="L394" title="All 2 branches missed.">        if (requirement.contains(&quot;delete&quot;, ignoreCase = true)) operations.add(&quot;delete&quot;)</span>
<span class="nc bnc" id="L395" title="All 2 branches missed.">        if (requirement.contains(&quot;create&quot;, ignoreCase = true)) operations.add(&quot;create&quot;)</span>
<span class="nc bnc" id="L396" title="All 2 branches missed.">        if (requirement.contains(&quot;move&quot;, ignoreCase = true)) operations.add(&quot;move&quot;)</span>
<span class="nc bnc" id="L397" title="All 2 branches missed.">        if (requirement.contains(&quot;rename&quot;, ignoreCase = true)) operations.add(&quot;rename&quot;)</span>
        
<span class="nc bnc" id="L399" title="All 4 branches missed.">        if (operations.isNotEmpty()) {</span>
<span class="nc" id="L400">            parameters[&quot;operations&quot;] = operations.joinToString(&quot;,&quot;)</span>
        }
        
        // Extract improvements
<span class="nc" id="L404">        val improvements = mutableListOf&lt;String&gt;()</span>
<span class="nc bnc" id="L405" title="All 2 branches missed.">        if (requirement.contains(&quot;better structure&quot;, ignoreCase = true)) improvements.add(&quot;better structure&quot;)</span>
<span class="nc bnc" id="L406" title="All 2 branches missed.">        if (requirement.contains(&quot;performance&quot;, ignoreCase = true)) improvements.add(&quot;performance&quot;)</span>
<span class="nc bnc" id="L407" title="All 2 branches missed.">        if (requirement.contains(&quot;readability&quot;, ignoreCase = true)) improvements.add(&quot;readability&quot;)</span>
        
<span class="nc bnc" id="L409" title="All 4 branches missed.">        if (improvements.isNotEmpty()) {</span>
<span class="nc" id="L410">            parameters[&quot;improvements&quot;] = improvements.joinToString(&quot;,&quot;)</span>
        }
<span class="nc" id="L412">    }</span>
    
    private fun extractOptimizationParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
        // Extract target class
<span class="fc" id="L416">        val targetClass = extractTargetClass(requirement)</span>
<span class="pc bpc" id="L417" title="1 of 2 branches missed.">        if (targetClass != null) {</span>
<span class="fc" id="L418">            parameters[&quot;targetClass&quot;] = targetClass</span>
        }
        
        // Extract optimization target
<span class="fc" id="L422">        when {</span>
<span class="pc bpc" id="L423" title="1 of 2 branches missed.">            requirement.contains(&quot;database queries&quot;, ignoreCase = true) -&gt; parameters[&quot;optimizationTarget&quot;] = &quot;database queries&quot;</span>
<span class="nc bnc" id="L424" title="All 2 branches missed.">            requirement.contains(&quot;memory&quot;, ignoreCase = true) -&gt; parameters[&quot;optimizationTarget&quot;] = &quot;memory usage&quot;</span>
<span class="nc bnc" id="L425" title="All 2 branches missed.">            requirement.contains(&quot;cpu&quot;, ignoreCase = true) -&gt; parameters[&quot;optimizationTarget&quot;] = &quot;CPU usage&quot;</span>
<span class="nc" id="L426">            else -&gt; parameters[&quot;optimizationTarget&quot;] = &quot;performance&quot;</span>
        }
        
        // Extract performance goal
<span class="fc" id="L430">        val performanceGoal = extractPerformanceGoal(requirement)</span>
<span class="pc bpc" id="L431" title="1 of 2 branches missed.">        if (performanceGoal != null) {</span>
<span class="fc" id="L432">            parameters[&quot;performanceGoal&quot;] = performanceGoal</span>
        }
<span class="fc" id="L434">    }</span>
    
    private fun extractImprovementParameters(requirement: String, parameters: MutableMap&lt;String, String&gt;) {
<span class="fc" id="L437">        when {</span>
<span class="pc bpc" id="L438" title="1 of 2 branches missed.">            requirement.contains(&quot;make it better&quot;, ignoreCase = true) -&gt; parameters[&quot;scope&quot;] = &quot;general&quot;</span>
<span class="nc bnc" id="L439" title="All 2 branches missed.">            requirement.contains(&quot;improve&quot;, ignoreCase = true) -&gt; parameters[&quot;scope&quot;] = &quot;improvement&quot;</span>
<span class="nc" id="L440">            else -&gt; parameters[&quot;scope&quot;] = &quot;general&quot;</span>
        }
<span class="fc" id="L442">    }</span>
    
    // Helper methods for extraction
    private fun extractClassName(requirement: String): String? {
        // Look for patterns like &quot;User class&quot;, &quot;Product entity&quot;, etc.
<span class="fc" id="L447">        val words = requirement.split(&quot; &quot;)</span>

        // First try to find class name before keywords
<span class="fc bfc" id="L450" title="All 2 branches covered.">        for (i in words.indices) {</span>
<span class="fc" id="L451">            val word = words[i].lowercase()</span>
<span class="fc bfc" id="L452" title="All 2 branches covered.">            if (word in listOf(&quot;class&quot;, &quot;entity&quot;, &quot;model&quot;, &quot;interface&quot;, &quot;service&quot;)) {</span>
<span class="pc bpc" id="L453" title="1 of 2 branches missed.">                if (i &gt; 0) {</span>
<span class="fc" id="L454">                    val className = words[i - 1]</span>
                    // Skip adjectives like &quot;simple&quot;, &quot;data&quot;, etc.
<span class="fc bfc" id="L456" title="All 2 branches covered.">                    if (className.lowercase() !in listOf(&quot;simple&quot;, &quot;data&quot;, &quot;new&quot;, &quot;basic&quot;)) {</span>
<span class="pc bpc" id="L457" title="2 of 4 branches missed.">                        return className.replaceFirstChar { it.uppercase() }</span>
<span class="pc bpc" id="L458" title="1 of 2 branches missed.">                    } else if (i &gt; 1) {</span>
                        // Look one word further back
<span class="fc" id="L460">                        val prevClassName = words[i - 2]</span>
<span class="pc bpc" id="L461" title="1 of 2 branches missed.">                        if (prevClassName.lowercase() !in listOf(&quot;a&quot;, &quot;the&quot;, &quot;create&quot;, &quot;generate&quot;)) {</span>
<span class="nc bnc" id="L462" title="All 4 branches missed.">                            return prevClassName.replaceFirstChar { it.uppercase() }</span>
                        }
                    }
                }
            }
        }

        // Look for common entity names that should be capitalized
<span class="fc" id="L470">        val commonEntities = listOf(&quot;user&quot;, &quot;product&quot;, &quot;order&quot;, &quot;customer&quot;, &quot;item&quot;, &quot;account&quot;, &quot;person&quot;)</span>
<span class="fc bfc" id="L471" title="All 2 branches covered.">        for (entity in commonEntities) {</span>
<span class="pc bpc" id="L472" title="1 of 2 branches missed.">            if (requirement.contains(entity, ignoreCase = true)) {</span>
<span class="nc bnc" id="L473" title="All 4 branches missed.">                return entity.replaceFirstChar { it.uppercase() }</span>
            }
        }

        // Look for capitalized words that might be class names
<span class="fc" id="L478">        val capitalizedWords = words.filter {</span>
<span class="pc bpc" id="L479" title="1 of 6 branches missed.">            it.firstOrNull()?.isUpperCase() == true &amp;&amp;</span>
<span class="pc bpc" id="L480" title="1 of 2 branches missed.">            it.lowercase() !in listOf(&quot;create&quot;, &quot;generate&quot;)</span>
        }
<span class="fc" id="L482">        return capitalizedWords.firstOrNull()</span>
    }
    
    private fun extractEntityName(requirement: String): String? {
        // Look for common entity patterns
<span class="fc" id="L487">        val entityPatterns = listOf(&quot;user&quot;, &quot;product&quot;, &quot;order&quot;, &quot;customer&quot;, &quot;item&quot;, &quot;account&quot;)</span>
<span class="fc" id="L488">        val requirementLower = requirement.lowercase()</span>

<span class="pc bpc" id="L490" title="4 of 10 branches missed.">        return entityPatterns.find { requirementLower.contains(it) }?.replaceFirstChar { it.uppercase() }</span>
    }
    
    private fun extractProperties(requirement: String): List&lt;String&gt; {
<span class="fc" id="L494">        val properties = mutableListOf&lt;String&gt;()</span>
<span class="fc" id="L495">        val commonProperties = listOf(&quot;id&quot;, &quot;name&quot;, &quot;email&quot;, &quot;age&quot;, &quot;price&quot;, &quot;description&quot;, &quot;title&quot;, &quot;content&quot;)</span>
<span class="fc" id="L496">        val requirementLower = requirement.lowercase()</span>
        
<span class="fc" id="L498">        commonProperties.forEach { prop -&gt;</span>
<span class="fc bfc" id="L499" title="All 2 branches covered.">            if (requirementLower.contains(prop)) {</span>
<span class="fc" id="L500">                properties.add(prop)</span>
            }
<span class="fc" id="L502">        }</span>
        
<span class="fc" id="L504">        return properties</span>
    }
    
    private fun extractTargetClass(requirement: String): String? {
        // Look for patterns like &quot;UserService class&quot;, &quot;UserController&quot;, etc.
<span class="fc" id="L509">        val classPattern = Regex(&quot;&quot;&quot;(\w+(?:Service|Controller|Repository|Manager|Handler))&quot;&quot;&quot;, RegexOption.IGNORE_CASE)</span>
<span class="pc bpc" id="L510" title="1 of 2 branches missed.">        return classPattern.find(requirement)?.value</span>
    }
    
    private fun extractTargetFile(requirement: String): String? {
        // Look for file patterns like &quot;UserService.kt&quot;
<span class="nc" id="L515">        val filePattern = Regex(&quot;&quot;&quot;(\w+\.\w+)&quot;&quot;&quot;)</span>
<span class="nc bnc" id="L516" title="All 2 branches missed.">        return filePattern.find(requirement)?.value</span>
    }
    
    private fun extractEntities(requirement: String): List&lt;String&gt; {
<span class="fc" id="L520">        val entities = mutableListOf&lt;String&gt;()</span>
<span class="fc" id="L521">        val entityPatterns = listOf(&quot;User&quot;, &quot;Product&quot;, &quot;Order&quot;, &quot;Customer&quot;, &quot;Item&quot;, &quot;Account&quot;, &quot;Category&quot;)</span>
        
<span class="fc" id="L523">        entityPatterns.forEach { entity -&gt;</span>
<span class="fc bfc" id="L524" title="All 2 branches covered.">            if (requirement.contains(entity, ignoreCase = true)) {</span>
<span class="fc" id="L525">                entities.add(entity)</span>
            }
<span class="fc" id="L527">        }</span>
        
<span class="fc" id="L529">        return entities</span>
    }
    
    private fun extractPerformanceGoal(requirement: String): String? {
        // Look for performance goals like &quot;1000+ concurrent users&quot;, &quot;sub-second response&quot;
<span class="fc" id="L534">        val goalPattern = Regex(&quot;&quot;&quot;(\d+\+?\s*(?:concurrent\s+)?users?|\d+\s*(?:ms|seconds?)|sub-second)&quot;&quot;&quot;, RegexOption.IGNORE_CASE)</span>
<span class="pc bpc" id="L535" title="1 of 2 branches missed.">        return goalPattern.find(requirement)?.value</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>