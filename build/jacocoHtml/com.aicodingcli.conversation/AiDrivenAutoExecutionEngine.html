<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiDrivenAutoExecutionEngine</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_class">AiDrivenAutoExecutionEngine</span></div><h1>AiDrivenAutoExecutionEngine</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">239 of 1,293</td><td class="ctr2">81%</td><td class="bar">26 of 56</td><td class="ctr2">53%</td><td class="ctr1">17</td><td class="ctr2">39</td><td class="ctr1">46</td><td class="ctr2">233</td><td class="ctr1">1</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a8"><a href="AiDrivenAutoExecutionEngine.kt.html#L109" class="el_method">executeWithAiGuidance(ConversationSession, ExecutionContext, TimeMark, Continuation)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="119" alt="119"/><img src="../jacoco-resources/greenbar.gif" width="101" height="10" title="654" alt="654"/></td><td class="ctr2" id="c5">84%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="72" height="10" title="17" alt="17"/></td><td class="ctr2" id="e3">85%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">11</td><td class="ctr1" id="h0">20</td><td class="ctr2" id="i0">116</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="AiDrivenAutoExecutionEngine.kt.html#L292" class="el_method">performSafetyCheck(String, Map)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="78" alt="78"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="37" alt="37"/></td><td class="ctr2" id="c9">32%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="21" alt="21"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="7" alt="7"/></td><td class="ctr2" id="e4">25%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">15</td><td class="ctr1" id="h1">7</td><td class="ctr2" id="i3">16</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="AiDrivenAutoExecutionEngine.kt.html#L58" class="el_method">continueConversation(String, Continuation)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="69" alt="69"/></td><td class="ctr2" id="c6">84%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">7</td><td class="ctr2" id="i2">24</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="AiDrivenAutoExecutionEngine.kt.html#L15" class="el_method">executeConversation(String, Continuation)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="63" alt="63"/></td><td class="ctr2" id="c7">82%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i1">26</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="AiDrivenAutoExecutionEngine.kt.html#L102" class="el_method">setMaxExecutionRounds(int)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="9" alt="9"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="AiDrivenAutoExecutionEngine.kt.html#L92" class="el_method">executeStep(ExecutionStep, Continuation)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="16" alt="16"/></td><td class="ctr2" id="c8">69%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h5">2</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="AiDrivenAutoExecutionEngine.kt.html#L331" class="el_method">createSuccessResult(ConversationSession, List, int, TimeMark, String, CompletionEvaluation, Continuation)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="65" alt="65"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i4">14</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="AiDrivenAutoExecutionEngine.kt.html#L361" class="el_method">createWaitingResult(ConversationSession, List, int, TimeMark, String, Continuation)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="56" alt="56"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i5">11</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a3"><a href="AiDrivenAutoExecutionEngine.kt.html#L385" class="el_method">createFailureResult(ConversationSession, List, int, TimeMark, String, Continuation)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="56" alt="56"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i6">11</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="AiDrivenAutoExecutionEngine.kt.html#L8" class="el_method">AiDrivenAutoExecutionEngine(ConversationStateManager, AiPromptEngine, ToolExecutor, AiExecutionStrategy, int, DefaultConstructorMarker)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="23" alt="23"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a0"><a href="AiDrivenAutoExecutionEngine.kt.html#L8" class="el_method">AiDrivenAutoExecutionEngine(ConversationStateManager, AiPromptEngine, ToolExecutor, AiExecutionStrategy)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="15" alt="15"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>