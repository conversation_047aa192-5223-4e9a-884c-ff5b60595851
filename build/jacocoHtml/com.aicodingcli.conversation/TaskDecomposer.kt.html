<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TaskDecomposer.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">TaskDecomposer.kt</span></div><h1>TaskDecomposer.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

/**
 * Interface for decomposing requirements into executable tasks
 */
interface TaskDecomposer {
    /**
     * Decompose a requirement into executable tasks
     */
    suspend fun decompose(requirement: String, context: ProjectContext): List&lt;ExecutableTask&gt;
    
    /**
     * Refine a task based on feedback
     */
    suspend fun refineTask(task: ExecutableTask, feedback: String): ExecutableTask
    
    /**
     * Validate task sequence for dependency conflicts
     */
    suspend fun validateTaskSequence(tasks: List&lt;ExecutableTask&gt;): ValidationResult
}

/**
 * Default implementation of TaskDecomposer
 */
<span class="fc" id="L26">class DefaultTaskDecomposer : TaskDecomposer {</span>
    
    override suspend fun decompose(requirement: String, context: ProjectContext): List&lt;ExecutableTask&gt; {
<span class="fc bfc" id="L29" title="All 2 branches covered.">        if (requirement.isBlank()) {</span>
<span class="fc" id="L30">            return emptyList()</span>
        }
        
<span class="fc" id="L33">        val tasks = mutableListOf&lt;ExecutableTask&gt;()</span>
<span class="fc" id="L34">        val requirementLower = requirement.lowercase()</span>
        
        // Analyze requirement and generate appropriate tasks
<span class="fc" id="L37">        when {</span>
<span class="fc bfc" id="L38" title="All 2 branches covered.">            isSimpleClassCreation(requirementLower) -&gt; {</span>
<span class="fc" id="L39">                tasks.addAll(generateClassCreationTasks(requirement, context))</span>
            }
<span class="fc bfc" id="L41" title="All 2 branches covered.">            isRestApiRequirement(requirementLower) -&gt; {</span>
<span class="fc" id="L42">                tasks.addAll(generateRestApiTasks(requirement, context))</span>
            }
<span class="fc bfc" id="L44" title="All 2 branches covered.">            isConfigurationRequirement(requirementLower) -&gt; {</span>
<span class="fc" id="L45">                tasks.addAll(generateConfigurationTasks(requirement, context))</span>
            }
<span class="fc bfc" id="L47" title="All 2 branches covered.">            isDataModelRequirement(requirementLower) -&gt; {</span>
<span class="fc" id="L48">                tasks.addAll(generateDataModelTasks(requirement, context))</span>
            }
            else -&gt; {
                // Generic task generation
<span class="fc" id="L52">                tasks.addAll(generateGenericTasks(requirement, context))</span>
            }
        }
        
        // Assign priorities and dependencies
<span class="fc" id="L57">        assignPrioritiesAndDependencies(tasks, context)</span>
        
<span class="fc" id="L59">        return tasks</span>
    }
    
    override suspend fun refineTask(task: ExecutableTask, feedback: String): ExecutableTask {
<span class="fc" id="L63">        val feedbackLower = feedback.lowercase()</span>
<span class="fc" id="L64">        var refinedDescription = task.description</span>
<span class="fc" id="L65">        var refinedToolCalls = task.toolCalls.toMutableList()</span>
        
        // Incorporate feedback into task description
<span class="fc" id="L68">        when {</span>
<span class="pc bpc" id="L69" title="1 of 2 branches missed.">            feedbackLower.contains(&quot;validation&quot;) -&gt; {</span>
<span class="fc" id="L70">                refinedDescription += &quot; with validation annotations&quot;</span>
            }
<span class="nc bnc" id="L72" title="All 2 branches missed.">            feedbackLower.contains(&quot;immutable&quot;) -&gt; {</span>
<span class="nc" id="L73">                refinedDescription += &quot; with immutable properties&quot;</span>
            }
<span class="nc bnc" id="L75" title="All 2 branches missed.">            feedbackLower.contains(&quot;test&quot;) -&gt; {</span>
<span class="nc" id="L76">                refinedDescription += &quot; including unit tests&quot;</span>
            }
        }
        
        // Update tool calls based on feedback
<span class="pc bpc" id="L81" title="3 of 4 branches missed.">        if (feedbackLower.contains(&quot;validation&quot;) || feedbackLower.contains(&quot;immutable&quot;)) {</span>
<span class="fc" id="L82">            refinedToolCalls = refinedToolCalls.map { toolCall -&gt;</span>
<span class="pc bpc" id="L83" title="2 of 4 branches missed.">                if (toolCall.toolName == &quot;save-file&quot; &amp;&amp; toolCall.parameters.containsKey(&quot;file_content&quot;)) {</span>
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">                    val content = toolCall.parameters[&quot;file_content&quot;] ?: &quot;&quot;</span>
<span class="fc" id="L85">                    val enhancedContent = enhanceCodeWithFeedback(content, feedback)</span>
<span class="fc" id="L86">                    toolCall.copy(parameters = toolCall.parameters + (&quot;file_content&quot; to enhancedContent))</span>
                } else {
<span class="nc" id="L88">                    toolCall</span>
<span class="fc" id="L89">                }</span>
<span class="fc" id="L90">            }.toMutableList()</span>
        }
        
<span class="fc" id="L93">        return task.copy(</span>
<span class="fc" id="L94">            description = refinedDescription,</span>
<span class="fc" id="L95">            toolCalls = refinedToolCalls</span>
        )
    }
    
    override suspend fun validateTaskSequence(tasks: List&lt;ExecutableTask&gt;): ValidationResult {
<span class="fc" id="L100">        val errors = mutableListOf&lt;String&gt;()</span>
<span class="fc" id="L101">        val taskIds = tasks.map { it.id }.toSet()</span>
        
        // Check for circular dependencies
<span class="fc bfc" id="L104" title="All 2 branches covered.">        if (hasCircularDependencies(tasks)) {</span>
<span class="fc" id="L105">            errors.add(&quot;Circular dependency detected in task sequence&quot;)</span>
        }
        
        // Check for missing dependencies
<span class="fc" id="L109">        tasks.forEach { task -&gt;</span>
<span class="fc" id="L110">            task.dependencies.forEach { depId -&gt;</span>
<span class="pc bpc" id="L111" title="1 of 2 branches missed.">                if (depId !in taskIds) {</span>
<span class="nc" id="L112">                    errors.add(&quot;Task ${task.id} depends on non-existent task $depId&quot;)</span>
                }
<span class="fc" id="L114">            }</span>
<span class="fc" id="L115">        }</span>
        
<span class="fc bfc" id="L117" title="All 2 branches covered.">        return if (errors.isEmpty()) {</span>
<span class="fc" id="L118">            ValidationResult.valid()</span>
        } else {
<span class="fc" id="L120">            ValidationResult.invalid(errors)</span>
        }
    }
    
    private fun isSimpleClassCreation(requirement: String): Boolean {
<span class="fc bfc" id="L125" title="All 2 branches covered.">        return requirement.contains(&quot;class&quot;) &amp;&amp;</span>
<span class="pc bpc" id="L126" title="2 of 4 branches missed.">               !requirement.contains(&quot;api&quot;) &amp;&amp; !requirement.contains(&quot;endpoint&quot;) &amp;&amp;</span>
<span class="pc bpc" id="L127" title="2 of 4 branches missed.">               !requirement.contains(&quot;rest&quot;) &amp;&amp; !requirement.contains(&quot;crud&quot;) &amp;&amp;</span>
<span class="pc bpc" id="L128" title="1 of 4 branches missed.">               (requirement.contains(&quot;simple&quot;) || requirement.contains(&quot;data&quot;) ||</span>
<span class="pc bpc" id="L129" title="3 of 4 branches missed.">                requirement.contains(&quot;create&quot;) || requirement.contains(&quot;user&quot;) ||</span>
<span class="nc bnc" id="L130" title="All 4 branches missed.">                requirement.contains(&quot;person&quot;) || requirement.contains(&quot;customer&quot;) ||</span>
<span class="pc bnc" id="L131" title="All 4 branches missed.">                requirement.contains(&quot;product&quot;) || requirement.contains(&quot;validation&quot;))</span>
    }
    
    private fun isRestApiRequirement(requirement: String): Boolean {
<span class="pc bpc" id="L135" title="1 of 4 branches missed.">        return requirement.contains(&quot;api&quot;) || requirement.contains(&quot;endpoint&quot;) || </span>
<span class="pc bpc" id="L136" title="2 of 4 branches missed.">               requirement.contains(&quot;rest&quot;) || requirement.contains(&quot;crud&quot;)</span>
    }
    
    private fun isConfigurationRequirement(requirement: String): Boolean {
<span class="pc bpc" id="L140" title="1 of 4 branches missed.">        return requirement.contains(&quot;config&quot;) || requirement.contains(&quot;properties&quot;) ||</span>
<span class="pc bpc" id="L141" title="1 of 2 branches missed.">               requirement.contains(&quot;settings&quot;)</span>
    }
    
    private fun isDataModelRequirement(requirement: String): Boolean {
<span class="pc bpc" id="L145" title="1 of 4 branches missed.">        return requirement.contains(&quot;model&quot;) || requirement.contains(&quot;entity&quot;) ||</span>
<span class="pc bpc" id="L146" title="3 of 4 branches missed.">               (requirement.contains(&quot;data&quot;) &amp;&amp; !requirement.contains(&quot;api&quot;))</span>
    }
    
    private fun generateClassCreationTasks(requirement: String, context: ProjectContext): List&lt;ExecutableTask&gt; {
<span class="fc" id="L150">        val tasks = mutableListOf&lt;ExecutableTask&gt;()</span>
        
        // Extract class name from requirement (simplified)
<span class="pc bpc" id="L153" title="1 of 2 branches missed.">        val className = extractClassName(requirement) ?: &quot;MyClass&quot;</span>
<span class="fc bfc" id="L154" title="All 2 branches covered.">        val filePath = if (context.projectPath.isBlank()) {</span>
<span class="fc" id="L155">            &quot;${className}.kt&quot;</span>
        } else {
<span class="fc" id="L157">            &quot;${context.projectPath}/src/main/kotlin/${className}.kt&quot;</span>
        }
        
        // Generate basic class content
<span class="fc" id="L161">        val classContent = generateBasicClassContent(className, requirement, context)</span>
        
<span class="fc" id="L163">        tasks.add(</span>
<span class="fc" id="L164">            ExecutableTask(</span>
<span class="fc" id="L165">                description = &quot;Create $className class file&quot;,</span>
<span class="fc" id="L166">                toolCalls = listOf(</span>
<span class="fc" id="L167">                    ToolCall(</span>
<span class="fc" id="L168">                        toolName = &quot;save-file&quot;,</span>
<span class="fc" id="L169">                        parameters = mapOf(</span>
<span class="fc" id="L170">                            &quot;path&quot; to filePath,</span>
<span class="fc" id="L171">                            &quot;file_content&quot; to classContent</span>
                        )
                    )
                ),
<span class="fc" id="L175">                priority = 1</span>
            )
        )
        
<span class="fc" id="L179">        return tasks</span>
    }
    
    private fun generateRestApiTasks(requirement: String, context: ProjectContext): List&lt;ExecutableTask&gt; {
<span class="fc" id="L183">        val tasks = mutableListOf&lt;ExecutableTask&gt;()</span>

        // Model/Entity task
<span class="fc" id="L186">        val modelTask = ExecutableTask(</span>
<span class="fc" id="L187">            id = &quot;model-task&quot;,</span>
<span class="fc" id="L188">            description = &quot;Create data model/entity class&quot;,</span>
<span class="fc" id="L189">            toolCalls = listOf(</span>
<span class="fc" id="L190">                ToolCall(</span>
<span class="fc" id="L191">                    toolName = &quot;save-file&quot;,</span>
<span class="fc" id="L192">                    parameters = mapOf(</span>
<span class="fc bfc" id="L193" title="All 2 branches covered.">                        &quot;path&quot; to if (context.projectPath.isBlank()) &quot;User.kt&quot; else &quot;${context.projectPath}/src/main/kotlin/User.kt&quot;,</span>
<span class="fc" id="L194">                        &quot;file_content&quot; to generateEntityClass(context)</span>
                    )
                )
            ),
<span class="fc" id="L198">            priority = 1</span>
        )
<span class="fc" id="L200">        tasks.add(modelTask)</span>

        // Service task
<span class="fc" id="L203">        val serviceTask = ExecutableTask(</span>
<span class="fc" id="L204">            id = &quot;service-task&quot;,</span>
<span class="fc" id="L205">            description = &quot;Create service layer for business logic&quot;,</span>
<span class="fc" id="L206">            toolCalls = listOf(</span>
<span class="fc" id="L207">                ToolCall(</span>
<span class="fc" id="L208">                    toolName = &quot;save-file&quot;,</span>
<span class="fc" id="L209">                    parameters = mapOf(</span>
<span class="fc bfc" id="L210" title="All 2 branches covered.">                        &quot;path&quot; to if (context.projectPath.isBlank()) &quot;UserService.kt&quot; else &quot;${context.projectPath}/src/main/kotlin/UserService.kt&quot;,</span>
<span class="fc" id="L211">                        &quot;file_content&quot; to generateServiceClass(context)</span>
                    )
                )
            ),
<span class="fc" id="L215">            dependencies = listOf(modelTask.id),</span>
<span class="fc" id="L216">            priority = 2</span>
        )
<span class="fc" id="L218">        tasks.add(serviceTask)</span>

        // Controller task
<span class="fc" id="L221">        val controllerTask = ExecutableTask(</span>
<span class="fc" id="L222">            id = &quot;controller-task&quot;,</span>
<span class="fc" id="L223">            description = &quot;Create REST controller with CRUD endpoints&quot;,</span>
<span class="fc" id="L224">            toolCalls = listOf(</span>
<span class="fc" id="L225">                ToolCall(</span>
<span class="fc" id="L226">                    toolName = &quot;save-file&quot;,</span>
<span class="fc" id="L227">                    parameters = mapOf(</span>
<span class="fc bfc" id="L228" title="All 2 branches covered.">                        &quot;path&quot; to if (context.projectPath.isBlank()) &quot;UserController.kt&quot; else &quot;${context.projectPath}/src/main/kotlin/UserController.kt&quot;,</span>
<span class="fc" id="L229">                        &quot;file_content&quot; to generateControllerClass(context)</span>
                    )
                )
            ),
<span class="fc" id="L233">            dependencies = listOf(serviceTask.id),</span>
<span class="fc" id="L234">            priority = 3</span>
        )
<span class="fc" id="L236">        tasks.add(controllerTask)</span>

        // Validation task (if mentioned in requirement)
<span class="fc bfc" id="L239" title="All 2 branches covered.">        if (requirement.contains(&quot;validation&quot;)) {</span>
<span class="fc" id="L240">            val validationTask = ExecutableTask(</span>
<span class="fc" id="L241">                id = &quot;validation-task&quot;,</span>
<span class="fc" id="L242">                description = &quot;Add validation annotations and constraints&quot;,</span>
<span class="fc" id="L243">                toolCalls = listOf(</span>
<span class="fc" id="L244">                    ToolCall(</span>
<span class="fc" id="L245">                        toolName = &quot;str-replace-editor&quot;,</span>
<span class="fc" id="L246">                        parameters = mapOf(</span>
<span class="fc" id="L247">                            &quot;path&quot; to &quot;${context.projectPath}/src/main/kotlin/User.kt&quot;,</span>
<span class="fc" id="L248">                            &quot;old_str&quot; to &quot;val name: String&quot;,</span>
<span class="fc" id="L249">                            &quot;new_str&quot; to &quot;@field:NotBlank val name: String&quot;</span>
                        )
                    )
                ),
<span class="fc" id="L253">                dependencies = listOf(modelTask.id),</span>
<span class="fc" id="L254">                priority = 2</span>
            )
<span class="fc" id="L256">            tasks.add(validationTask)</span>
        }

        // Test task
<span class="fc bfc" id="L260" title="All 2 branches covered.">        if (requirement.contains(&quot;test&quot;)) {</span>
<span class="fc" id="L261">            val testTask = ExecutableTask(</span>
<span class="fc" id="L262">                id = &quot;test-task&quot;,</span>
<span class="fc" id="L263">                description = &quot;Create unit tests for API endpoints&quot;,</span>
<span class="fc" id="L264">                toolCalls = listOf(</span>
<span class="fc" id="L265">                    ToolCall(</span>
<span class="fc" id="L266">                        toolName = &quot;save-file&quot;,</span>
<span class="fc" id="L267">                        parameters = mapOf(</span>
<span class="fc" id="L268">                            &quot;path&quot; to &quot;${context.projectPath}/src/test/kotlin/UserControllerTest.kt&quot;,</span>
<span class="fc" id="L269">                            &quot;file_content&quot; to generateTestClass(context)</span>
                        )
                    )
                ),
<span class="fc" id="L273">                dependencies = listOf(controllerTask.id),</span>
<span class="fc" id="L274">                priority = 4</span>
            )
<span class="fc" id="L276">            tasks.add(testTask)</span>
        }

<span class="fc" id="L279">        return tasks</span>
    }
    
    private fun generateConfigurationTasks(requirement: String, context: ProjectContext): List&lt;ExecutableTask&gt; {
<span class="fc" id="L283">        val tasks = mutableListOf&lt;ExecutableTask&gt;()</span>
        
<span class="pc bpc" id="L285" title="1 of 2 branches missed.">        val configContent = if (requirement.contains(&quot;database&quot;)) {</span>
<span class="fc" id="L286">            generateDatabaseConfig(context)</span>
        } else {
<span class="nc" id="L288">            generateGenericConfig()</span>
        }
        
<span class="fc" id="L291">        tasks.add(</span>
<span class="fc" id="L292">            ExecutableTask(</span>
<span class="fc" id="L293">                description = &quot;Create configuration file&quot;,</span>
<span class="fc" id="L294">                toolCalls = listOf(</span>
<span class="fc" id="L295">                    ToolCall(</span>
<span class="fc" id="L296">                        toolName = &quot;save-file&quot;,</span>
<span class="fc" id="L297">                        parameters = mapOf(</span>
<span class="fc" id="L298">                            &quot;path&quot; to &quot;${context.projectPath}/src/main/resources/application.yml&quot;,</span>
<span class="fc" id="L299">                            &quot;file_content&quot; to configContent</span>
                        )
                    )
                ),
<span class="fc" id="L303">                priority = 1</span>
            )
        )
        
<span class="fc" id="L307">        return tasks</span>
    }
    
    private fun generateDataModelTasks(requirement: String, context: ProjectContext): List&lt;ExecutableTask&gt; {
<span class="fc" id="L311">        val tasks = mutableListOf&lt;ExecutableTask&gt;()</span>
        
<span class="fc bfc" id="L313" title="All 2 branches covered.">        val modelContent = if (context.framework == &quot;spring-boot&quot;) {</span>
<span class="fc" id="L314">            generateSpringDataModel()</span>
        } else {
<span class="fc" id="L316">            generatePlainDataModel()</span>
        }
        
<span class="fc" id="L319">        tasks.add(</span>
<span class="fc" id="L320">            ExecutableTask(</span>
<span class="fc" id="L321">                description = &quot;Create data model class&quot;,</span>
<span class="fc" id="L322">                toolCalls = listOf(</span>
<span class="fc" id="L323">                    ToolCall(</span>
<span class="fc" id="L324">                        toolName = &quot;save-file&quot;,</span>
<span class="fc" id="L325">                        parameters = mapOf(</span>
<span class="fc" id="L326">                            &quot;path&quot; to &quot;${context.projectPath}/src/main/kotlin/DataModel.kt&quot;,</span>
<span class="fc" id="L327">                            &quot;file_content&quot; to modelContent</span>
                        )
                    )
                ),
<span class="fc" id="L331">                priority = 1</span>
            )
        )
        
<span class="fc" id="L335">        return tasks</span>
    }
    
    private fun generateGenericTasks(requirement: String, context: ProjectContext): List&lt;ExecutableTask&gt; {
<span class="fc" id="L339">        return listOf(</span>
<span class="fc" id="L340">            ExecutableTask(</span>
<span class="fc" id="L341">                description = &quot;Implement requirement: $requirement&quot;,</span>
<span class="fc" id="L342">                toolCalls = listOf(</span>
<span class="fc" id="L343">                    ToolCall(</span>
<span class="fc" id="L344">                        toolName = &quot;save-file&quot;,</span>
<span class="fc" id="L345">                        parameters = mapOf(</span>
<span class="fc" id="L346">                            &quot;path&quot; to &quot;${context.projectPath}/src/main/kotlin/Implementation.kt&quot;,</span>
<span class="fc" id="L347">                            &quot;file_content&quot; to &quot;// TODO: Implement $requirement&quot;</span>
                        )
                    )
                ),
<span class="fc" id="L351">                priority = 1</span>
            )
        )
    }
    
    private fun assignPrioritiesAndDependencies(tasks: MutableList&lt;ExecutableTask&gt;, context: ProjectContext) {
        // Priority assignment is already done in individual generators
        // This method can be used for additional logic if needed
<span class="fc" id="L359">    }</span>
    
    private fun hasCircularDependencies(tasks: List&lt;ExecutableTask&gt;): Boolean {
<span class="fc" id="L362">        val visited = mutableSetOf&lt;String&gt;()</span>
<span class="fc" id="L363">        val recursionStack = mutableSetOf&lt;String&gt;()</span>
        
        fun dfs(taskId: String): Boolean {
<span class="fc bfc" id="L366" title="All 2 branches covered.">            if (recursionStack.contains(taskId)) return true</span>
<span class="fc bfc" id="L367" title="All 2 branches covered.">            if (visited.contains(taskId)) return false</span>
            
<span class="fc" id="L369">            visited.add(taskId)</span>
<span class="fc" id="L370">            recursionStack.add(taskId)</span>
            
<span class="pc bpc" id="L372" title="1 of 4 branches missed.">            val task = tasks.find { it.id == taskId }</span>
<span class="pc bpc" id="L373" title="2 of 4 branches missed.">            task?.dependencies?.forEach { depId -&gt;</span>
<span class="fc bfc" id="L374" title="All 2 branches covered.">                if (dfs(depId)) return true</span>
<span class="fc" id="L375">            }</span>
            
<span class="fc" id="L377">            recursionStack.remove(taskId)</span>
<span class="fc" id="L378">            return false</span>
        }
        
<span class="fc" id="L381">        return tasks.any { dfs(it.id) }</span>
    }
    
    private fun extractClassName(requirement: String): String? {
        // Simple extraction - look for patterns like &quot;create X class&quot; or &quot;X data class&quot;
<span class="fc" id="L386">        val words = requirement.split(&quot; &quot;)</span>
<span class="fc" id="L387">        val classIndex = words.indexOfFirst { it.lowercase() == &quot;class&quot; }</span>

<span class="fc" id="L389">        return when {</span>
            // First check for specific class names mentioned in the requirement
<span class="fc bfc" id="L391" title="All 2 branches covered.">            requirement.contains(&quot;user&quot;, ignoreCase = true) -&gt; &quot;User&quot;</span>
<span class="pc bpc" id="L392" title="1 of 2 branches missed.">            requirement.contains(&quot;person&quot;, ignoreCase = true) -&gt; &quot;Person&quot;</span>
<span class="pc bpc" id="L393" title="1 of 2 branches missed.">            requirement.contains(&quot;customer&quot;, ignoreCase = true) -&gt; &quot;Customer&quot;</span>
<span class="pc bpc" id="L394" title="1 of 2 branches missed.">            requirement.contains(&quot;product&quot;, ignoreCase = true) -&gt; &quot;Product&quot;</span>
            // Then try to extract from position relative to &quot;class&quot;
<span class="pc bpc" id="L396" title="1 of 2 branches missed.">            classIndex &gt; 0 -&gt; {</span>
<span class="fc" id="L397">                val candidateName = words[classIndex - 1]</span>
                // Skip common words like &quot;data&quot;, &quot;simple&quot;, etc.
<span class="pc bpc" id="L399" title="1 of 2 branches missed.">                if (candidateName.lowercase() in listOf(&quot;data&quot;, &quot;simple&quot;, &quot;basic&quot;, &quot;new&quot;, &quot;a&quot;, &quot;an&quot;, &quot;the&quot;)) {</span>
                    // Look for a proper noun before these words
<span class="pc bpc" id="L401" title="1 of 2 branches missed.">                    if (classIndex &gt; 1) {</span>
<span class="pc bpc" id="L402" title="2 of 4 branches missed.">                        words[classIndex - 2].replaceFirstChar { it.uppercase() }</span>
                    } else {
<span class="nc" id="L404">                        &quot;MyClass&quot;</span>
                    }
                } else {
<span class="nc bnc" id="L407" title="All 4 branches missed.">                    candidateName.replaceFirstChar { it.uppercase() }</span>
                }
            }
<span class="nc" id="L410">            else -&gt; &quot;MyClass&quot;</span>
        }
    }
    
    private fun generateBasicClassContent(className: String, requirement: String, context: ProjectContext): String {
<span class="fc" id="L415">        val properties = extractProperties(requirement)</span>
<span class="fc" id="L416">        val propertiesCode = properties.joinToString(&quot;\n    &quot;) { &quot;val $it: String&quot; }</span>
        
<span class="fc" id="L418">        return &quot;&quot;&quot;</span>
<span class="fc" id="L419">            data class $className(</span>
<span class="fc" id="L420">                $propertiesCode</span>
            )
<span class="fc" id="L422">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun extractProperties(requirement: String): List&lt;String&gt; {
<span class="fc" id="L426">        val properties = mutableListOf&lt;String&gt;()</span>
        
<span class="fc bfc" id="L428" title="All 2 branches covered.">        if (requirement.contains(&quot;name&quot;, ignoreCase = true)) properties.add(&quot;name&quot;)</span>
<span class="fc bfc" id="L429" title="All 2 branches covered.">        if (requirement.contains(&quot;age&quot;, ignoreCase = true)) properties.add(&quot;age&quot;)</span>
<span class="fc bfc" id="L430" title="All 2 branches covered.">        if (requirement.contains(&quot;email&quot;, ignoreCase = true)) properties.add(&quot;email&quot;)</span>
        
<span class="fc bfc" id="L432" title="All 2 branches covered.">        return properties.ifEmpty { listOf(&quot;id&quot;, &quot;name&quot;) }</span>
    }
    
    private fun generateEntityClass(context: ProjectContext): String {
<span class="pc bpc" id="L436" title="1 of 2 branches missed.">        return if (context.framework == &quot;spring-boot&quot;) {</span>
            &quot;&quot;&quot;
                import javax.persistence.*
                
                @Entity
                @Table(name = &quot;users&quot;)
                data class User(
                    @Id
                    @GeneratedValue(strategy = GenerationType.IDENTITY)
                    val id: Long = 0,
                    
                    @Column(nullable = false)
                    val name: String,
                    
                    @Column(nullable = false, unique = true)
                    val email: String
                )
<span class="fc" id="L453">            &quot;&quot;&quot;.trimIndent()</span>
        } else {
            &quot;&quot;&quot;
                data class User(
                    val id: Long,
                    val name: String,
                    val email: String
                )
<span class="nc" id="L461">            &quot;&quot;&quot;.trimIndent()</span>
        }
    }
    
    private fun generateServiceClass(context: ProjectContext): String {
<span class="fc" id="L466">        return &quot;&quot;&quot;</span>
            import org.springframework.stereotype.Service

            @Service
            class UserService {

                fun getAllUsers(): List&lt;User&gt; {
                    // TODO: Implement business logic
                    return emptyList()
                }

                fun createUser(user: User): User {
                    // TODO: Implement business logic
                    return user
                }

                fun getUserById(id: Long): User? {
                    // TODO: Implement business logic
                    return null
                }

                fun updateUser(id: Long, user: User): User {
                    // TODO: Implement business logic
                    return user
                }

                fun deleteUser(id: Long) {
                    // TODO: Implement business logic
                }
            }
<span class="fc" id="L496">        &quot;&quot;&quot;.trimIndent()</span>
    }

    private fun generateControllerClass(context: ProjectContext): String {
<span class="fc" id="L500">        return &quot;&quot;&quot;</span>
            import org.springframework.web.bind.annotation.*

            @RestController
            @RequestMapping(&quot;/api/users&quot;)
            class UserController(private val userService: UserService) {

                @GetMapping
                fun getAllUsers(): List&lt;User&gt; {
                    return userService.getAllUsers()
                }

                @PostMapping
                fun createUser(@RequestBody user: User): User {
                    return userService.createUser(user)
                }

                @GetMapping(&quot;/{id}&quot;)
                fun getUserById(@PathVariable id: Long): User? {
                    return userService.getUserById(id)
                }

                @PutMapping(&quot;/{id}&quot;)
                fun updateUser(@PathVariable id: Long, @RequestBody user: User): User {
                    return userService.updateUser(id, user)
                }

                @DeleteMapping(&quot;/{id}&quot;)
                fun deleteUser(@PathVariable id: Long) {
                    userService.deleteUser(id)
                }
            }
<span class="fc" id="L532">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateTestClass(context: ProjectContext): String {
<span class="fc" id="L536">        return &quot;&quot;&quot;</span>
            import org.junit.jupiter.api.Test
            import org.junit.jupiter.api.Assertions.*
            
            class UserControllerTest {
                
                @Test
                fun `should get all users`() {
                    // TODO: Implement test
                }
                
                @Test
                fun `should create user`() {
                    // TODO: Implement test
                }
                
                @Test
                fun `should get user by id`() {
                    // TODO: Implement test
                }
                
                @Test
                fun `should update user`() {
                    // TODO: Implement test
                }
                
                @Test
                fun `should delete user`() {
                    // TODO: Implement test
                }
            }
<span class="fc" id="L567">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateDatabaseConfig(context: ProjectContext): String {
<span class="fc" id="L571">        return &quot;&quot;&quot;</span>
            spring:
              datasource:
                url: jdbc:h2:mem:testdb
                driver-class-name: org.h2.Driver
                username: sa
                password: 
              jpa:
                hibernate:
                  ddl-auto: create-drop
                show-sql: true
<span class="fc" id="L582">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateGenericConfig(): String {
<span class="nc" id="L586">        return &quot;&quot;&quot;</span>
            # Application Configuration
            app:
              name: MyApplication
              version: 1.0.0
<span class="nc" id="L591">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateSpringDataModel(): String {
<span class="fc" id="L595">        return &quot;&quot;&quot;</span>
            import javax.persistence.*
            
            @Entity
            data class DataModel(
                @Id
                @GeneratedValue(strategy = GenerationType.IDENTITY)
                val id: Long = 0,
                
                val name: String,
                val value: String
            )
<span class="fc" id="L607">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generatePlainDataModel(): String {
<span class="fc" id="L611">        return &quot;&quot;&quot;</span>
            data class DataModel(
                val id: Long,
                val name: String,
                val value: String
            )
<span class="fc" id="L617">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun enhanceCodeWithFeedback(content: String, feedback: String): String {
<span class="fc" id="L621">        var enhancedContent = content</span>
        
<span class="pc bpc" id="L623" title="1 of 2 branches missed.">        if (feedback.contains(&quot;validation&quot;, ignoreCase = true)) {</span>
<span class="fc" id="L624">            enhancedContent = enhancedContent.replace(&quot;val &quot;, &quot;@field:NotBlank val &quot;)</span>
        }
        
<span class="pc bpc" id="L627" title="1 of 2 branches missed.">        if (feedback.contains(&quot;immutable&quot;, ignoreCase = true)) {</span>
<span class="fc" id="L628">            enhancedContent = enhancedContent.replace(&quot;var &quot;, &quot;val &quot;)</span>
        }
        
<span class="fc" id="L631">        return enhancedContent</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>