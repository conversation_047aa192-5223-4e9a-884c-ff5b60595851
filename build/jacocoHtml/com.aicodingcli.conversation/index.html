<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.conversation</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.conversation</span></div><h1>com.aicodingcli.conversation</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">3,014 of 11,863</td><td class="ctr2">74%</td><td class="bar">491 of 995</td><td class="ctr2">50%</td><td class="ctr1">456</td><td class="ctr2">932</td><td class="ctr1">220</td><td class="ctr2">1,461</td><td class="ctr1">104</td><td class="ctr2">429</td><td class="ctr1">12</td><td class="ctr2">72</td></tr></tfoot><tbody><tr><td id="a30"><a href="DefaultRequirementParser.html" class="el_class">DefaultRequirementParser</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="675" alt="675"/><img src="../jacoco-resources/greenbar.gif" width="91" height="10" title="2,141" alt="2,141"/></td><td class="ctr2" id="c38">76%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="180" alt="180"/><img src="../jacoco-resources/greenbar.gif" width="68" height="10" title="242" alt="242"/></td><td class="ctr2" id="e7">57%</td><td class="ctr1" id="f0">142</td><td class="ctr2" id="g0">253</td><td class="ctr1" id="h1">47</td><td class="ctr2" id="i0">278</td><td class="ctr1" id="j16">2</td><td class="ctr2" id="k0">37</td><td class="ctr1" id="l12">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a8"><a href="AiDrivenAutoExecutionEngine.html" class="el_class">AiDrivenAutoExecutionEngine</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="239" alt="239"/><img src="../jacoco-resources/greenbar.gif" width="44" height="10" title="1,054" alt="1,054"/></td><td class="ctr2" id="c36">81%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="26" alt="26"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="30" alt="30"/></td><td class="ctr2" id="e9">53%</td><td class="ctr1" id="f5">17</td><td class="ctr2" id="g2">39</td><td class="ctr1" id="h2">46</td><td class="ctr2" id="i2">233</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k8">11</td><td class="ctr1" id="l13">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a29"><a href="DefaultAutoExecutionEngine.html" class="el_class">DefaultAutoExecutionEngine</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="194" alt="194"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="597" alt="597"/></td><td class="ctr2" id="c39">75%</td><td class="bar" id="d19"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="15" alt="15"/></td><td class="ctr2" id="e0">83%</td><td class="ctr1" id="f19">4</td><td class="ctr2" id="g17">16</td><td class="ctr1" id="h0">52</td><td class="ctr2" id="i3">160</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k24">7</td><td class="ctr1" id="l14">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a31"><a href="DefaultTaskDecomposer.html" class="el_class">DefaultTaskDecomposer</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="167" alt="167"/><img src="../jacoco-resources/greenbar.gif" width="56" height="10" title="1,323" alt="1,323"/></td><td class="ctr2" id="c33">88%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="57" alt="57"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="91" alt="91"/></td><td class="ctr2" id="e4">61%</td><td class="ctr1" id="f1">47</td><td class="ctr2" id="g1">103</td><td class="ctr1" id="h4">14</td><td class="ctr2" id="i1">249</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k1">29</td><td class="ctr1" id="l15">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a63"><a href="ToolMetadata.html" class="el_class">ToolMetadata</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="161" alt="161"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="96" alt="96"/></td><td class="ctr2" id="c57">37%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="26" alt="26"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f6">17</td><td class="ctr2" id="g6">24</td><td class="ctr1" id="h31">0</td><td class="ctr2" id="i17">9</td><td class="ctr1" id="j3">4</td><td class="ctr2" id="k9">11</td><td class="ctr1" id="l16">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a65"><a href="ToolParameter.html" class="el_class">ToolParameter</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="156" alt="156"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="86" alt="86"/></td><td class="ctr2" id="c58">35%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="26" alt="26"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f4">18</td><td class="ctr2" id="g7">24</td><td class="ctr1" id="h32">0</td><td class="ctr2" id="i18">9</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k10">11</td><td class="ctr1" id="l17">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a36"><a href="ExecutionContext.html" class="el_class">ExecutionContext</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="130" alt="130"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="89" alt="89"/></td><td class="ctr2" id="c56">40%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="18" alt="18"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f8">13</td><td class="ctr2" id="g11">20</td><td class="ctr1" id="h33">0</td><td class="ctr2" id="i19">9</td><td class="ctr1" id="j4">4</td><td class="ctr2" id="k11">11</td><td class="ctr1" id="l18">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a34"><a href="ExecutableTask.html" class="el_class">ExecutableTask</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="127" alt="127"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="220" alt="220"/></td><td class="ctr2" id="c47">63%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="17" alt="17"/></td><td class="ctr2" id="e13">40%</td><td class="ctr1" id="f2">23</td><td class="ctr2" id="g4">34</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">11</td><td class="ctr1" id="j5">4</td><td class="ctr2" id="k6">13</td><td class="ctr1" id="l19">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a6"><a href="ActionDecisionResponseJson.html" class="el_class">ActionDecisionResponseJson</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="108" alt="108"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="86" alt="86"/></td><td class="ctr2" id="c54">44%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="e17">27%</td><td class="ctr1" id="f12">10</td><td class="ctr2" id="g12">19</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i21">8</td><td class="ctr1" id="j14">3</td><td class="ctr2" id="k13">10</td><td class="ctr1" id="l20">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a23"><a href="ConversationState.html" class="el_class">ConversationState</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="94" alt="94"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="224" alt="224"/></td><td class="ctr2" id="c42">70%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="25" alt="25"/></td><td class="ctr2" id="e2">73%</td><td class="ctr1" id="f9">13</td><td class="ctr2" id="g5">31</td><td class="ctr1" id="h8">4</td><td class="ctr2" id="i12">12</td><td class="ctr1" id="j6">4</td><td class="ctr2" id="k4">14</td><td class="ctr1" id="l21">0</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a61"><a href="ToolExample.html" class="el_class">ToolExample</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="92" alt="92"/></td><td class="ctr2" id="c60">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f14">8</td><td class="ctr2" id="g24">8</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i33">5</td><td class="ctr1" id="j1">7</td><td class="ctr2" id="k25">7</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a48"><a href="ParsedRequirement.html" class="el_class">ParsedRequirement</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="90" alt="90"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="62" alt="62"/></td><td class="ctr2" id="c55">40%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="10" alt="10"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f13">9</td><td class="ctr2" id="g18">14</td><td class="ctr1" id="h34">0</td><td class="ctr2" id="i27">7</td><td class="ctr1" id="j7">4</td><td class="ctr2" id="k17">9</td><td class="ctr1" id="l22">0</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a25"><a href="ConversationStateManager.html" class="el_class">ConversationStateManager</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="89" alt="89"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="245" alt="245"/></td><td class="ctr2" id="c41">73%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="10" alt="10"/></td><td class="ctr2" id="e8">55%</td><td class="ctr1" id="f15">8</td><td class="ctr2" id="g9">22</td><td class="ctr1" id="h3">15</td><td class="ctr2" id="i5">75</td><td class="ctr1" id="j8">4</td><td class="ctr2" id="k5">14</td><td class="ctr1" id="l23">0</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a9"><a href="AiExecutionRound.html" class="el_class">AiExecutionRound</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="78" alt="78"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="43" alt="43"/></td><td class="ctr2" id="c59">35%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="18" alt="18"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f3">19</td><td class="ctr2" id="g10">21</td><td class="ctr1" id="h6">9</td><td class="ctr2" id="i7">19</td><td class="ctr1" id="j0">10</td><td class="ctr2" id="k7">12</td><td class="ctr1" id="l24">0</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a11"><a href="AnalysisResponseJson.html" class="el_class">AnalysisResponseJson</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="69" alt="69"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="78" alt="78"/></td><td class="ctr2" id="c52">53%</td><td class="bar" id="d22"/><td class="ctr2" id="e10">50%</td><td class="ctr1" id="f23">3</td><td class="ctr2" id="g20">11</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i22">8</td><td class="ctr1" id="j17">2</td><td class="ctr2" id="k14">10</td><td class="ctr1" id="l25">0</td><td class="ctr2" id="m14">1</td></tr><tr><td id="a38"><a href="ExecutionError.html" class="el_class">ExecutionError</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="65" alt="65"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="79" alt="79"/></td><td class="ctr2" id="c51">54%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="e14">33%</td><td class="ctr1" id="f10">13</td><td class="ctr2" id="g15">17</td><td class="ctr1" id="h35">0</td><td class="ctr2" id="i23">8</td><td class="ctr1" id="j9">4</td><td class="ctr2" id="k21">8</td><td class="ctr1" id="l26">0</td><td class="ctr2" id="m15">1</td></tr><tr><td id="a42"><a href="ExecutionStep.html" class="el_class">ExecutionStep</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="64" alt="64"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="124" alt="124"/></td><td class="ctr2" id="c45">65%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="e15">33%</td><td class="ctr1" id="f11">13</td><td class="ctr2" id="g13">19</td><td class="ctr1" id="h36">0</td><td class="ctr2" id="i14">10</td><td class="ctr1" id="j10">4</td><td class="ctr2" id="k15">10</td><td class="ctr1" id="l27">0</td><td class="ctr2" id="m16">1</td></tr><tr><td id="a19"><a href="CompletionEvaluationResponseJson.html" class="el_class">CompletionEvaluationResponseJson</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="64" alt="64"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="72" alt="72"/></td><td class="ctr2" id="c53">52%</td><td class="bar" id="d23"/><td class="ctr2" id="e11">50%</td><td class="ctr1" id="f24">3</td><td class="ctr2" id="g21">10</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i28">7</td><td class="ctr1" id="j18">2</td><td class="ctr2" id="k18">9</td><td class="ctr1" id="l28">0</td><td class="ctr2" id="m17">1</td></tr><tr><td id="a59"><a href="ToolCall.html" class="el_class">ToolCall</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="58" alt="58"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="82" alt="82"/></td><td class="ctr2" id="c48">58%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="7" alt="7"/></td><td class="ctr2" id="e16">30%</td><td class="ctr1" id="f16">8</td><td class="ctr2" id="g19">14</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i29">7</td><td class="ctr1" id="j15">3</td><td class="ctr2" id="k19">9</td><td class="ctr1" id="l29">0</td><td class="ctr2" id="m18">1</td></tr><tr><td id="a67"><a href="ToolResult.html" class="el_class">ToolResult</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="46" alt="46"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="130" alt="130"/></td><td class="ctr2" id="c40">73%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="e12">44%</td><td class="ctr1" id="f17">8</td><td class="ctr2" id="g14">18</td><td class="ctr1" id="h37">0</td><td class="ctr2" id="i24">8</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k20">9</td><td class="ctr1" id="l30">0</td><td class="ctr2" id="m19">1</td></tr><tr><td id="a21"><a href="ConversationSession.html" class="el_class">ConversationSession</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="37" alt="37"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="350" alt="350"/></td><td class="ctr2" id="c30">90%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="25" alt="25"/></td><td class="ctr2" id="e5">59%</td><td class="ctr1" id="f7">17</td><td class="ctr2" id="g3">36</td><td class="ctr1" id="h38">0</td><td class="ctr2" id="i10">17</td><td class="ctr1" id="j44">0</td><td class="ctr2" id="k2">15</td><td class="ctr1" id="l31">0</td><td class="ctr2" id="m20">1</td></tr><tr><td id="a10"><a href="AiExecutionStrategy.html" class="el_class">AiExecutionStrategy</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="34" alt="34"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="147" alt="147"/></td><td class="ctr2" id="c37">81%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="7" alt="7"/></td><td class="ctr2" id="e6">58%</td><td class="ctr1" id="f18">8</td><td class="ctr2" id="g16">17</td><td class="ctr1" id="h39">0</td><td class="ctr2" id="i11">15</td><td class="ctr1" id="j11">4</td><td class="ctr2" id="k12">11</td><td class="ctr1" id="l32">0</td><td class="ctr2" id="m21">1</td></tr><tr><td id="a32"><a href="DefaultToolExecutor.html" class="el_class">DefaultToolExecutor</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="31" alt="31"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="159" alt="159"/></td><td class="ctr2" id="c34">83%</td><td class="bar" id="d21"/><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g22">10</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i6">37</td><td class="ctr1" id="j45">0</td><td class="ctr2" id="k22">8</td><td class="ctr1" id="l33">0</td><td class="ctr2" id="m22">1</td></tr><tr><td id="a28"><a href="DefaultAiPromptEngine.html" class="el_class">DefaultAiPromptEngine</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="24" alt="24"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="389" alt="389"/></td><td class="ctr2" id="c25">94%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="9" alt="9"/></td><td class="ctr2" id="e3">69%</td><td class="ctr1" id="f20">4</td><td class="ctr2" id="g8">23</td><td class="ctr1" id="h10">3</td><td class="ctr2" id="i4">99</td><td class="ctr1" id="j46">0</td><td class="ctr2" id="k3">15</td><td class="ctr1" id="l34">0</td><td class="ctr2" id="m23">1</td></tr><tr><td id="a33"><a href="DurationSerializer.html" class="el_class">DurationSerializer</a></td><td class="bar" id="b24"/><td class="ctr2" id="c61">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f21">4</td><td class="ctr2" id="g34">4</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i39">4</td><td class="ctr1" id="j12">4</td><td class="ctr2" id="k34">4</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m24">1</td></tr><tr><td id="a41"><a href="ExecutionResult$Companion.html" class="el_class">ExecutionResult.Companion</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="28" alt="28"/></td><td class="ctr2" id="c44">66%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g37">3</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i8">18</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k37">3</td><td class="ctr1" id="l35">0</td><td class="ctr2" id="m25">1</td></tr><tr><td id="a50"><a href="ProjectContext.html" class="el_class">ProjectContext</a></td><td class="bar" id="b26"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="56" alt="56"/></td><td class="ctr2" id="c35">82%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f22">4</td><td class="ctr2" id="g25">8</td><td class="ctr1" id="h40">0</td><td class="ctr2" id="i25">8</td><td class="ctr1" id="j13">4</td><td class="ctr2" id="k23">8</td><td class="ctr1" id="l36">0</td><td class="ctr2" id="m26">1</td></tr><tr><td id="a71"><a href="ValidationResult$Companion.html" class="el_class">ValidationResult.Companion</a></td><td class="bar" id="b27"/><td class="ctr2" id="c46">64%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g38">3</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i42">3</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k38">3</td><td class="ctr1" id="l37">0</td><td class="ctr2" id="m27">1</td></tr><tr><td id="a47"><a href="KotlinDurationSerializer.html" class="el_class">KotlinDurationSerializer</a></td><td class="bar" id="b28"/><td class="ctr2" id="c50">55%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g35">4</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i40">4</td><td class="ctr1" id="j19">2</td><td class="ctr2" id="k35">4</td><td class="ctr1" id="l38">0</td><td class="ctr2" id="m28">1</td></tr><tr><td id="a56"><a href="StepResult$Companion.html" class="el_class">StepResult.Companion</a></td><td class="bar" id="b29"/><td class="ctr2" id="c49">56%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g40">2</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i46">2</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k40">2</td><td class="ctr1" id="l39">0</td><td class="ctr2" id="m29">1</td></tr><tr><td id="a68"><a href="ToolResult$Companion.html" class="el_class">ToolResult.Companion</a></td><td class="bar" id="b30"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="40" alt="40"/></td><td class="ctr2" id="c26">93%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g28">5</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i34">5</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k28">5</td><td class="ctr1" id="l40">0</td><td class="ctr2" id="m30">1</td></tr><tr><td id="a15"><a href="AnalysisResult$Success.html" class="el_class">AnalysisResult.Success</a></td><td class="bar" id="b31"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="37" alt="37"/></td><td class="ctr2" id="c27">92%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g26">7</td><td class="ctr1" id="h41">0</td><td class="ctr2" id="i26">8</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k26">7</td><td class="ctr1" id="l41">0</td><td class="ctr2" id="m31">1</td></tr><tr><td id="a70"><a href="ValidationResult.html" class="el_class">ValidationResult</a></td><td class="bar" id="b32"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="34" alt="34"/></td><td class="ctr2" id="c28">91%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g29">5</td><td class="ctr1" id="h42">0</td><td class="ctr2" id="i35">5</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">5</td><td class="ctr1" id="l42">0</td><td class="ctr2" id="m32">1</td></tr><tr><td id="a55"><a href="StepResult.html" class="el_class">StepResult</a></td><td class="bar" id="b33"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="29" alt="29"/></td><td class="ctr2" id="c29">90%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g30">5</td><td class="ctr1" id="h43">0</td><td class="ctr2" id="i36">5</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">5</td><td class="ctr1" id="l43">0</td><td class="ctr2" id="m33">1</td></tr><tr><td id="a2"><a href="ActionDecision$ExecuteTool.html" class="el_class">ActionDecision.ExecuteTool</a></td><td class="bar" id="b34"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="25" alt="25"/></td><td class="ctr2" id="c32">89%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g31">5</td><td class="ctr1" id="h44">0</td><td class="ctr2" id="i32">6</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">5</td><td class="ctr1" id="l44">0</td><td class="ctr2" id="m34">1</td></tr><tr><td id="a17"><a href="CompletionEvaluation$Failure.html" class="el_class">CompletionEvaluation.Failure</a></td><td class="bar" id="b35"/><td class="ctr2" id="c43">70%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g41">2</td><td class="ctr1" id="h45">0</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k41">2</td><td class="ctr1" id="l45">0</td><td class="ctr2" id="m35">1</td></tr><tr><td id="a35"><a href="ExecutableTask$Companion.html" class="el_class">ExecutableTask.Companion</a></td><td class="bar" id="b36"/><td class="ctr2" id="c62">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k55">1</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m36">1</td></tr><tr><td id="a37"><a href="ExecutionContext$Companion.html" class="el_class">ExecutionContext.Companion</a></td><td class="bar" id="b37"/><td class="ctr2" id="c63">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k56">1</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m37">1</td></tr><tr><td id="a66"><a href="ToolParameter$Companion.html" class="el_class">ToolParameter.Companion</a></td><td class="bar" id="b38"/><td class="ctr2" id="c64">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k57">1</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m38">1</td></tr><tr><td id="a24"><a href="ConversationState$Companion.html" class="el_class">ConversationState.Companion</a></td><td class="bar" id="b39"/><td class="ctr2" id="c65">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k58">1</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m39">1</td></tr><tr><td id="a49"><a href="ParsedRequirement$Companion.html" class="el_class">ParsedRequirement.Companion</a></td><td class="bar" id="b40"/><td class="ctr2" id="c66">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k59">1</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m40">1</td></tr><tr><td id="a64"><a href="ToolMetadata$Companion.html" class="el_class">ToolMetadata.Companion</a></td><td class="bar" id="b41"/><td class="ctr2" id="c67">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i55">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k60">1</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m41">1</td></tr><tr><td id="a60"><a href="ToolCall$Companion.html" class="el_class">ToolCall.Companion</a></td><td class="bar" id="b42"/><td class="ctr2" id="c68">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i56">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k61">1</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m42">1</td></tr><tr><td id="a43"><a href="ExecutionStep$Companion.html" class="el_class">ExecutionStep.Companion</a></td><td class="bar" id="b43"/><td class="ctr2" id="c69">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k62">1</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m43">1</td></tr><tr><td id="a39"><a href="ExecutionError$Companion.html" class="el_class">ExecutionError.Companion</a></td><td class="bar" id="b44"/><td class="ctr2" id="c70">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k63">1</td><td class="ctr1" id="l10">1</td><td class="ctr2" id="m44">1</td></tr><tr><td id="a62"><a href="ToolExample$Companion.html" class="el_class">ToolExample.Companion</a></td><td class="bar" id="b45"/><td class="ctr2" id="c71">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i59">1</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k64">1</td><td class="ctr1" id="l11">1</td><td class="ctr2" id="m45">1</td></tr><tr><td id="a44"><a href="InstantSerializer.html" class="el_class">InstantSerializer</a></td><td class="bar" id="b46"/><td class="ctr2" id="c31">90%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g36">4</td><td class="ctr1" id="h46">0</td><td class="ctr2" id="i41">4</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k36">4</td><td class="ctr1" id="l46">0</td><td class="ctr2" id="m46">1</td></tr><tr><td id="a45"><a href="Intent.html" class="el_class">Intent</a></td><td class="bar" id="b47"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="117" alt="117"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">0</td><td class="ctr2" id="g42">2</td><td class="ctr1" id="h47">0</td><td class="ctr2" id="i9">18</td><td class="ctr1" id="j47">0</td><td class="ctr2" id="k42">2</td><td class="ctr1" id="l47">0</td><td class="ctr2" id="m47">1</td></tr><tr><td id="a40"><a href="ExecutionResult.html" class="el_class">ExecutionResult</a></td><td class="bar" id="b48"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="73" alt="73"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">0</td><td class="ctr2" id="g23">10</td><td class="ctr1" id="h48">0</td><td class="ctr2" id="i15">10</td><td class="ctr1" id="j48">0</td><td class="ctr2" id="k16">10</td><td class="ctr1" id="l48">0</td><td class="ctr2" id="m48">1</td></tr><tr><td id="a51"><a href="RequirementCategory.html" class="el_class">RequirementCategory</a></td><td class="bar" id="b49"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="67" alt="67"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">0</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h49">0</td><td class="ctr2" id="i16">10</td><td class="ctr1" id="j49">0</td><td class="ctr2" id="k65">1</td><td class="ctr1" id="l49">0</td><td class="ctr2" id="m49">1</td></tr><tr><td id="a26"><a href="ConversationStatus.html" class="el_class">ConversationStatus</a></td><td class="bar" id="b50"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="63" alt="63"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">0</td><td class="ctr2" id="g43">2</td><td class="ctr1" id="h50">0</td><td class="ctr2" id="i20">9</td><td class="ctr1" id="j50">0</td><td class="ctr2" id="k43">2</td><td class="ctr1" id="l50">0</td><td class="ctr2" id="m50">1</td></tr><tr><td id="a57"><a href="TaskStatus.html" class="el_class">TaskStatus</a></td><td class="bar" id="b51"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="51" alt="51"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">0</td><td class="ctr2" id="g44">2</td><td class="ctr1" id="h51">0</td><td class="ctr2" id="i30">7</td><td class="ctr1" id="j51">0</td><td class="ctr2" id="k44">2</td><td class="ctr1" id="l51">0</td><td class="ctr2" id="m51">1</td></tr><tr><td id="a18"><a href="CompletionEvaluation$Success.html" class="el_class">CompletionEvaluation.Success</a></td><td class="bar" id="b52"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="34" alt="34"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">0</td><td class="ctr2" id="g27">6</td><td class="ctr1" id="h52">0</td><td class="ctr2" id="i31">7</td><td class="ctr1" id="j52">0</td><td class="ctr2" id="k27">6</td><td class="ctr1" id="l52">0</td><td class="ctr2" id="m52">1</td></tr><tr><td id="a0"><a href="ActionDecision$Companion.html" class="el_class">ActionDecision.Companion</a></td><td class="bar" id="b53"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="33" alt="33"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">0</td><td class="ctr2" id="g32">5</td><td class="ctr1" id="h53">0</td><td class="ctr2" id="i37">5</td><td class="ctr1" id="j53">0</td><td class="ctr2" id="k32">5</td><td class="ctr1" id="l53">0</td><td class="ctr2" id="m53">1</td></tr><tr><td id="a53"><a href="SafetyCheckResult.html" class="el_class">SafetyCheckResult</a></td><td class="bar" id="b54"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="32" alt="32"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">0</td><td class="ctr2" id="g33">5</td><td class="ctr1" id="h54">0</td><td class="ctr2" id="i38">5</td><td class="ctr1" id="j54">0</td><td class="ctr2" id="k33">5</td><td class="ctr1" id="l54">0</td><td class="ctr2" id="m54">1</td></tr><tr><td id="a69"><a href="ToolRiskLevel.html" class="el_class">ToolRiskLevel</a></td><td class="bar" id="b55"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="25" alt="25"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">0</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h55">0</td><td class="ctr2" id="i43">3</td><td class="ctr1" id="j55">0</td><td class="ctr2" id="k66">1</td><td class="ctr1" id="l55">0</td><td class="ctr2" id="m55">1</td></tr><tr><td id="a54"><a href="SafetyCheckResult$Companion.html" class="el_class">SafetyCheckResult.Companion</a></td><td class="bar" id="b56"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="25" alt="25"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">0</td><td class="ctr2" id="g39">3</td><td class="ctr1" id="h56">0</td><td class="ctr2" id="i44">3</td><td class="ctr1" id="j56">0</td><td class="ctr2" id="k39">3</td><td class="ctr1" id="l56">0</td><td class="ctr2" id="m56">1</td></tr><tr><td id="a52"><a href="RequirementComplexity.html" class="el_class">RequirementComplexity</a></td><td class="bar" id="b57"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="25" alt="25"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">0</td><td class="ctr2" id="g67">1</td><td class="ctr1" id="h57">0</td><td class="ctr2" id="i45">3</td><td class="ctr1" id="j57">0</td><td class="ctr2" id="k67">1</td><td class="ctr1" id="l57">0</td><td class="ctr2" id="m57">1</td></tr><tr><td id="a13"><a href="AnalysisResult$Companion.html" class="el_class">AnalysisResult.Companion</a></td><td class="bar" id="b58"/><td class="ctr2" id="c11">100%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">0</td><td class="ctr2" id="g45">2</td><td class="ctr1" id="h58">0</td><td class="ctr2" id="i47">2</td><td class="ctr1" id="j58">0</td><td class="ctr2" id="k45">2</td><td class="ctr1" id="l58">0</td><td class="ctr2" id="m58">1</td></tr><tr><td id="a16"><a href="CompletionEvaluation$Companion.html" class="el_class">CompletionEvaluation.Companion</a></td><td class="bar" id="b59"/><td class="ctr2" id="c12">100%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f59">0</td><td class="ctr2" id="g46">2</td><td class="ctr1" id="h59">0</td><td class="ctr2" id="i48">2</td><td class="ctr1" id="j59">0</td><td class="ctr2" id="k46">2</td><td class="ctr1" id="l59">0</td><td class="ctr2" id="m59">1</td></tr><tr><td id="a5"><a href="ActionDecision$WaitUser.html" class="el_class">ActionDecision.WaitUser</a></td><td class="bar" id="b60"/><td class="ctr2" id="c13">100%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f60">0</td><td class="ctr2" id="g47">2</td><td class="ctr1" id="h60">0</td><td class="ctr2" id="i60">1</td><td class="ctr1" id="j60">0</td><td class="ctr2" id="k47">2</td><td class="ctr1" id="l60">0</td><td class="ctr2" id="m60">1</td></tr><tr><td id="a3"><a href="ActionDecision$Fail.html" class="el_class">ActionDecision.Fail</a></td><td class="bar" id="b61"/><td class="ctr2" id="c14">100%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f61">0</td><td class="ctr2" id="g48">2</td><td class="ctr1" id="h61">0</td><td class="ctr2" id="i61">1</td><td class="ctr1" id="j61">0</td><td class="ctr2" id="k48">2</td><td class="ctr1" id="l61">0</td><td class="ctr2" id="m61">1</td></tr><tr><td id="a14"><a href="AnalysisResult$Failure.html" class="el_class">AnalysisResult.Failure</a></td><td class="bar" id="b62"/><td class="ctr2" id="c15">100%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f62">0</td><td class="ctr2" id="g49">2</td><td class="ctr1" id="h62">0</td><td class="ctr2" id="i62">1</td><td class="ctr1" id="j62">0</td><td class="ctr2" id="k49">2</td><td class="ctr1" id="l62">0</td><td class="ctr2" id="m62">1</td></tr><tr><td id="a4"><a href="ActionDecision$Failure.html" class="el_class">ActionDecision.Failure</a></td><td class="bar" id="b63"/><td class="ctr2" id="c16">100%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f63">0</td><td class="ctr2" id="g50">2</td><td class="ctr1" id="h63">0</td><td class="ctr2" id="i63">1</td><td class="ctr1" id="j63">0</td><td class="ctr2" id="k50">2</td><td class="ctr1" id="l63">0</td><td class="ctr2" id="m63">1</td></tr><tr><td id="a1"><a href="ActionDecision$Complete.html" class="el_class">ActionDecision.Complete</a></td><td class="bar" id="b64"/><td class="ctr2" id="c17">100%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f64">0</td><td class="ctr2" id="g51">2</td><td class="ctr1" id="h64">0</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j64">0</td><td class="ctr2" id="k51">2</td><td class="ctr1" id="l64">0</td><td class="ctr2" id="m64">1</td></tr><tr><td id="a27"><a href="ConversationStatus$Companion.html" class="el_class">ConversationStatus.Companion</a></td><td class="bar" id="b65"/><td class="ctr2" id="c18">100%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f65">0</td><td class="ctr2" id="g52">2</td><td class="ctr1" id="h65">0</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j65">0</td><td class="ctr2" id="k52">2</td><td class="ctr1" id="l65">0</td><td class="ctr2" id="m65">1</td></tr><tr><td id="a58"><a href="TaskStatus$Companion.html" class="el_class">TaskStatus.Companion</a></td><td class="bar" id="b66"/><td class="ctr2" id="c19">100%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f66">0</td><td class="ctr2" id="g53">2</td><td class="ctr1" id="h66">0</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j66">0</td><td class="ctr2" id="k53">2</td><td class="ctr1" id="l66">0</td><td class="ctr2" id="m66">1</td></tr><tr><td id="a46"><a href="Intent$Companion.html" class="el_class">Intent.Companion</a></td><td class="bar" id="b67"/><td class="ctr2" id="c20">100%</td><td class="bar" id="d67"/><td class="ctr2" id="e67">n/a</td><td class="ctr1" id="f67">0</td><td class="ctr2" id="g54">2</td><td class="ctr1" id="h67">0</td><td class="ctr2" id="i67">1</td><td class="ctr1" id="j67">0</td><td class="ctr2" id="k54">2</td><td class="ctr1" id="l67">0</td><td class="ctr2" id="m67">1</td></tr><tr><td id="a7"><a href="ActionDecisionResponseJson$Companion.html" class="el_class">ActionDecisionResponseJson.Companion</a></td><td class="bar" id="b68"/><td class="ctr2" id="c21">100%</td><td class="bar" id="d68"/><td class="ctr2" id="e68">n/a</td><td class="ctr1" id="f68">0</td><td class="ctr2" id="g68">1</td><td class="ctr1" id="h68">0</td><td class="ctr2" id="i68">1</td><td class="ctr1" id="j68">0</td><td class="ctr2" id="k68">1</td><td class="ctr1" id="l68">0</td><td class="ctr2" id="m68">1</td></tr><tr><td id="a20"><a href="CompletionEvaluationResponseJson$Companion.html" class="el_class">CompletionEvaluationResponseJson.Companion</a></td><td class="bar" id="b69"/><td class="ctr2" id="c22">100%</td><td class="bar" id="d69"/><td class="ctr2" id="e69">n/a</td><td class="ctr1" id="f69">0</td><td class="ctr2" id="g69">1</td><td class="ctr1" id="h69">0</td><td class="ctr2" id="i69">1</td><td class="ctr1" id="j69">0</td><td class="ctr2" id="k69">1</td><td class="ctr1" id="l69">0</td><td class="ctr2" id="m69">1</td></tr><tr><td id="a22"><a href="ConversationSession$Companion.html" class="el_class">ConversationSession.Companion</a></td><td class="bar" id="b70"/><td class="ctr2" id="c23">100%</td><td class="bar" id="d70"/><td class="ctr2" id="e70">n/a</td><td class="ctr1" id="f70">0</td><td class="ctr2" id="g70">1</td><td class="ctr1" id="h70">0</td><td class="ctr2" id="i70">1</td><td class="ctr1" id="j70">0</td><td class="ctr2" id="k70">1</td><td class="ctr1" id="l70">0</td><td class="ctr2" id="m70">1</td></tr><tr><td id="a12"><a href="AnalysisResponseJson$Companion.html" class="el_class">AnalysisResponseJson.Companion</a></td><td class="bar" id="b71"/><td class="ctr2" id="c24">100%</td><td class="bar" id="d71"/><td class="ctr2" id="e71">n/a</td><td class="ctr1" id="f71">0</td><td class="ctr2" id="g71">1</td><td class="ctr1" id="h71">0</td><td class="ctr2" id="i71">1</td><td class="ctr1" id="j71">0</td><td class="ctr2" id="k71">1</td><td class="ctr1" id="l71">0</td><td class="ctr2" id="m71">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>