<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ActionDecisionResponseJson</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_class">ActionDecisionResponseJson</span></div><h1>ActionDecisionResponseJson</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">108 of 194</td><td class="ctr2">44%</td><td class="bar">13 of 18</td><td class="ctr2">27%</td><td class="ctr1">10</td><td class="ctr2">19</td><td class="ctr1">2</td><td class="ctr2">8</td><td class="ctr1">3</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a9"><a href="AiPromptEngine.kt.html#L288" class="el_method">write$Self$ai_coding_cli(ActionDecisionResponseJson, CompositeEncoder, SerialDescriptor)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="67" alt="67"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="AiPromptEngine.kt.html#L289" class="el_method">ActionDecisionResponseJson(String, String, Map, String, double)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="18" alt="18"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h0">6</td><td class="ctr2" id="i0">6</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="AiPromptEngine.kt.html#L289" class="el_method">ActionDecisionResponseJson(String, String, Map, String, double, int, DefaultConstructorMarker)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="18" alt="18"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a0"><a href="AiPromptEngine.kt.html#L288" class="el_method">ActionDecisionResponseJson(int, String, String, Map, String, double, SerializationConstructorMarker)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="69" height="10" title="39" alt="39"/></td><td class="ctr2" id="c6">88%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="5" alt="5"/></td><td class="ctr2" id="e0">83%</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">3</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="AiPromptEngine.kt.html#L288" class="el_method">static {...}</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="57" height="10" title="32" alt="32"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="AiPromptEngine.kt.html#L290" class="el_method">getAction()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="AiPromptEngine.kt.html#L291" class="el_method">getToolName()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="AiPromptEngine.kt.html#L292" class="el_method">getParameters()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="AiPromptEngine.kt.html#L293" class="el_method">getReasoning()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a4"><a href="AiPromptEngine.kt.html#L294" class="el_method">getConfidence()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>