<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DefaultTaskDecomposer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_class">DefaultTaskDecomposer</span></div><h1>DefaultTaskDecomposer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">167 of 1,490</td><td class="ctr2">88%</td><td class="bar">57 of 148</td><td class="ctr2">61%</td><td class="ctr1">47</td><td class="ctr2">103</td><td class="ctr1">14</td><td class="ctr2">249</td><td class="ctr1">1</td><td class="ctr2">29</td></tr></tfoot><tbody><tr><td id="a4"><a href="TaskDecomposer.kt.html#L386" class="el_method">extractClassName(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="59" alt="59"/><img src="../jacoco-resources/greenbar.gif" width="69" height="10" title="163" alt="163"/></td><td class="ctr2" id="c25">73%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="10" alt="10"/></td><td class="ctr2" id="e13">45%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g1">12</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i4">15</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a26"><a href="TaskDecomposer.kt.html#L125" class="el_method">isSimpleClassCreation(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="45" alt="45"/><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="76" alt="76"/></td><td class="ctr2" id="c27">62%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="10" alt="10"/></td><td class="ctr2" id="e14">38%</td><td class="ctr1" id="f0">11</td><td class="ctr2" id="g0">14</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a27"><a href="TaskDecomposer.kt.html#L63" class="el_method">refineTask(ExecutableTask, String, Continuation)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="37" alt="37"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="96" alt="96"/></td><td class="ctr2" id="c26">72%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="5" alt="5"/></td><td class="ctr2" id="e15">31%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">9</td><td class="ctr1" id="h0">5</td><td class="ctr2" id="i1">22</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a24"><a href="TaskDecomposer.kt.html#L145" class="el_method">isDataModelRequirement(String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="31" alt="31"/></td><td class="ctr2" id="c24">77%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">50%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a28"><a href="TaskDecomposer.kt.html#L100" class="el_method">validateTaskSequence(List, Continuation)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="51" alt="51"/></td><td class="ctr2" id="c23">87%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="5" alt="5"/></td><td class="ctr2" id="e4">83%</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g9">4</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i7">13</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a21"><a href="TaskDecomposer.kt.html#L366" class="el_method">hasCircularDependencies$dfs(Set, Set, List, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="29" height="10" title="69" alt="69"/></td><td class="ctr2" id="c21">95%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="11" alt="11"/></td><td class="ctr2" id="e5">78%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">10</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a8"><a href="TaskDecomposer.kt.html#L150" class="el_method">generateClassCreationTasks(String, ProjectContext)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="72" alt="72"/></td><td class="ctr2" id="c19">97%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="e6">75%</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a9"><a href="TaskDecomposer.kt.html#L283" class="el_method">generateConfigurationTasks(String, ProjectContext)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="62" alt="62"/></td><td class="ctr2" id="c20">96%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="1" alt="1"/></td><td class="ctr2" id="e10">50%</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">15</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="TaskDecomposer.kt.html#L586" class="el_method">generateGenericConfig()</a></td><td class="bar" id="b8"/><td class="ctr2" id="c28">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a13"><a href="TaskDecomposer.kt.html#L436" class="el_method">generateEntityClass(ProjectContext)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c22">88%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="1" alt="1"/></td><td class="ctr2" id="e11">50%</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i14">3</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a17"><a href="TaskDecomposer.kt.html#L183" class="el_method">generateRestApiTasks(String, ProjectContext)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="283" alt="283"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d12"><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i0">64</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a1"><a href="TaskDecomposer.kt.html#L29" class="el_method">decompose(String, ProjectContext, Continuation)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="34" height="10" title="81" alt="81"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d13"><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g5">6</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i3">16</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a5"><a href="TaskDecomposer.kt.html#L426" class="el_method">extractProperties(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="61" alt="61"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d14"><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i13">5</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a12"><a href="TaskDecomposer.kt.html#L311" class="el_method">generateDataModelTasks(String, ProjectContext)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="59" alt="59"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d15"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i6">15</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a15"><a href="TaskDecomposer.kt.html#L339" class="el_method">generateGenericTasks(String, ProjectContext)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="42" alt="42"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i9">10</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a25"><a href="TaskDecomposer.kt.html#L135" class="el_method">isRestApiRequirement(String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="40" alt="40"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="5" alt="5"/></td><td class="ctr2" id="e8">62%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g8">5</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a3"><a href="TaskDecomposer.kt.html#L621" class="el_method">enhanceCodeWithFeedback(String, String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="34" alt="34"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">50%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g12">3</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i11">6</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a23"><a href="TaskDecomposer.kt.html#L140" class="el_method">isConfigurationRequirement(String)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="31" alt="31"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">66%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g10">4</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a22"><a href="TaskDecomposer.kt.html#L362" class="el_method">hasCircularDependencies(List)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="22" alt="22"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i15">3</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a7"><a href="TaskDecomposer.kt.html#L415" class="el_method">generateBasicClassContent(String, String, ProjectContext)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="22" alt="22"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i12">6</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a6"><a href="TaskDecomposer.kt.html#L416" class="el_method">generateBasicClassContent$lambda$10(String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a2"><a href="TaskDecomposer.kt.html#L26" class="el_method">DefaultTaskDecomposer()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a18"><a href="TaskDecomposer.kt.html#L466" class="el_method">generateServiceClass(ProjectContext)</a></td><td class="bar" id="b22"/><td class="ctr2" id="c12">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a10"><a href="TaskDecomposer.kt.html#L500" class="el_method">generateControllerClass(ProjectContext)</a></td><td class="bar" id="b23"/><td class="ctr2" id="c13">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a20"><a href="TaskDecomposer.kt.html#L536" class="el_method">generateTestClass(ProjectContext)</a></td><td class="bar" id="b24"/><td class="ctr2" id="c14">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a11"><a href="TaskDecomposer.kt.html#L571" class="el_method">generateDatabaseConfig(ProjectContext)</a></td><td class="bar" id="b25"/><td class="ctr2" id="c15">100%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">0</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i23">2</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a19"><a href="TaskDecomposer.kt.html#L595" class="el_method">generateSpringDataModel()</a></td><td class="bar" id="b26"/><td class="ctr2" id="c16">100%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">0</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">0</td><td class="ctr2" id="i24">2</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a16"><a href="TaskDecomposer.kt.html#L611" class="el_method">generatePlainDataModel()</a></td><td class="bar" id="b27"/><td class="ctr2" id="c17">100%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">0</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">0</td><td class="ctr2" id="i25">2</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a0"><a href="TaskDecomposer.kt.html#L359" class="el_method">assignPrioritiesAndDependencies(List, ProjectContext)</a></td><td class="bar" id="b28"/><td class="ctr2" id="c18">100%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">0</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">0</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k28">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>