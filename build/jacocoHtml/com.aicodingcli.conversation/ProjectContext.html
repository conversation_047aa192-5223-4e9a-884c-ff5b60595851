<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ProjectContext</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_class">ProjectContext</span></div><h1>ProjectContext</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">12 of 68</td><td class="ctr2">82%</td><td class="bar">0 of 0</td><td class="ctr2">n/a</td><td class="ctr1">4</td><td class="ctr2">8</td><td class="ctr1">0</td><td class="ctr2">8</td><td class="ctr1">4</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a3"><a href="ConversationModels.kt.html#L238" class="el_method">getLanguage()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d0"/><td class="ctr2" id="e0">n/a</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g0">1</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="ConversationModels.kt.html#L240" class="el_method">getBuildTool()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="ConversationModels.kt.html#L241" class="el_method">getDependencies()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="ConversationModels.kt.html#L242" class="el_method">getMetadata()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="ConversationModels.kt.html#L236" class="el_method">ProjectContext(String, String, String, String, List, Map, int, DefaultConstructorMarker)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="29" alt="29"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i1">6</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a6"><a href="ConversationModels.kt.html#L236" class="el_method">ProjectContext(String, String, String, String, List, Map)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="86" height="10" title="21" alt="21"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i0">7</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="ConversationModels.kt.html#L237" class="el_method">getProjectPath()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="ConversationModels.kt.html#L239" class="el_method">getFramework()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>