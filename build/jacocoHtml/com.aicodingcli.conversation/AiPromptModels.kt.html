<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiPromptModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_source">AiPromptModels.kt</span></div><h1>AiPromptModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation

import kotlinx.serialization.Serializable

/**
 * Execution context for AI prompt decisions
 */
<span class="pc bnc" id="L8" title="All 18 branches missed.">@Serializable</span>
<span class="fc" id="L9">data class ExecutionContext(</span>
<span class="fc" id="L10">    val projectPath: String,</span>
<span class="fc" id="L11">    val language: String,</span>
<span class="fc" id="L12">    val framework: String,</span>
<span class="fc" id="L13">    val buildTool: String,</span>
<span class="pc" id="L14">    val workingDirectory: String = System.getProperty(&quot;user.dir&quot;),</span>
<span class="pc" id="L15">    val metadata: Map&lt;String, String&gt; = emptyMap()</span>
<span class="fc" id="L16">)</span>

/**
 * Result of requirement analysis
 */
sealed class AnalysisResult {
<span class="fc" id="L22">    data class Success(</span>
<span class="fc" id="L23">        val intent: String,</span>
<span class="fc" id="L24">        val complexity: RequirementComplexity,</span>
<span class="fc" id="L25">        val category: RequirementCategory,</span>
<span class="fc" id="L26">        val prerequisites: List&lt;String&gt;,</span>
<span class="fc" id="L27">        val estimatedSteps: Int,</span>
<span class="pc" id="L28">        val reasoning: String</span>
<span class="fc" id="L29">    ) : AnalysisResult()</span>
    
<span class="fc" id="L31">    data class Failure(val error: String) : AnalysisResult()</span>
    
    companion object {
        fun success(
            intent: String,
            complexity: RequirementComplexity,
            category: RequirementCategory,
            prerequisites: List&lt;String&gt;,
            estimatedSteps: Int,
            reasoning: String
<span class="fc" id="L41">        ): AnalysisResult = Success(intent, complexity, category, prerequisites, estimatedSteps, reasoning)</span>
        
<span class="fc" id="L43">        fun failure(error: String): AnalysisResult = Failure(error)</span>
    }
}

/**
 * Decision for next action to take
 */
sealed class ActionDecision {
<span class="fc" id="L51">    data class ExecuteTool(</span>
<span class="fc" id="L52">        val toolName: String,</span>
<span class="fc" id="L53">        val parameters: Map&lt;String, String&gt;,</span>
<span class="pc" id="L54">        val reasoning: String,</span>
<span class="fc" id="L55">        val confidence: Double</span>
<span class="fc" id="L56">    ) : ActionDecision()</span>
    
<span class="fc" id="L58">    data class Complete(val reasoning: String) : ActionDecision()</span>
<span class="fc" id="L59">    data class WaitUser(val reasoning: String) : ActionDecision()</span>
<span class="fc" id="L60">    data class Fail(val reasoning: String) : ActionDecision()</span>
<span class="fc" id="L61">    data class Failure(val error: String) : ActionDecision()</span>
    
    companion object {
        fun executeTool(
            toolName: String,
            parameters: Map&lt;String, String&gt;,
            reasoning: String,
            confidence: Double
<span class="fc" id="L69">        ): ActionDecision = ExecuteTool(toolName, parameters, reasoning, confidence)</span>
        
<span class="fc" id="L71">        fun complete(reasoning: String): ActionDecision = Complete(reasoning)</span>
<span class="fc" id="L72">        fun waitUser(reasoning: String): ActionDecision = WaitUser(reasoning)</span>
<span class="fc" id="L73">        fun fail(reasoning: String): ActionDecision = Fail(reasoning)</span>
<span class="fc" id="L74">        fun failure(error: String): ActionDecision = Failure(error)</span>
    }
}

/**
 * Evaluation of requirement completion
 */
sealed class CompletionEvaluation {
<span class="fc" id="L82">    data class Success(</span>
<span class="fc" id="L83">        val completed: Boolean,</span>
<span class="fc" id="L84">        val completionPercentage: Int,</span>
<span class="fc" id="L85">        val missingItems: List&lt;String&gt;,</span>
<span class="fc" id="L86">        val summary: String,</span>
<span class="fc" id="L87">        val nextSteps: List&lt;String&gt;</span>
<span class="fc" id="L88">    ) : CompletionEvaluation()</span>
    
<span class="pc" id="L90">    data class Failure(val error: String) : CompletionEvaluation()</span>
    
    companion object {
        fun success(
            completed: Boolean,
            completionPercentage: Int,
            missingItems: List&lt;String&gt;,
            summary: String,
            nextSteps: List&lt;String&gt;
<span class="fc" id="L99">        ): CompletionEvaluation = Success(completed, completionPercentage, missingItems, summary, nextSteps)</span>
        
<span class="fc" id="L101">        fun failure(error: String): CompletionEvaluation = Failure(error)</span>
    }
}

/**
 * Complexity levels for requirements
 */
enum class RequirementComplexity {
<span class="fc" id="L109">    SIMPLE,     // Single file operation, basic task</span>
<span class="fc" id="L110">    MODERATE,   // Multiple files, some logic</span>
<span class="fc" id="L111">    COMPLEX     // Multiple components, complex logic, dependencies</span>
}

/**
 * Categories for requirements
 */
enum class RequirementCategory {
<span class="fc" id="L118">    FILE_OPERATION,     // Creating, reading, writing files</span>
<span class="fc" id="L119">    CODE_GENERATION,    // Generating classes, functions, tests</span>
<span class="fc" id="L120">    TESTING,           // Running tests, creating test cases</span>
<span class="fc" id="L121">    REFACTORING,       // Code improvements, restructuring</span>
<span class="fc" id="L122">    ANALYSIS,          // Code analysis, diagnostics</span>
<span class="fc" id="L123">    BUILD_OPERATION,   // Build, compile, package operations</span>
<span class="fc" id="L124">    GIT_OPERATION,     // Version control operations</span>
<span class="fc" id="L125">    CONFIGURATION,     // Configuration file changes</span>
<span class="fc" id="L126">    DOCUMENTATION,     // Creating or updating documentation</span>
<span class="fc" id="L127">    OTHER              // Other types of operations</span>
}

/**
 * AI-driven execution strategy
 */
<span class="fc" id="L133">data class AiExecutionStrategy(</span>
<span class="fc" id="L134">    val maxRounds: Int = 25,</span>
<span class="fc" id="L135">    val confidenceThreshold: Double = 0.7,</span>
<span class="fc" id="L136">    val enableSafetyChecks: Boolean = true,</span>
<span class="pc" id="L137">    val allowFileOperations: Boolean = true,</span>
<span class="pc" id="L138">    val allowNetworkOperations: Boolean = false,</span>
<span class="pc" id="L139">    val allowSystemCommands: Boolean = false,</span>
<span class="pc" id="L140">    val requireUserConfirmation: List&lt;String&gt; = listOf(&quot;remove-files&quot;, &quot;git-operations&quot;)</span>
<span class="fc" id="L141">) {</span>
    fun shouldRequireConfirmation(toolName: String): Boolean {
<span class="fc" id="L143">        return requireUserConfirmation.contains(toolName)</span>
    }
    
    fun isToolAllowed(toolName: String): Boolean {
<span class="fc" id="L147">        return when {</span>
<span class="pc bpc" id="L148" title="3 of 4 branches missed.">            !allowFileOperations &amp;&amp; toolName in listOf(&quot;save-file&quot;, &quot;str-replace-editor&quot;, &quot;remove-files&quot;) -&gt; false</span>
<span class="pc bpc" id="L149" title="1 of 4 branches missed.">            !allowNetworkOperations &amp;&amp; toolName in listOf(&quot;web-search&quot;, &quot;web-fetch&quot;, &quot;open-browser&quot;) -&gt; false</span>
<span class="pc bpc" id="L150" title="1 of 4 branches missed.">            !allowSystemCommands &amp;&amp; toolName in listOf(&quot;launch-process&quot;, &quot;git-operations&quot;) -&gt; false</span>
<span class="fc" id="L151">            else -&gt; true</span>
        }
    }
}

/**
 * Safety check result
 */
<span class="fc" id="L159">data class SafetyCheckResult(</span>
<span class="fc" id="L160">    val allowed: Boolean,</span>
<span class="fc" id="L161">    val reason: String,</span>
<span class="fc" id="L162">    val requiresConfirmation: Boolean = false</span>
<span class="fc" id="L163">) {</span>
    companion object {
<span class="fc" id="L165">        fun allowed(): SafetyCheckResult = SafetyCheckResult(true, &quot;Operation allowed&quot;)</span>
<span class="fc" id="L166">        fun denied(reason: String): SafetyCheckResult = SafetyCheckResult(false, reason)</span>
        fun requiresConfirmation(reason: String): SafetyCheckResult = 
<span class="fc" id="L168">            SafetyCheckResult(true, reason, requiresConfirmation = true)</span>
    }
}

/**
 * AI execution round information
 */
<span class="fc" id="L175">data class AiExecutionRound(</span>
<span class="pc" id="L176">    val roundNumber: Int,</span>
<span class="pc" id="L177">    val requirement: String,</span>
<span class="pc" id="L178">    val analysisResult: AnalysisResult?,</span>
<span class="pc" id="L179">    val actionDecision: ActionDecision?,</span>
<span class="pc" id="L180">    val executionStep: ExecutionStep?,</span>
<span class="pc" id="L181">    val safetyCheck: SafetyCheckResult?,</span>
<span class="pc" id="L182">    val completionEvaluation: CompletionEvaluation?,</span>
<span class="pc" id="L183">    val timestamp: java.time.Instant = java.time.Instant.now()</span>
<span class="fc" id="L184">) {</span>
    val isSuccessful: Boolean
<span class="nc bnc" id="L186" title="All 6 branches missed.">        get() = executionStep?.result?.success == true</span>
    
    val shouldContinue: Boolean
<span class="nc" id="L189">        get() = when (actionDecision) {</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">            is ActionDecision.ExecuteTool -&gt; true</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">            is ActionDecision.Complete -&gt; false</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">            is ActionDecision.WaitUser -&gt; false</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">            is ActionDecision.Fail -&gt; false</span>
<span class="nc bnc" id="L194" title="All 2 branches missed.">            is ActionDecision.Failure -&gt; false</span>
<span class="nc bnc" id="L195" title="All 2 branches missed.">            null -&gt; false</span>
<span class="nc" id="L196">        }</span>
}

// ToolMetadata, ToolParameter, ToolExample, and ToolRiskLevel are already defined in ConversationModels.kt
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>