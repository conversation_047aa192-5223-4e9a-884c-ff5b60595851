<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DefaultAutoExecutionEngine</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation</a> &gt; <span class="el_class">DefaultAutoExecutionEngine</span></div><h1>DefaultAutoExecutionEngine</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">194 of 791</td><td class="ctr2">75%</td><td class="bar">3 of 18</td><td class="ctr2">83%</td><td class="ctr1">4</td><td class="ctr2">16</td><td class="ctr1">52</td><td class="ctr2">160</td><td class="ctr1">1</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a5"><a href="AutoExecutionEngine.kt.html#L148" class="el_method">executeTasksInSession(ConversationSession, TimeMark, Continuation)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="136" alt="136"/><img src="../jacoco-resources/greenbar.gif" width="86" height="10" title="346" alt="346"/></td><td class="ctr2" id="c3">71%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">83%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h0">28</td><td class="ctr2" id="i0">91</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="AutoExecutionEngine.kt.html#L105" class="el_method">continueConversation(String, Continuation)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="44" alt="44"/></td><td class="ctr2" id="c5">63%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">14</td><td class="ctr2" id="i2">19</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="AutoExecutionEngine.kt.html#L41" class="el_method">executeConversation(String, Continuation)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="161" alt="161"/></td><td class="ctr2" id="c2">92%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">7</td><td class="ctr2" id="i1">33</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="AutoExecutionEngine.kt.html#L33" class="el_method">DefaultAutoExecutionEngine(ConversationStateManager, TaskDecomposer, RequirementParser, ToolExecutor, int, int, DefaultConstructorMarker)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="13" alt="13"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="AutoExecutionEngine.kt.html#L132" class="el_method">executeStep(ExecutionStep, Continuation)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="16" alt="16"/></td><td class="ctr2" id="c4">69%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">2</td><td class="ctr2" id="i3">6</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="AutoExecutionEngine.kt.html#L33" class="el_method">DefaultAutoExecutionEngine(ConversationStateManager, TaskDecomposer, RequirementParser, ToolExecutor, int)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="18" alt="18"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a6"><a href="AutoExecutionEngine.kt.html#L142" class="el_method">setMaxExecutionRounds(int)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="12" alt="12"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>