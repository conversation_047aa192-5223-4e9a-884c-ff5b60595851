<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.ai.providers</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.ai.providers</span></div><h1>com.aicodingcli.ai.providers</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4,049 of 6,577</td><td class="ctr2">38%</td><td class="bar">488 of 621</td><td class="ctr2">21%</td><td class="ctr1">513</td><td class="ctr2">660</td><td class="ctr1">231</td><td class="ctr2">585</td><td class="ctr1">215</td><td class="ctr2">343</td><td class="ctr1">32</td><td class="ctr2">76</td></tr></tfoot><tbody><tr><td id="a46"><a href="OllamaStreamResponse.html" class="el_class">OllamaStreamResponse</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="428" alt="428"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="66" alt="66"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f0">48</td><td class="ctr2" id="g0">48</td><td class="ctr1" id="h0">21</td><td class="ctr2" id="i3">21</td><td class="ctr1" id="j0">15</td><td class="ctr2" id="k0">15</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a39"><a href="OllamaResponse.html" class="el_class">OllamaResponse</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="286" alt="286"/><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="115" alt="115"/></td><td class="ctr2" id="c39">28%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="43" alt="43"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="15" alt="15"/></td><td class="ctr2" id="e23">25%</td><td class="ctr1" id="f2">30</td><td class="ctr2" id="g1">44</td><td class="ctr1" id="h11">7</td><td class="ctr2" id="i4">21</td><td class="ctr1" id="j6">8</td><td class="ctr2" id="k1">15</td><td class="ctr1" id="l32">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a20"><a href="ClaudeStreamMessage.html" class="el_class">ClaudeStreamMessage</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="256" alt="256"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="18" alt="18"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f6">22</td><td class="ctr2" id="g9">22</td><td class="ctr1" id="h5">13</td><td class="ctr2" id="i11">13</td><td class="ctr1" id="j1">13</td><td class="ctr2" id="k2">13</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a18"><a href="ClaudeStreamEvent.html" class="el_class">ClaudeStreamEvent</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="216" alt="216"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="34" alt="34"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f4">26</td><td class="ctr2" id="g5">26</td><td class="ctr1" id="h10">8</td><td class="ctr2" id="i20">8</td><td class="ctr1" id="j3">9</td><td class="ctr2" id="k14">9</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a29"><a href="OllamaModel.html" class="el_class">OllamaModel</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="201" alt="201"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="47" alt="47"/></td><td class="ctr2" id="c43">18%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="29" alt="29"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="e27">14%</td><td class="ctr1" id="f3">27</td><td class="ctr2" id="g4">28</td><td class="ctr1" id="h8">9</td><td class="ctr2" id="i12">12</td><td class="ctr1" id="j2">10</td><td class="ctr2" id="k5">11</td><td class="ctr1" id="l33">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a16"><a href="ClaudeStreamDelta.html" class="el_class">ClaudeStreamDelta</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="47" height="10" title="171" alt="171"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="47" height="10" title="26" alt="26"/></td><td class="ctr2" id="e31">0%</td><td class="ctr1" id="f7">21</td><td class="ctr2" id="g10">21</td><td class="ctr1" id="h9">9</td><td class="ctr2" id="i18">9</td><td class="ctr1" id="j7">8</td><td class="ctr2" id="k16">8</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a35"><a href="OllamaOptions.html" class="el_class">OllamaOptions</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="156" alt="156"/><img src="../jacoco-resources/greenbar.gif" width="47" height="10" title="171" alt="171"/></td><td class="ctr2" id="c20">52%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="32" alt="32"/><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="18" alt="18"/></td><td class="ctr2" id="e18">36%</td><td class="ctr1" id="f1">32</td><td class="ctr2" id="g2">36</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i13">12</td><td class="ctr1" id="j9">7</td><td class="ctr2" id="k6">11</td><td class="ctr1" id="l34">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a31"><a href="OllamaModelDetails.html" class="el_class">OllamaModelDetails</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="136" alt="136"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="76" alt="76"/></td><td class="ctr2" id="c34">35%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="e22">27%</td><td class="ctr1" id="f10">16</td><td class="ctr2" id="g12">20</td><td class="ctr1" id="h12">7</td><td class="ctr2" id="i15">10</td><td class="ctr1" id="j4">9</td><td class="ctr2" id="k7">11</td><td class="ctr1" id="l35">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a72"><a href="OpenAiStreamResponse.html" class="el_class">OpenAiStreamResponse</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="128" alt="128"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e32">0%</td><td class="ctr1" id="f18">10</td><td class="ctr2" id="g24">10</td><td class="ctr1" id="h13">7</td><td class="ctr2" id="i22">7</td><td class="ctr1" id="j5">9</td><td class="ctr2" id="k15">9</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a9"><a href="ClaudeRequest.html" class="el_class">ClaudeRequest</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="125" alt="125"/><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="172" alt="172"/></td><td class="ctr2" id="c18">57%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="14" alt="14"/></td><td class="ctr2" id="e17">41%</td><td class="ctr1" id="f5">23</td><td class="ctr2" id="g3">29</td><td class="ctr1" id="h60">0</td><td class="ctr2" id="i14">11</td><td class="ctr1" id="j8">8</td><td class="ctr2" id="k3">12</td><td class="ctr1" id="l36">0</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a61"><a href="OpenAiModel.html" class="el_class">OpenAiModel</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="114" alt="114"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="28" alt="28"/></td><td class="ctr2" id="c42">19%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="e25">16%</td><td class="ctr1" id="f11">16</td><td class="ctr2" id="g14">17</td><td class="ctr1" id="h16">6</td><td class="ctr2" id="i23">7</td><td class="ctr1" id="j10">7</td><td class="ctr2" id="k17">8</td><td class="ctr1" id="l37">0</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a70"><a href="OpenAiStreamDelta.html" class="el_class">OpenAiStreamDelta</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="109" alt="109"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="18" alt="18"/></td><td class="ctr2" id="e33">0%</td><td class="ctr1" id="f12">15</td><td class="ctr2" id="g16">15</td><td class="ctr1" id="h17">5</td><td class="ctr2" id="i28">5</td><td class="ctr1" id="j14">6</td><td class="ctr2" id="k22">6</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a48"><a href="OpenAiChatRequest.html" class="el_class">OpenAiChatRequest</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="106" alt="106"/><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="116" alt="116"/></td><td class="ctr2" id="c21">52%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="9" alt="9"/></td><td class="ctr2" id="e19">34%</td><td class="ctr1" id="f8">20</td><td class="ctr2" id="g8">23</td><td class="ctr1" id="h24">2</td><td class="ctr2" id="i19">9</td><td class="ctr1" id="j11">7</td><td class="ctr2" id="k12">10</td><td class="ctr1" id="l38">0</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a11"><a href="ClaudeResponse.html" class="el_class">ClaudeResponse</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="105" alt="105"/><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="86" alt="86"/></td><td class="ctr2" id="c26">45%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f21">7</td><td class="ctr2" id="g19">13</td><td class="ctr1" id="h18">5</td><td class="ctr2" id="i16">10</td><td class="ctr1" id="j15">6</td><td class="ctr2" id="k4">12</td><td class="ctr1" id="l39">0</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a14"><a href="ClaudeService$streamChat$2$1.html" class="el_class">ClaudeService.streamChat.2.new FlowCollector() {...}</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="105" alt="105"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="47" height="10" title="26" alt="26"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f13">15</td><td class="ctr2" id="g17">15</td><td class="ctr1" id="h1">16</td><td class="ctr2" id="i5">16</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k38">1</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m14">1</td></tr><tr><td id="a54"><a href="OpenAiError.html" class="el_class">OpenAiError</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="103" alt="103"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="39" alt="39"/></td><td class="ctr2" id="c41">27%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="e26">16%</td><td class="ctr1" id="f15">13</td><td class="ctr2" id="g15">17</td><td class="ctr1" id="h25">2</td><td class="ctr2" id="i24">7</td><td class="ctr1" id="j18">4</td><td class="ctr2" id="k18">8</td><td class="ctr1" id="l40">0</td><td class="ctr2" id="m15">1</td></tr><tr><td id="a37"><a href="OllamaRequest.html" class="el_class">OllamaRequest</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="102" alt="102"/><img src="../jacoco-resources/greenbar.gif" width="39" height="10" title="141" alt="141"/></td><td class="ctr2" id="c17">58%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="9" alt="9"/></td><td class="ctr2" id="e20">34%</td><td class="ctr1" id="f9">20</td><td class="ctr2" id="g6">24</td><td class="ctr1" id="h61">0</td><td class="ctr2" id="i17">10</td><td class="ctr1" id="j12">7</td><td class="ctr2" id="k8">11</td><td class="ctr1" id="l41">0</td><td class="ctr2" id="m16">1</td></tr><tr><td id="a68"><a href="OpenAiStreamChoice.html" class="el_class">OpenAiStreamChoice</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="99" alt="99"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="10" alt="10"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f16">12</td><td class="ctr2" id="g22">12</td><td class="ctr1" id="h14">7</td><td class="ctr2" id="i25">7</td><td class="ctr1" id="j13">7</td><td class="ctr2" id="k21">7</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m17">1</td></tr><tr><td id="a66"><a href="OpenAiService$streamChat$2$1.html" class="el_class">OpenAiService.streamChat.2.new FlowCollector() {...}</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="90" alt="90"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="23" alt="23"/></td><td class="ctr2" id="e36">0%</td><td class="ctr1" id="f14">14</td><td class="ctr2" id="g18">14</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i6">16</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k39">1</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m18">1</td></tr><tr><td id="a67"><a href="OpenAiService$streamChat$2.html" class="el_class">OpenAiService.streamChat.new Function2() {...}</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="88" alt="88"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h3">14</td><td class="ctr2" id="i7">14</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k40">1</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m19">1</td></tr><tr><td id="a15"><a href="ClaudeService$streamChat$2.html" class="el_class">ClaudeService.streamChat.new Function2() {...}</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="87" alt="87"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h4">14</td><td class="ctr2" id="i8">14</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k41">1</td><td class="ctr1" id="l10">1</td><td class="ctr2" id="m20">1</td></tr><tr><td id="a50"><a href="OpenAiChatResponse.html" class="el_class">OpenAiChatResponse</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="80" alt="80"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="69" alt="69"/></td><td class="ctr2" id="c24">46%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f24">6</td><td class="ctr2" id="g23">11</td><td class="ctr1" id="h19">4</td><td class="ctr2" id="i21">8</td><td class="ctr1" id="j16">5</td><td class="ctr2" id="k13">10</td><td class="ctr1" id="l42">0</td><td class="ctr2" id="m21">1</td></tr><tr><td id="a41"><a href="OllamaResponseMessage.html" class="el_class">OllamaResponseMessage</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="76" alt="76"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="47" alt="47"/></td><td class="ctr2" id="c32">38%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">20%</td><td class="ctr1" id="f19">10</td><td class="ctr2" id="g20">13</td><td class="ctr1" id="h22">3</td><td class="ctr2" id="i26">6</td><td class="ctr1" id="j17">5</td><td class="ctr2" id="k19">8</td><td class="ctr1" id="l43">0</td><td class="ctr2" id="m22">1</td></tr><tr><td id="a13"><a href="ClaudeService.html" class="el_class">ClaudeService</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="58" alt="58"/><img src="../jacoco-resources/greenbar.gif" width="91" height="10" title="328" alt="328"/></td><td class="ctr2" id="c12">84%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="10" alt="10"/></td><td class="ctr2" id="e15">43%</td><td class="ctr1" id="f17">11</td><td class="ctr2" id="g7">24</td><td class="ctr1" id="h7">11</td><td class="ctr2" id="i0">84</td><td class="ctr1" id="j60">0</td><td class="ctr2" id="k9">11</td><td class="ctr1" id="l44">0</td><td class="ctr2" id="m23">1</td></tr><tr><td id="a65"><a href="OpenAiService.html" class="el_class">OpenAiService</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="53" alt="53"/><img src="../jacoco-resources/greenbar.gif" width="78" height="10" title="281" alt="281"/></td><td class="ctr2" id="c13">84%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="7" alt="7"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f22">7</td><td class="ctr2" id="g13">20</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i1">79</td><td class="ctr1" id="j61">0</td><td class="ctr2" id="k10">11</td><td class="ctr1" id="l45">0</td><td class="ctr2" id="m24">1</td></tr><tr><td id="a27"><a href="OllamaMessage.html" class="el_class">OllamaMessage</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="53" alt="53"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="70" alt="70"/></td><td class="ctr2" id="c19">56%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="e21">30%</td><td class="ctr1" id="f20">9</td><td class="ctr2" id="g21">13</td><td class="ctr1" id="h62">0</td><td class="ctr2" id="i27">6</td><td class="ctr1" id="j19">4</td><td class="ctr2" id="k20">8</td><td class="ctr1" id="l46">0</td><td class="ctr2" id="m25">1</td></tr><tr><td id="a52"><a href="OpenAiChoice.html" class="el_class">OpenAiChoice</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="43" alt="43"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="23" alt="23"/></td><td class="ctr2" id="c35">34%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f27">4</td><td class="ctr2" id="g25">7</td><td class="ctr1" id="h26">2</td><td class="ctr2" id="i29">5</td><td class="ctr1" id="j21">3</td><td class="ctr2" id="k23">6</td><td class="ctr1" id="l47">0</td><td class="ctr2" id="m26">1</td></tr><tr><td id="a63"><a href="OpenAiModelsResponse.html" class="el_class">OpenAiModelsResponse</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="39" alt="39"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="32" alt="32"/></td><td class="ctr2" id="c25">45%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e5">50%</td><td class="ctr1" id="f25">5</td><td class="ctr2" id="g26">7</td><td class="ctr1" id="h23">3</td><td class="ctr2" id="i32">4</td><td class="ctr1" id="j20">4</td><td class="ctr2" id="k24">6</td><td class="ctr1" id="l48">0</td><td class="ctr2" id="m27">1</td></tr><tr><td id="a43"><a href="OllamaService.html" class="el_class">OllamaService</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="36" alt="36"/><img src="../jacoco-resources/greenbar.gif" width="83" height="10" title="298" alt="298"/></td><td class="ctr2" id="c11">89%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">63%</td><td class="ctr1" id="f23">7</td><td class="ctr2" id="g11">21</td><td class="ctr1" id="h15">7</td><td class="ctr2" id="i2">79</td><td class="ctr1" id="j62">0</td><td class="ctr2" id="k11">11</td><td class="ctr1" id="l49">0</td><td class="ctr2" id="m28">1</td></tr><tr><td id="a74"><a href="OpenAiUsage.html" class="el_class">OpenAiUsage</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="36" alt="36"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="26" alt="26"/></td><td class="ctr2" id="c30">41%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e6">50%</td><td class="ctr1" id="f32">3</td><td class="ctr2" id="g27">7</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i30">5</td><td class="ctr1" id="j27">2</td><td class="ctr2" id="k25">6</td><td class="ctr1" id="l50">0</td><td class="ctr2" id="m29">1</td></tr><tr><td id="a4"><a href="ClaudeErrorResponse.html" class="el_class">ClaudeErrorResponse</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="32" alt="32"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="17" alt="17"/></td><td class="ctr2" id="c36">34%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e7">50%</td><td class="ctr1" id="f28">4</td><td class="ctr2" id="g29">6</td><td class="ctr1" id="h27">2</td><td class="ctr2" id="i33">4</td><td class="ctr1" id="j22">3</td><td class="ctr2" id="k26">5</td><td class="ctr1" id="l51">0</td><td class="ctr2" id="m30">1</td></tr><tr><td id="a0"><a href="ClaudeContent.html" class="el_class">ClaudeContent</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="30" alt="30"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="17" alt="17"/></td><td class="ctr2" id="c33">36%</td><td class="bar" id="d31"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e8">50%</td><td class="ctr1" id="f29">4</td><td class="ctr2" id="g30">6</td><td class="ctr1" id="h28">2</td><td class="ctr2" id="i34">4</td><td class="ctr1" id="j23">3</td><td class="ctr2" id="k27">5</td><td class="ctr1" id="l52">0</td><td class="ctr2" id="m31">1</td></tr><tr><td id="a58"><a href="OpenAiException.html" class="el_class">OpenAiException</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="28" alt="28"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c40">28%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f33">3</td><td class="ctr2" id="g38">4</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i31">5</td><td class="ctr1" id="j24">3</td><td class="ctr2" id="k33">4</td><td class="ctr1" id="l53">0</td><td class="ctr2" id="m32">1</td></tr><tr><td id="a33"><a href="OllamaModelsResponse.html" class="el_class">OllamaModelsResponse</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="27" alt="27"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="25" alt="25"/></td><td class="ctr2" id="c22">48%</td><td class="bar" id="d32"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e9">50%</td><td class="ctr1" id="f30">4</td><td class="ctr2" id="g31">6</td><td class="ctr1" id="h29">2</td><td class="ctr2" id="i40">3</td><td class="ctr1" id="j25">3</td><td class="ctr2" id="k28">5</td><td class="ctr1" id="l54">0</td><td class="ctr2" id="m33">1</td></tr><tr><td id="a22"><a href="ClaudeUsage.html" class="el_class">ClaudeUsage</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="27" alt="27"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="20" alt="20"/></td><td class="ctr2" id="c28">42%</td><td class="bar" id="d33"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e10">50%</td><td class="ctr1" id="f34">3</td><td class="ctr2" id="g32">6</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i35">4</td><td class="ctr1" id="j28">2</td><td class="ctr2" id="k29">5</td><td class="ctr1" id="l55">0</td><td class="ctr2" id="m34">1</td></tr><tr><td id="a2"><a href="ClaudeError.html" class="el_class">ClaudeError</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="27" alt="27"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="20" alt="20"/></td><td class="ctr2" id="c29">42%</td><td class="bar" id="d34"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e11">50%</td><td class="ctr1" id="f35">3</td><td class="ctr2" id="g33">6</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i36">4</td><td class="ctr1" id="j29">2</td><td class="ctr2" id="k30">5</td><td class="ctr1" id="l56">0</td><td class="ctr2" id="m35">1</td></tr><tr><td id="a7"><a href="ClaudeMessage.html" class="el_class">ClaudeMessage</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="22" alt="22"/></td><td class="ctr2" id="c23">46%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e37">0%</td><td class="ctr1" id="f31">4</td><td class="ctr2" id="g34">6</td><td class="ctr1" id="h63">0</td><td class="ctr2" id="i37">4</td><td class="ctr1" id="j26">3</td><td class="ctr2" id="k31">5</td><td class="ctr1" id="l57">0</td><td class="ctr2" id="m36">1</td></tr><tr><td id="a56"><a href="OpenAiErrorResponse.html" class="el_class">OpenAiErrorResponse</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="14" alt="14"/></td><td class="ctr2" id="c31">41%</td><td class="bar" id="d35"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e12">50%</td><td class="ctr1" id="f36">3</td><td class="ctr2" id="g36">5</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i41">3</td><td class="ctr1" id="j30">2</td><td class="ctr2" id="k34">4</td><td class="ctr1" id="l58">0</td><td class="ctr2" id="m37">1</td></tr><tr><td id="a6"><a href="ClaudeException.html" class="el_class">ClaudeException</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c38">29%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f38">2</td><td class="ctr2" id="g39">3</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i38">4</td><td class="ctr1" id="j31">2</td><td class="ctr2" id="k36">3</td><td class="ctr1" id="l59">0</td><td class="ctr2" id="m38">1</td></tr><tr><td id="a24"><a href="OllamaErrorResponse.html" class="el_class">OllamaErrorResponse</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="14" alt="14"/></td><td class="ctr2" id="c27">43%</td><td class="bar" id="d36"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e13">50%</td><td class="ctr1" id="f37">3</td><td class="ctr2" id="g37">5</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i42">3</td><td class="ctr1" id="j32">2</td><td class="ctr2" id="k35">4</td><td class="ctr1" id="l60">0</td><td class="ctr2" id="m39">1</td></tr><tr><td id="a45"><a href="OllamaService$streamChat$2.html" class="el_class">OllamaService.streamChat.new Function2() {...}</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="68" alt="68"/></td><td class="ctr2" id="c15">80%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f64">0</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h20">4</td><td class="ctr2" id="i9">14</td><td class="ctr1" id="j63">0</td><td class="ctr2" id="k42">1</td><td class="ctr1" id="l61">0</td><td class="ctr2" id="m40">1</td></tr><tr><td id="a44"><a href="OllamaService$streamChat$2$1.html" class="el_class">OllamaService.streamChat.2.new FlowCollector() {...}</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="50" alt="50"/></td><td class="ctr2" id="c16">78%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="5" alt="5"/></td><td class="ctr2" id="e16">41%</td><td class="ctr1" id="f26">5</td><td class="ctr2" id="g28">7</td><td class="ctr1" id="h21">4</td><td class="ctr2" id="i10">14</td><td class="ctr1" id="j64">0</td><td class="ctr2" id="k43">1</td><td class="ctr1" id="l62">0</td><td class="ctr2" id="m41">1</td></tr><tr><td id="a26"><a href="OllamaException.html" class="el_class">OllamaException</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c37">33%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g40">2</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i43">3</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">2</td><td class="ctr1" id="l63">0</td><td class="ctr2" id="m42">1</td></tr><tr><td id="a59"><a href="OpenAiMessage.html" class="el_class">OpenAiMessage</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="39" alt="39"/></td><td class="ctr2" id="c14">82%</td><td class="bar" id="d37"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e14">50%</td><td class="ctr1" id="f39">2</td><td class="ctr2" id="g35">6</td><td class="ctr1" id="h64">0</td><td class="ctr2" id="i39">4</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k32">5</td><td class="ctr1" id="l64">0</td><td class="ctr2" id="m43">1</td></tr><tr><td id="a60"><a href="OpenAiMessage$Companion.html" class="el_class">OpenAiMessage.Companion</a></td><td class="bar" id="b44"/><td class="ctr2" id="c55">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k44">1</td><td class="ctr1" id="l11">1</td><td class="ctr2" id="m44">1</td></tr><tr><td id="a3"><a href="ClaudeError$Companion.html" class="el_class">ClaudeError.Companion</a></td><td class="bar" id="b45"/><td class="ctr2" id="c56">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k45">1</td><td class="ctr1" id="l12">1</td><td class="ctr2" id="m45">1</td></tr><tr><td id="a71"><a href="OpenAiStreamDelta$Companion.html" class="el_class">OpenAiStreamDelta.Companion</a></td><td class="bar" id="b46"/><td class="ctr2" id="c57">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k46">1</td><td class="ctr1" id="l13">1</td><td class="ctr2" id="m46">1</td></tr><tr><td id="a1"><a href="ClaudeContent$Companion.html" class="el_class">ClaudeContent.Companion</a></td><td class="bar" id="b47"/><td class="ctr2" id="c58">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k47">1</td><td class="ctr1" id="l14">1</td><td class="ctr2" id="m47">1</td></tr><tr><td id="a75"><a href="OpenAiUsage$Companion.html" class="el_class">OpenAiUsage.Companion</a></td><td class="bar" id="b48"/><td class="ctr2" id="c59">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k48">1</td><td class="ctr1" id="l15">1</td><td class="ctr2" id="m48">1</td></tr><tr><td id="a53"><a href="OpenAiChoice$Companion.html" class="el_class">OpenAiChoice.Companion</a></td><td class="bar" id="b49"/><td class="ctr2" id="c60">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k49">1</td><td class="ctr1" id="l16">1</td><td class="ctr2" id="m49">1</td></tr><tr><td id="a30"><a href="OllamaModel$Companion.html" class="el_class">OllamaModel.Companion</a></td><td class="bar" id="b50"/><td class="ctr2" id="c61">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k50">1</td><td class="ctr1" id="l17">1</td><td class="ctr2" id="m50">1</td></tr><tr><td id="a69"><a href="OpenAiStreamChoice$Companion.html" class="el_class">OpenAiStreamChoice.Companion</a></td><td class="bar" id="b51"/><td class="ctr2" id="c62">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k51">1</td><td class="ctr1" id="l18">1</td><td class="ctr2" id="m51">1</td></tr><tr><td id="a73"><a href="OpenAiStreamResponse$Companion.html" class="el_class">OpenAiStreamResponse.Companion</a></td><td class="bar" id="b52"/><td class="ctr2" id="c63">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k52">1</td><td class="ctr1" id="l19">1</td><td class="ctr2" id="m52">1</td></tr><tr><td id="a47"><a href="OllamaStreamResponse$Companion.html" class="el_class">OllamaStreamResponse.Companion</a></td><td class="bar" id="b53"/><td class="ctr2" id="c64">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k53">1</td><td class="ctr1" id="l20">1</td><td class="ctr2" id="m53">1</td></tr><tr><td id="a28"><a href="OllamaMessage$Companion.html" class="el_class">OllamaMessage.Companion</a></td><td class="bar" id="b54"/><td class="ctr2" id="c65">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k54">1</td><td class="ctr1" id="l21">1</td><td class="ctr2" id="m54">1</td></tr><tr><td id="a17"><a href="ClaudeStreamDelta$Companion.html" class="el_class">ClaudeStreamDelta.Companion</a></td><td class="bar" id="b55"/><td class="ctr2" id="c66">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i55">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k55">1</td><td class="ctr1" id="l22">1</td><td class="ctr2" id="m55">1</td></tr><tr><td id="a21"><a href="ClaudeStreamMessage$Companion.html" class="el_class">ClaudeStreamMessage.Companion</a></td><td class="bar" id="b56"/><td class="ctr2" id="c67">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i56">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k56">1</td><td class="ctr1" id="l23">1</td><td class="ctr2" id="m56">1</td></tr><tr><td id="a42"><a href="OllamaResponseMessage$Companion.html" class="el_class">OllamaResponseMessage.Companion</a></td><td class="bar" id="b57"/><td class="ctr2" id="c68">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k57">1</td><td class="ctr1" id="l24">1</td><td class="ctr2" id="m57">1</td></tr><tr><td id="a32"><a href="OllamaModelDetails$Companion.html" class="el_class">OllamaModelDetails.Companion</a></td><td class="bar" id="b58"/><td class="ctr2" id="c69">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h53">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k58">1</td><td class="ctr1" id="l25">1</td><td class="ctr2" id="m58">1</td></tr><tr><td id="a8"><a href="ClaudeMessage$Companion.html" class="el_class">ClaudeMessage.Companion</a></td><td class="bar" id="b59"/><td class="ctr2" id="c70">0%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h54">1</td><td class="ctr2" id="i59">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k59">1</td><td class="ctr1" id="l26">1</td><td class="ctr2" id="m59">1</td></tr><tr><td id="a23"><a href="ClaudeUsage$Companion.html" class="el_class">ClaudeUsage.Companion</a></td><td class="bar" id="b60"/><td class="ctr2" id="c71">0%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f59">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h55">1</td><td class="ctr2" id="i60">1</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k60">1</td><td class="ctr1" id="l27">1</td><td class="ctr2" id="m60">1</td></tr><tr><td id="a55"><a href="OpenAiError$Companion.html" class="el_class">OpenAiError.Companion</a></td><td class="bar" id="b61"/><td class="ctr2" id="c72">0%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f60">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h56">1</td><td class="ctr2" id="i61">1</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k61">1</td><td class="ctr1" id="l28">1</td><td class="ctr2" id="m61">1</td></tr><tr><td id="a62"><a href="OpenAiModel$Companion.html" class="el_class">OpenAiModel.Companion</a></td><td class="bar" id="b62"/><td class="ctr2" id="c73">0%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f61">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h57">1</td><td class="ctr2" id="i62">1</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k62">1</td><td class="ctr1" id="l29">1</td><td class="ctr2" id="m62">1</td></tr><tr><td id="a19"><a href="ClaudeStreamEvent$Companion.html" class="el_class">ClaudeStreamEvent.Companion</a></td><td class="bar" id="b63"/><td class="ctr2" id="c74">0%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f62">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h58">1</td><td class="ctr2" id="i63">1</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k63">1</td><td class="ctr1" id="l30">1</td><td class="ctr2" id="m63">1</td></tr><tr><td id="a36"><a href="OllamaOptions$Companion.html" class="el_class">OllamaOptions.Companion</a></td><td class="bar" id="b64"/><td class="ctr2" id="c75">0%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f63">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h59">1</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k64">1</td><td class="ctr1" id="l31">1</td><td class="ctr2" id="m64">1</td></tr><tr><td id="a5"><a href="ClaudeErrorResponse$Companion.html" class="el_class">ClaudeErrorResponse.Companion</a></td><td class="bar" id="b65"/><td class="ctr2" id="c0">100%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f65">0</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h65">0</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j65">0</td><td class="ctr2" id="k65">1</td><td class="ctr1" id="l65">0</td><td class="ctr2" id="m65">1</td></tr><tr><td id="a57"><a href="OpenAiErrorResponse$Companion.html" class="el_class">OpenAiErrorResponse.Companion</a></td><td class="bar" id="b66"/><td class="ctr2" id="c1">100%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f66">0</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h66">0</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j66">0</td><td class="ctr2" id="k66">1</td><td class="ctr1" id="l66">0</td><td class="ctr2" id="m66">1</td></tr><tr><td id="a51"><a href="OpenAiChatResponse$Companion.html" class="el_class">OpenAiChatResponse.Companion</a></td><td class="bar" id="b67"/><td class="ctr2" id="c2">100%</td><td class="bar" id="d67"/><td class="ctr2" id="e67">n/a</td><td class="ctr1" id="f67">0</td><td class="ctr2" id="g67">1</td><td class="ctr1" id="h67">0</td><td class="ctr2" id="i67">1</td><td class="ctr1" id="j67">0</td><td class="ctr2" id="k67">1</td><td class="ctr1" id="l67">0</td><td class="ctr2" id="m67">1</td></tr><tr><td id="a49"><a href="OpenAiChatRequest$Companion.html" class="el_class">OpenAiChatRequest.Companion</a></td><td class="bar" id="b68"/><td class="ctr2" id="c3">100%</td><td class="bar" id="d68"/><td class="ctr2" id="e68">n/a</td><td class="ctr1" id="f68">0</td><td class="ctr2" id="g68">1</td><td class="ctr1" id="h68">0</td><td class="ctr2" id="i68">1</td><td class="ctr1" id="j68">0</td><td class="ctr2" id="k68">1</td><td class="ctr1" id="l68">0</td><td class="ctr2" id="m68">1</td></tr><tr><td id="a34"><a href="OllamaModelsResponse$Companion.html" class="el_class">OllamaModelsResponse.Companion</a></td><td class="bar" id="b69"/><td class="ctr2" id="c4">100%</td><td class="bar" id="d69"/><td class="ctr2" id="e69">n/a</td><td class="ctr1" id="f69">0</td><td class="ctr2" id="g69">1</td><td class="ctr1" id="h69">0</td><td class="ctr2" id="i69">1</td><td class="ctr1" id="j69">0</td><td class="ctr2" id="k69">1</td><td class="ctr1" id="l69">0</td><td class="ctr2" id="m69">1</td></tr><tr><td id="a40"><a href="OllamaResponse$Companion.html" class="el_class">OllamaResponse.Companion</a></td><td class="bar" id="b70"/><td class="ctr2" id="c5">100%</td><td class="bar" id="d70"/><td class="ctr2" id="e70">n/a</td><td class="ctr1" id="f70">0</td><td class="ctr2" id="g70">1</td><td class="ctr1" id="h70">0</td><td class="ctr2" id="i70">1</td><td class="ctr1" id="j70">0</td><td class="ctr2" id="k70">1</td><td class="ctr1" id="l70">0</td><td class="ctr2" id="m70">1</td></tr><tr><td id="a64"><a href="OpenAiModelsResponse$Companion.html" class="el_class">OpenAiModelsResponse.Companion</a></td><td class="bar" id="b71"/><td class="ctr2" id="c6">100%</td><td class="bar" id="d71"/><td class="ctr2" id="e71">n/a</td><td class="ctr1" id="f71">0</td><td class="ctr2" id="g71">1</td><td class="ctr1" id="h71">0</td><td class="ctr2" id="i71">1</td><td class="ctr1" id="j71">0</td><td class="ctr2" id="k71">1</td><td class="ctr1" id="l71">0</td><td class="ctr2" id="m71">1</td></tr><tr><td id="a38"><a href="OllamaRequest$Companion.html" class="el_class">OllamaRequest.Companion</a></td><td class="bar" id="b72"/><td class="ctr2" id="c7">100%</td><td class="bar" id="d72"/><td class="ctr2" id="e72">n/a</td><td class="ctr1" id="f72">0</td><td class="ctr2" id="g72">1</td><td class="ctr1" id="h72">0</td><td class="ctr2" id="i72">1</td><td class="ctr1" id="j72">0</td><td class="ctr2" id="k72">1</td><td class="ctr1" id="l72">0</td><td class="ctr2" id="m72">1</td></tr><tr><td id="a25"><a href="OllamaErrorResponse$Companion.html" class="el_class">OllamaErrorResponse.Companion</a></td><td class="bar" id="b73"/><td class="ctr2" id="c8">100%</td><td class="bar" id="d73"/><td class="ctr2" id="e73">n/a</td><td class="ctr1" id="f73">0</td><td class="ctr2" id="g73">1</td><td class="ctr1" id="h73">0</td><td class="ctr2" id="i73">1</td><td class="ctr1" id="j73">0</td><td class="ctr2" id="k73">1</td><td class="ctr1" id="l73">0</td><td class="ctr2" id="m73">1</td></tr><tr><td id="a10"><a href="ClaudeRequest$Companion.html" class="el_class">ClaudeRequest.Companion</a></td><td class="bar" id="b74"/><td class="ctr2" id="c9">100%</td><td class="bar" id="d74"/><td class="ctr2" id="e74">n/a</td><td class="ctr1" id="f74">0</td><td class="ctr2" id="g74">1</td><td class="ctr1" id="h74">0</td><td class="ctr2" id="i74">1</td><td class="ctr1" id="j74">0</td><td class="ctr2" id="k74">1</td><td class="ctr1" id="l74">0</td><td class="ctr2" id="m74">1</td></tr><tr><td id="a12"><a href="ClaudeResponse$Companion.html" class="el_class">ClaudeResponse.Companion</a></td><td class="bar" id="b75"/><td class="ctr2" id="c10">100%</td><td class="bar" id="d75"/><td class="ctr2" id="e75">n/a</td><td class="ctr1" id="f75">0</td><td class="ctr2" id="g75">1</td><td class="ctr1" id="h75">0</td><td class="ctr2" id="i75">1</td><td class="ctr1" id="j75">0</td><td class="ctr2" id="k75">1</td><td class="ctr1" id="l75">0</td><td class="ctr2" id="m75">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>