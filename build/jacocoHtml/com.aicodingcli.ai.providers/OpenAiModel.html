<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OpenAiModel</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.ai.providers</a> &gt; <span class="el_class">OpenAiModel</span></div><h1>OpenAiModel</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">114 of 142</td><td class="ctr2">19%</td><td class="bar">15 of 18</td><td class="ctr2">16%</td><td class="ctr1">16</td><td class="ctr2">17</td><td class="ctr1">6</td><td class="ctr2">7</td><td class="ctr1">7</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a7"><a href="OpenAiModels.kt.html#L107" class="el_method">write$Self$ai_coding_cli(OpenAiModel, CompositeEncoder, SerialDescriptor)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="57" alt="57"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="OpenAiModels.kt.html#L108" class="el_method">OpenAiModel(String, String, Long, String, int, DefaultConstructorMarker)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="17" alt="17"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="OpenAiModels.kt.html#L108" class="el_method">OpenAiModel(String, String, Long, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="15" alt="15"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h0">5</td><td class="ctr2" id="i0">5</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="OpenAiModels.kt.html#L107" class="el_method">OpenAiModel(int, String, String, Long, String, SerializationConstructorMarker)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="58" height="10" title="28" alt="28"/></td><td class="ctr2" id="c0">68%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="3" alt="3"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i2">3</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="OpenAiModels.kt.html#L109" class="el_method">getId()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="OpenAiModels.kt.html#L110" class="el_method">getObject()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="OpenAiModels.kt.html#L111" class="el_method">getCreated()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="OpenAiModels.kt.html#L112" class="el_method">getOwned_by()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>