<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OllamaModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.ai.providers</a> &gt; <span class="el_source">OllamaModels.kt</span></div><h1>OllamaModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.ai.providers

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Ollama API request models
 */
<span class="pc bpc" id="L9" title="17 of 26 branches missed.">@Serializable</span>
<span class="fc" id="L10">data class OllamaRequest(</span>
<span class="pc" id="L11">    val model: String,</span>
<span class="pc" id="L12">    val messages: List&lt;OllamaMessage&gt;,</span>
<span class="pc" id="L13">    val stream: Boolean,</span>
<span class="pc" id="L14">    val format: String? = null,</span>
<span class="pc" id="L15">    val options: OllamaOptions? = null,</span>
<span class="pc" id="L16">    @SerialName(&quot;keep_alive&quot;)</span>
<span class="pc" id="L17">    val keepAlive: String? = null</span>
<span class="fc" id="L18">)</span>

<span class="pc bpc" id="L20" title="7 of 10 branches missed.">@Serializable</span>
<span class="fc" id="L21">data class OllamaMessage(</span>
<span class="pc" id="L22">    val role: String,</span>
<span class="pc" id="L23">    val content: String,</span>
<span class="pc" id="L24">    val images: List&lt;String&gt;? = null</span>
<span class="fc" id="L25">)</span>

<span class="pc bpc" id="L27" title="32 of 50 branches missed.">@Serializable</span>
<span class="fc" id="L28">data class OllamaOptions(</span>
<span class="pc" id="L29">    val temperature: Float? = null,</span>
<span class="pc" id="L30">    @SerialName(&quot;num_predict&quot;)</span>
<span class="nc" id="L31">    val numPredict: Int? = null,</span>
<span class="pc" id="L32">    @SerialName(&quot;top_k&quot;)</span>
<span class="pc" id="L33">    val topK: Int? = null,</span>
<span class="pc" id="L34">    @SerialName(&quot;top_p&quot;)</span>
<span class="pc" id="L35">    val topP: Float? = null,</span>
<span class="pc" id="L36">    val seed: Int? = null,</span>
<span class="pc" id="L37">    val stop: List&lt;String&gt;? = null</span>
<span class="fc" id="L38">)</span>

/**
 * Ollama API response models
 */
<span class="pc bpc" id="L43" title="43 of 58 branches missed.">@Serializable</span>
<span class="nc" id="L44">data class OllamaResponse(</span>
<span class="pc" id="L45">    val model: String,</span>
<span class="nc" id="L46">    @SerialName(&quot;created_at&quot;)</span>
    val createdAt: String,
<span class="pc" id="L48">    val message: OllamaResponseMessage,</span>
<span class="pc" id="L49">    val done: Boolean,</span>
<span class="pc" id="L50">    @SerialName(&quot;done_reason&quot;)</span>
<span class="pc" id="L51">    val doneReason: String? = null,</span>
<span class="nc" id="L52">    @SerialName(&quot;total_duration&quot;)</span>
<span class="pc" id="L53">    val totalDuration: Long? = null,</span>
<span class="nc" id="L54">    @SerialName(&quot;load_duration&quot;)</span>
<span class="pc" id="L55">    val loadDuration: Long? = null,</span>
<span class="pc" id="L56">    @SerialName(&quot;prompt_eval_count&quot;)</span>
<span class="pc" id="L57">    val promptEvalCount: Int? = null,</span>
<span class="nc" id="L58">    @SerialName(&quot;prompt_eval_duration&quot;)</span>
<span class="pc" id="L59">    val promptEvalDuration: Long? = null,</span>
<span class="pc" id="L60">    @SerialName(&quot;eval_count&quot;)</span>
<span class="pc" id="L61">    val evalCount: Int? = null,</span>
<span class="nc" id="L62">    @SerialName(&quot;eval_duration&quot;)</span>
<span class="pc" id="L63">    val evalDuration: Long? = null</span>
<span class="nc" id="L64">)</span>

<span class="pc bpc" id="L66" title="8 of 10 branches missed.">@Serializable</span>
<span class="nc" id="L67">data class OllamaResponseMessage(</span>
<span class="nc" id="L68">    val role: String,</span>
<span class="pc" id="L69">    val content: String,</span>
<span class="pc" id="L70">    val images: List&lt;String&gt;? = null</span>
<span class="nc" id="L71">)</span>

/**
 * Ollama streaming response models
 */
<span class="nc bnc" id="L76" title="All 66 branches missed.">@Serializable</span>
<span class="nc" id="L77">data class OllamaStreamResponse(</span>
<span class="nc" id="L78">    val model: String,</span>
<span class="nc" id="L79">    @SerialName(&quot;created_at&quot;)</span>
    val createdAt: String,
<span class="nc" id="L81">    val message: OllamaResponseMessage? = null,</span>
<span class="nc" id="L82">    val done: Boolean,</span>
<span class="nc" id="L83">    @SerialName(&quot;done_reason&quot;)</span>
<span class="nc" id="L84">    val doneReason: String? = null,</span>
<span class="nc" id="L85">    @SerialName(&quot;total_duration&quot;)</span>
<span class="nc" id="L86">    val totalDuration: Long? = null,</span>
<span class="nc" id="L87">    @SerialName(&quot;load_duration&quot;)</span>
<span class="nc" id="L88">    val loadDuration: Long? = null,</span>
<span class="nc" id="L89">    @SerialName(&quot;prompt_eval_count&quot;)</span>
<span class="nc" id="L90">    val promptEvalCount: Int? = null,</span>
<span class="nc" id="L91">    @SerialName(&quot;prompt_eval_duration&quot;)</span>
<span class="nc" id="L92">    val promptEvalDuration: Long? = null,</span>
<span class="nc" id="L93">    @SerialName(&quot;eval_count&quot;)</span>
<span class="nc" id="L94">    val evalCount: Int? = null,</span>
<span class="nc" id="L95">    @SerialName(&quot;eval_duration&quot;)</span>
<span class="nc" id="L96">    val evalDuration: Long? = null</span>
<span class="nc" id="L97">)</span>

/**
 * Ollama error response models
 */
<span class="pc bpc" id="L102" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L103">data class OllamaErrorResponse(</span>
<span class="pc" id="L104">    val error: String</span>
)

/**
 * Ollama models list response
 */
<span class="pc bpc" id="L110" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L111">data class OllamaModelsResponse(</span>
<span class="nc" id="L112">    val models: List&lt;OllamaModel&gt;</span>
)

<span class="pc bpc" id="L115" title="29 of 34 branches missed.">@Serializable</span>
<span class="nc" id="L116">data class OllamaModel(</span>
<span class="nc" id="L117">    val name: String,</span>
<span class="nc" id="L118">    val model: String? = null,</span>
<span class="nc" id="L119">    val size: Long,</span>
<span class="nc" id="L120">    val digest: String,</span>
<span class="nc" id="L121">    val details: OllamaModelDetails? = null,</span>
<span class="nc" id="L122">    @SerialName(&quot;expires_at&quot;)</span>
<span class="pc" id="L123">    val expiresAt: String? = null,</span>
<span class="nc" id="L124">    @SerialName(&quot;size_vram&quot;)</span>
<span class="pc" id="L125">    val sizeVram: Long? = null</span>
<span class="nc" id="L126">)</span>

<span class="pc bpc" id="L128" title="13 of 18 branches missed.">@Serializable</span>
<span class="nc" id="L129">data class OllamaModelDetails(</span>
<span class="nc" id="L130">    @SerialName(&quot;parent_model&quot;)</span>
<span class="pc" id="L131">    val parentModel: String? = null,</span>
<span class="nc" id="L132">    val format: String,</span>
<span class="nc" id="L133">    val family: String,</span>
<span class="pc" id="L134">    val families: List&lt;String&gt;? = null,</span>
<span class="nc" id="L135">    @SerialName(&quot;parameter_size&quot;)</span>
    val parameterSize: String,
<span class="nc" id="L137">    @SerialName(&quot;quantization_level&quot;)</span>
    val quantizationLevel: String
<span class="nc" id="L139">)</span>

/**
 * Ollama specific exception
 */
<span class="pc" id="L144">class OllamaException(</span>
    message: String,
<span class="nc" id="L146">    cause: Throwable? = null</span>
<span class="pc" id="L147">) : Exception(message, cause)</span>
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>