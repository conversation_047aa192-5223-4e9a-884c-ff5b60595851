<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OpenAiChatResponse</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.ai.providers</a> &gt; <span class="el_class">OpenAiChatResponse</span></div><h1>OpenAiChatResponse</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">80 of 149</td><td class="ctr2">46%</td><td class="bar">1 of 2</td><td class="ctr2">50%</td><td class="ctr1">6</td><td class="ctr2">11</td><td class="ctr1">4</td><td class="ctr2">8</td><td class="ctr1">5</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a9"><a href="OpenAiModels.kt.html#L28" class="el_method">write$Self$ai_coding_cli(OpenAiChatResponse, CompositeEncoder, SerialDescriptor)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="45" alt="45"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g1">1</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a7"><a href="OpenAiModels.kt.html#L29" class="el_method">OpenAiChatResponse(String, String, long, String, List, OpenAiUsage)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="21" alt="21"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h0">7</td><td class="ctr2" id="i0">7</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="OpenAiModels.kt.html#L28" class="el_method">OpenAiChatResponse(int, String, String, long, String, List, OpenAiUsage, SerializationConstructorMarker)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="69" height="10" title="26" alt="26"/></td><td class="ctr2" id="c4">83%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="OpenAiModels.kt.html#L30" class="el_method">getId()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="OpenAiModels.kt.html#L31" class="el_method">getObject()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="OpenAiModels.kt.html#L32" class="el_method">getCreated()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a8"><a href="OpenAiModels.kt.html#L28" class="el_method">static {...}</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="34" alt="34"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="OpenAiModels.kt.html#L33" class="el_method">getModel()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a0"><a href="OpenAiModels.kt.html#L34" class="el_method">getChoices()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="OpenAiModels.kt.html#L35" class="el_method">getUsage()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>