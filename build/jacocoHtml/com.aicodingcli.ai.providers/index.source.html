<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.ai.providers</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.ai.providers</span></div><h1>com.aicodingcli.ai.providers</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4,049 of 6,577</td><td class="ctr2">38%</td><td class="bar">488 of 621</td><td class="ctr2">21%</td><td class="ctr1">513</td><td class="ctr2">660</td><td class="ctr1">231</td><td class="ctr2">585</td><td class="ctr1">215</td><td class="ctr2">343</td><td class="ctr1">32</td><td class="ctr2">76</td></tr></tfoot><tbody><tr><td id="a2"><a href="OllamaModels.kt.html" class="el_source">OllamaModels.kt</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="81" height="10" title="1,511" alt="1,511"/><img src="../jacoco-resources/greenbar.gif" width="38" height="10" title="723" alt="723"/></td><td class="ctr2" id="c3">32%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="94" height="10" title="217" alt="217"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="59" alt="59"/></td><td class="ctr2" id="e1">21%</td><td class="ctr1" id="f0">206</td><td class="ctr2" id="g0">249</td><td class="ctr1" id="h0">52</td><td class="ctr2" id="i2">107</td><td class="ctr1" id="j0">77</td><td class="ctr2" id="k0">111</td><td class="ctr1" id="l2">7</td><td class="ctr2" id="m1">21</td></tr><tr><td id="a0"><a href="ClaudeModels.kt.html" class="el_source">ClaudeModels.kt</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="1,054" alt="1,054"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="371" alt="371"/></td><td class="ctr2" id="c5">26%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="105" alt="105"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="19" alt="19"/></td><td class="ctr2" id="e5">15%</td><td class="ctr1" id="f1">126</td><td class="ctr2" id="g1">154</td><td class="ctr1" id="h1">42</td><td class="ctr2" id="i5">75</td><td class="ctr1" id="j2">66</td><td class="ctr2" id="k2">92</td><td class="ctr1" id="l1">10</td><td class="ctr2" id="m2">21</td></tr><tr><td id="a4"><a href="OpenAiModels.kt.html" class="el_source">OpenAiModels.kt</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="937" alt="937"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="409" alt="409"/></td><td class="ctr2" id="c4">30%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="83" alt="83"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="21" alt="21"/></td><td class="ctr2" id="e3">20%</td><td class="ctr1" id="f2">120</td><td class="ctr2" id="g2">153</td><td class="ctr1" id="h2">41</td><td class="ctr2" id="i4">76</td><td class="ctr1" id="j1">68</td><td class="ctr2" id="k1">101</td><td class="ctr1" id="l0">11</td><td class="ctr2" id="m0">25</td></tr><tr><td id="a1"><a href="ClaudeService.kt.html" class="el_source">ClaudeService.kt</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="250" alt="250"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="328" alt="328"/></td><td class="ctr2" id="c1">56%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="39" alt="39"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">20%</td><td class="ctr1" id="f3">27</td><td class="ctr2" id="g3">40</td><td class="ctr1" id="h4">40</td><td class="ctr2" id="i0">113</td><td class="ctr1" id="j3">2</td><td class="ctr2" id="k3">13</td><td class="ctr1" id="l3">2</td><td class="ctr2" id="m3">3</td></tr><tr><td id="a5"><a href="OpenAiService.kt.html" class="el_source">OpenAiService.kt</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="231" alt="231"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="281" alt="281"/></td><td class="ctr2" id="c2">54%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="30" alt="30"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="e4">18%</td><td class="ctr1" id="f4">22</td><td class="ctr2" id="g4">35</td><td class="ctr1" id="h3">41</td><td class="ctr2" id="i1">108</td><td class="ctr1" id="j4">2</td><td class="ctr2" id="k4">13</td><td class="ctr1" id="l4">2</td><td class="ctr2" id="m4">3</td></tr><tr><td id="a3"><a href="OllamaService.kt.html" class="el_source">OllamaService.kt</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="66" alt="66"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="416" alt="416"/></td><td class="ctr2" id="c0">86%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="17" alt="17"/></td><td class="ctr2" id="e0">54%</td><td class="ctr1" id="f5">12</td><td class="ctr2" id="g5">29</td><td class="ctr1" id="h5">15</td><td class="ctr2" id="i3">106</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">13</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">3</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>