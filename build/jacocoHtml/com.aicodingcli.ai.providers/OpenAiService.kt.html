<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OpenAiService.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.ai.providers</a> &gt; <span class="el_source">OpenAiService.kt</span></div><h1>OpenAiService.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.ai.providers

import com.aicodingcli.ai.*
import com.aicodingcli.http.AiHttpClient
import com.aicodingcli.http.HttpException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString

/**
 * OpenAI service implementation
 */
<span class="fc" id="L15">class OpenAiService(</span>
<span class="fc" id="L16">    override val config: AiServiceConfig,</span>
<span class="fc" id="L17">    private val httpClient: AiHttpClient = AiHttpClient()</span>
<span class="fc" id="L18">) : BaseAiService(config) {</span>

<span class="fc" id="L20">    private val json = Json {</span>
<span class="fc" id="L21">        ignoreUnknownKeys = true</span>
<span class="fc" id="L22">        isLenient = true</span>
<span class="fc" id="L23">    }</span>

<span class="fc bfc" id="L25" title="All 2 branches covered.">    private val baseUrl = config.baseUrl ?: &quot;https://api.openai.com/v1&quot;</span>

<span class="fc" id="L27">    override suspend fun chat(request: AiRequest): AiResponse {</span>
<span class="fc" id="L28">        validateRequest(request)</span>
        
<span class="fc" id="L30">        try {</span>
<span class="fc" id="L31">            val openAiRequest = convertToOpenAiRequest(request)</span>
<span class="fc" id="L32">            val requestBody = json.encodeToString(openAiRequest)</span>
            
<span class="fc" id="L34">            val response = httpClient.post(</span>
<span class="fc" id="L35">                url = &quot;$baseUrl/chat/completions&quot;,</span>
<span class="fc" id="L36">                body = requestBody,</span>
<span class="fc" id="L37">                headers = createHeaders()</span>
            )
            
<span class="fc" id="L40">            val openAiResponse = json.decodeFromString&lt;OpenAiChatResponse&gt;(response.body)</span>
<span class="fc" id="L41">            return convertToAiResponse(openAiResponse)</span>
            
<span class="fc" id="L43">        } catch (e: HttpException) {</span>
<span class="fc" id="L44">            throw handleHttpException(e)</span>
<span class="nc" id="L45">        } catch (e: Exception) {</span>
<span class="nc" id="L46">            throw OpenAiException(&quot;Failed to process chat request: ${e.message}&quot;, cause = e)</span>
        }
    }

    override suspend fun streamChat(request: AiRequest): Flow&lt;AiStreamChunk&gt; {
<span class="fc" id="L51">        validateRequest(request)</span>

<span class="pc" id="L53">        return flow {</span>
<span class="nc" id="L54">            try {</span>
<span class="nc" id="L55">                val openAiRequest = convertToOpenAiRequest(request.copy(stream = true))</span>
<span class="nc" id="L56">                val requestBody = json.encodeToString(openAiRequest)</span>

<span class="nc" id="L58">                httpClient.postStream(</span>
<span class="nc" id="L59">                    url = &quot;$baseUrl/chat/completions&quot;,</span>
<span class="nc" id="L60">                    body = requestBody,</span>
<span class="nc" id="L61">                    headers = createHeaders()</span>
<span class="nc" id="L62">                ).collect { data -&gt;</span>
<span class="nc bnc" id="L63" title="All 4 branches missed.">                    if (data.isNotBlank()) {</span>
<span class="nc" id="L64">                        try {</span>
<span class="nc" id="L65">                            val streamResponse = json.decodeFromString&lt;OpenAiStreamResponse&gt;(data)</span>
<span class="nc" id="L66">                            val choice = streamResponse.choices.firstOrNull()</span>

<span class="nc bnc" id="L68" title="All 2 branches missed.">                            if (choice != null) {</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">                                val content = choice.delta.content ?: &quot;&quot;</span>
<span class="nc bnc" id="L70" title="All 15 branches missed.">                                val finishReason = when (choice.finishReason) {</span>
<span class="nc" id="L71">                                    &quot;stop&quot; -&gt; FinishReason.STOP</span>
<span class="nc" id="L72">                                    &quot;length&quot; -&gt; FinishReason.LENGTH</span>
<span class="nc" id="L73">                                    &quot;content_filter&quot; -&gt; FinishReason.CONTENT_FILTER</span>
<span class="nc" id="L74">                                    &quot;function_call&quot; -&gt; FinishReason.FUNCTION_CALL</span>
<span class="nc" id="L75">                                    null -&gt; null</span>
<span class="nc" id="L76">                                    else -&gt; FinishReason.STOP</span>
                                }

<span class="nc" id="L79">                                emit(AiStreamChunk(content, finishReason))</span>
                            }
<span class="nc" id="L81">                        } catch (e: Exception) {</span>
                            // Skip malformed JSON chunks
                        }
                    }
<span class="nc" id="L85">                }</span>

<span class="nc" id="L87">            } catch (e: HttpException) {</span>
<span class="nc" id="L88">                throw handleHttpException(e)</span>
<span class="nc" id="L89">            } catch (e: Exception) {</span>
<span class="nc" id="L90">                throw OpenAiException(&quot;Failed to process streaming chat request: ${e.message}&quot;, cause = e)</span>
            }
<span class="nc" id="L92">        }</span>
    }

<span class="fc" id="L95">    override suspend fun testConnection(): Boolean {</span>
<span class="fc" id="L96">        return try {</span>
<span class="fc" id="L97">            val response = httpClient.get(</span>
<span class="fc" id="L98">                url = &quot;$baseUrl/models&quot;,</span>
<span class="fc" id="L99">                headers = createHeaders()</span>
            )
            
            // Try to parse the response to ensure it's valid
<span class="fc" id="L103">            json.decodeFromString&lt;OpenAiModelsResponse&gt;(response.body)</span>
<span class="fc" id="L104">            true</span>
            
<span class="fc" id="L106">        } catch (e: Exception) {</span>
<span class="fc bfc" id="L107" title="All 2 branches covered.">            false</span>
        }
    }

    /**
     * Convert AI request to OpenAI format
     */
    private fun convertToOpenAiRequest(request: AiRequest): OpenAiChatRequest {
<span class="fc" id="L115">        val openAiMessages = request.messages.map { message -&gt;</span>
<span class="fc" id="L116">            OpenAiMessage(</span>
<span class="pc bpc" id="L117" title="2 of 3 branches missed.">                role = when (message.role) {</span>
<span class="nc" id="L118">                    MessageRole.SYSTEM -&gt; &quot;system&quot;</span>
<span class="fc" id="L119">                    MessageRole.USER -&gt; &quot;user&quot;</span>
<span class="nc" id="L120">                    MessageRole.ASSISTANT -&gt; &quot;assistant&quot;</span>
                },
<span class="fc" id="L122">                content = message.content</span>
<span class="fc" id="L123">            )</span>
        }
        
<span class="fc" id="L126">        return OpenAiChatRequest(</span>
<span class="fc" id="L127">            model = request.model,</span>
<span class="fc" id="L128">            messages = openAiMessages,</span>
<span class="fc" id="L129">            temperature = request.temperature,</span>
<span class="fc" id="L130">            maxTokens = request.maxTokens,</span>
<span class="fc" id="L131">            stream = request.stream</span>
        )
    }

    /**
     * Convert OpenAI response to AI format
     */
    private fun convertToAiResponse(openAiResponse: OpenAiChatResponse): AiResponse {
<span class="pc bpc" id="L139" title="1 of 2 branches missed.">        val choice = openAiResponse.choices.firstOrNull()</span>
<span class="nc" id="L140">            ?: throw OpenAiException(&quot;No choices in OpenAI response&quot;)</span>
        
<span class="pc bpc" id="L142" title="4 of 5 branches missed.">        val finishReason = when (choice.finishReason) {</span>
<span class="fc" id="L143">            &quot;stop&quot; -&gt; FinishReason.STOP</span>
<span class="nc" id="L144">            &quot;length&quot; -&gt; FinishReason.LENGTH</span>
<span class="nc" id="L145">            &quot;content_filter&quot; -&gt; FinishReason.CONTENT_FILTER</span>
<span class="nc" id="L146">            &quot;function_call&quot; -&gt; FinishReason.FUNCTION_CALL</span>
<span class="nc" id="L147">            else -&gt; FinishReason.STOP</span>
        }
        
<span class="fc" id="L150">        return AiResponse(</span>
<span class="fc" id="L151">            content = choice.message.content,</span>
<span class="fc" id="L152">            model = openAiResponse.model,</span>
<span class="fc" id="L153">            usage = TokenUsage(</span>
<span class="fc" id="L154">                promptTokens = openAiResponse.usage.promptTokens,</span>
<span class="fc" id="L155">                completionTokens = openAiResponse.usage.completionTokens,</span>
<span class="fc" id="L156">                totalTokens = openAiResponse.usage.totalTokens</span>
            ),
<span class="fc" id="L158">            finishReason = finishReason</span>
        )
    }

    /**
     * Create headers for OpenAI API requests
     */
    private fun createHeaders(): Map&lt;String, String&gt; {
<span class="fc" id="L166">        return mapOf(</span>
<span class="fc" id="L167">            &quot;Authorization&quot; to &quot;Bearer ${config.apiKey}&quot;,</span>
<span class="fc" id="L168">            &quot;Content-Type&quot; to &quot;application/json&quot;</span>
        )
    }

    /**
     * Handle HTTP exceptions and convert to OpenAI exceptions
     */
    private fun handleHttpException(e: HttpException): OpenAiException {
<span class="fc" id="L176">        return try {</span>
<span class="fc" id="L177">            val errorResponse = json.decodeFromString&lt;OpenAiErrorResponse&gt;(e.responseBody)</span>
<span class="fc" id="L178">            OpenAiException(</span>
<span class="fc" id="L179">                message = errorResponse.error.message,</span>
<span class="fc" id="L180">                errorType = errorResponse.error.type,</span>
<span class="fc" id="L181">                errorCode = errorResponse.error.code,</span>
<span class="fc" id="L182">                cause = e</span>
            )
<span class="nc" id="L184">        } catch (parseException: Exception) {</span>
<span class="pc" id="L185">            OpenAiException(</span>
<span class="nc" id="L186">                message = &quot;OpenAI API error: ${e.statusCode.value} - ${e.responseBody}&quot;,</span>
<span class="nc" id="L187">                cause = e</span>
            )
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>