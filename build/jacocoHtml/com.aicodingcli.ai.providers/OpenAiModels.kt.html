<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OpenAiModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.ai.providers</a> &gt; <span class="el_source">OpenAiModels.kt</span></div><h1>OpenAiModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.ai.providers

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * OpenAI API request models
 */
<span class="pc bpc" id="L9" title="17 of 26 branches missed.">@Serializable</span>
<span class="pc" id="L10">data class OpenAiChatRequest(</span>
<span class="pc" id="L11">    val model: String,</span>
<span class="pc" id="L12">    val messages: List&lt;OpenAiMessage&gt;,</span>
<span class="pc" id="L13">    val temperature: Float? = null,</span>
<span class="pc" id="L14">    @SerialName(&quot;max_tokens&quot;)</span>
<span class="nc" id="L15">    val maxTokens: Int? = null,</span>
<span class="pc" id="L16">    val stream: Boolean = false</span>
<span class="nc" id="L17">)</span>

<span class="pc bpc" id="L19" title="1 of 2 branches missed.">@Serializable</span>
<span class="fc" id="L20">data class OpenAiMessage(</span>
<span class="pc" id="L21">    val role: String,</span>
<span class="fc" id="L22">    val content: String</span>
)

/**
 * OpenAI API response models
 */
<span class="pc bpc" id="L28" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L29">data class OpenAiChatResponse(</span>
<span class="nc" id="L30">    val id: String,</span>
<span class="nc" id="L31">    val `object`: String,</span>
<span class="nc" id="L32">    val created: Long,</span>
<span class="pc" id="L33">    val model: String,</span>
<span class="pc" id="L34">    val choices: List&lt;OpenAiChoice&gt;,</span>
<span class="pc" id="L35">    val usage: OpenAiUsage</span>
)

<span class="pc bpc" id="L38" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L39">data class OpenAiChoice(</span>
<span class="nc" id="L40">    val index: Int,</span>
<span class="pc" id="L41">    val message: OpenAiMessage,</span>
<span class="pc" id="L42">    @SerialName(&quot;finish_reason&quot;)</span>
    val finishReason: String?
)

<span class="pc bpc" id="L46" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L47">data class OpenAiUsage(</span>
<span class="pc" id="L48">    @SerialName(&quot;prompt_tokens&quot;)</span>
    val promptTokens: Int,
<span class="pc" id="L50">    @SerialName(&quot;completion_tokens&quot;)</span>
    val completionTokens: Int,
<span class="pc" id="L52">    @SerialName(&quot;total_tokens&quot;)</span>
    val totalTokens: Int
)

/**
 * OpenAI streaming response models
 */
<span class="nc bnc" id="L59" title="All 2 branches missed.">@Serializable</span>
<span class="nc" id="L60">data class OpenAiStreamResponse(</span>
<span class="nc" id="L61">    val id: String,</span>
<span class="nc" id="L62">    val `object`: String,</span>
<span class="nc" id="L63">    val created: Long,</span>
<span class="nc" id="L64">    val model: String,</span>
<span class="nc" id="L65">    val choices: List&lt;OpenAiStreamChoice&gt;</span>
)

<span class="nc bnc" id="L68" title="All 10 branches missed.">@Serializable</span>
<span class="nc" id="L69">data class OpenAiStreamChoice(</span>
<span class="nc" id="L70">    val index: Int,</span>
<span class="nc" id="L71">    val delta: OpenAiStreamDelta,</span>
<span class="nc" id="L72">    @SerialName(&quot;finish_reason&quot;)</span>
<span class="nc" id="L73">    val finishReason: String? = null</span>
<span class="nc" id="L74">)</span>

<span class="nc bnc" id="L76" title="All 18 branches missed.">@Serializable</span>
<span class="nc" id="L77">data class OpenAiStreamDelta(</span>
<span class="nc" id="L78">    val role: String? = null,</span>
<span class="nc" id="L79">    val content: String? = null</span>
<span class="nc" id="L80">)</span>

/**
 * OpenAI error response models
 */
<span class="pc bpc" id="L85" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L86">data class OpenAiErrorResponse(</span>
<span class="pc" id="L87">    val error: OpenAiError</span>
)

<span class="pc bpc" id="L90" title="15 of 18 branches missed.">@Serializable</span>
<span class="nc" id="L91">data class OpenAiError(</span>
<span class="pc" id="L92">    val message: String,</span>
<span class="pc" id="L93">    val type: String,</span>
<span class="pc" id="L94">    val param: String? = null,</span>
<span class="pc" id="L95">    val code: String? = null</span>
<span class="nc" id="L96">)</span>

/**
 * OpenAI models list response
 */
<span class="pc bpc" id="L101" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L102">data class OpenAiModelsResponse(</span>
<span class="nc" id="L103">    val `object`: String,</span>
<span class="nc" id="L104">    val data: List&lt;OpenAiModel&gt;</span>
)

<span class="pc bpc" id="L107" title="15 of 18 branches missed.">@Serializable</span>
<span class="nc" id="L108">data class OpenAiModel(</span>
<span class="nc" id="L109">    val id: String,</span>
<span class="nc" id="L110">    val `object`: String,</span>
<span class="nc" id="L111">    val created: Long? = null,</span>
<span class="nc" id="L112">    val owned_by: String? = null</span>
<span class="nc" id="L113">)</span>

/**
 * OpenAI specific exception
 */
<span class="pc" id="L118">class OpenAiException(</span>
    message: String,
<span class="pc" id="L120">    val errorType: String? = null,</span>
<span class="pc" id="L121">    val errorCode: String? = null,</span>
<span class="nc" id="L122">    cause: Throwable? = null</span>
<span class="pc" id="L123">) : Exception(message, cause)</span>
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>