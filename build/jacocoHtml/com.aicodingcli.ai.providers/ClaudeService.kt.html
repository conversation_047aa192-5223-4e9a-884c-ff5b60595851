<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ClaudeService.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.ai.providers</a> &gt; <span class="el_source">ClaudeService.kt</span></div><h1>ClaudeService.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.ai.providers

import com.aicodingcli.ai.*
import com.aicodingcli.http.AiHttpClient
import com.aicodingcli.http.HttpException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString

/**
 * Claude service implementation
 */
<span class="fc" id="L15">class ClaudeService(</span>
<span class="fc" id="L16">    override val config: AiServiceConfig,</span>
<span class="fc" id="L17">    private val httpClient: AiHttpClient = AiHttpClient()</span>
<span class="fc" id="L18">) : BaseAiService(config) {</span>

<span class="fc" id="L20">    private val json = Json {</span>
<span class="fc" id="L21">        ignoreUnknownKeys = true</span>
<span class="fc" id="L22">        isLenient = true</span>
<span class="fc" id="L23">    }</span>

<span class="fc bfc" id="L25" title="All 2 branches covered.">    private val baseUrl = config.baseUrl ?: &quot;https://api.anthropic.com/v1&quot;</span>

<span class="fc" id="L27">    override suspend fun chat(request: AiRequest): AiResponse {</span>
<span class="fc" id="L28">        validateRequest(request)</span>
        
<span class="fc" id="L30">        try {</span>
<span class="fc" id="L31">            val claudeRequest = convertToClaudeRequest(request)</span>
<span class="fc" id="L32">            val requestBody = json.encodeToString(claudeRequest)</span>
            
<span class="fc" id="L34">            val response = httpClient.post(</span>
<span class="fc" id="L35">                url = &quot;$baseUrl/messages&quot;,</span>
<span class="fc" id="L36">                body = requestBody,</span>
<span class="fc" id="L37">                headers = createHeaders()</span>
            )
            
<span class="fc" id="L40">            val claudeResponse = json.decodeFromString&lt;ClaudeResponse&gt;(response.body)</span>
<span class="fc" id="L41">            return convertToAiResponse(claudeResponse)</span>
            
<span class="fc" id="L43">        } catch (e: HttpException) {</span>
<span class="fc" id="L44">            throw handleHttpException(e)</span>
<span class="nc" id="L45">        } catch (e: Exception) {</span>
<span class="nc" id="L46">            throw ClaudeException(&quot;Failed to process chat request: ${e.message}&quot;, cause = e)</span>
        }
    }

    override suspend fun streamChat(request: AiRequest): Flow&lt;AiStreamChunk&gt; {
<span class="fc" id="L51">        validateRequest(request)</span>

<span class="pc" id="L53">        return flow {</span>
<span class="nc" id="L54">            try {</span>
<span class="nc" id="L55">                val claudeRequest = convertToClaudeRequest(request.copy(stream = true))</span>
<span class="nc" id="L56">                val requestBody = json.encodeToString(claudeRequest)</span>

<span class="nc" id="L58">                httpClient.postStream(</span>
<span class="nc" id="L59">                    url = &quot;$baseUrl/messages&quot;,</span>
<span class="nc" id="L60">                    body = requestBody,</span>
<span class="nc" id="L61">                    headers = createHeaders()</span>
<span class="nc" id="L62">                ).collect { data -&gt;</span>
<span class="nc bnc" id="L63" title="All 4 branches missed.">                    if (data.isNotBlank()) {</span>
<span class="nc" id="L64">                        try {</span>
<span class="nc" id="L65">                            val streamEvent = json.decodeFromString&lt;ClaudeStreamEvent&gt;(data)</span>

<span class="nc" id="L67">                            when (streamEvent.type) {</span>
<span class="nc bnc" id="L68" title="All 2 branches missed.">                                &quot;content_block_delta&quot; -&gt; {</span>
<span class="nc bnc" id="L69" title="All 4 branches missed.">                                    val content = streamEvent.delta?.text ?: &quot;&quot;</span>
<span class="nc" id="L70">                                    emit(AiStreamChunk(content, null))</span>
                                }
<span class="nc bnc" id="L72" title="All 2 branches missed.">                                &quot;message_stop&quot; -&gt; {</span>
<span class="nc bnc" id="L73" title="All 14 branches missed.">                                    val finishReason = when (streamEvent.message?.stopReason) {</span>
<span class="nc" id="L74">                                        &quot;end_turn&quot; -&gt; FinishReason.STOP</span>
<span class="nc" id="L75">                                        &quot;max_tokens&quot; -&gt; FinishReason.LENGTH</span>
<span class="nc" id="L76">                                        &quot;stop_sequence&quot; -&gt; FinishReason.STOP</span>
<span class="nc" id="L77">                                        else -&gt; FinishReason.STOP</span>
                                    }
<span class="nc" id="L79">                                    emit(AiStreamChunk(&quot;&quot;, finishReason))</span>
                                }
                            }
<span class="nc" id="L82">                        } catch (e: Exception) {</span>
                            // Skip malformed JSON chunks
                        }
                    }
<span class="nc" id="L86">                }</span>

<span class="nc" id="L88">            } catch (e: HttpException) {</span>
<span class="nc" id="L89">                throw handleHttpException(e)</span>
<span class="nc" id="L90">            } catch (e: Exception) {</span>
<span class="nc" id="L91">                throw ClaudeException(&quot;Failed to process streaming chat request: ${e.message}&quot;, cause = e)</span>
            }
<span class="nc" id="L93">        }</span>
    }

<span class="fc" id="L96">    override suspend fun testConnection(): Boolean {</span>
<span class="fc" id="L97">        return try {</span>
<span class="fc" id="L98">            val testRequest = ClaudeRequest(</span>
<span class="fc" id="L99">                model = config.model,</span>
<span class="fc" id="L100">                messages = listOf(ClaudeMessage(&quot;user&quot;, &quot;Test connection&quot;)),</span>
<span class="fc" id="L101">                maxTokens = 10</span>
            )
<span class="fc" id="L103">            val requestBody = json.encodeToString(testRequest)</span>
            
<span class="fc" id="L105">            val response = httpClient.post(</span>
<span class="fc" id="L106">                url = &quot;$baseUrl/messages&quot;,</span>
<span class="fc" id="L107">                body = requestBody,</span>
<span class="fc" id="L108">                headers = createHeaders()</span>
            )
            
            // Try to parse the response to ensure it's valid
<span class="fc" id="L112">            json.decodeFromString&lt;ClaudeResponse&gt;(response.body)</span>
<span class="fc" id="L113">            true</span>
            
<span class="fc" id="L115">        } catch (e: Exception) {</span>
<span class="fc bfc" id="L116" title="All 2 branches covered.">            false</span>
        }
    }

    /**
     * Convert AI request to Claude format
     */
    private fun convertToClaudeRequest(request: AiRequest): ClaudeRequest {
<span class="fc" id="L124">        val claudeMessages = request.messages.map { message -&gt;</span>
<span class="fc" id="L125">            ClaudeMessage(</span>
<span class="pc bpc" id="L126" title="2 of 3 branches missed.">                role = when (message.role) {</span>
<span class="nc" id="L127">                    MessageRole.SYSTEM -&gt; &quot;user&quot; // Claude handles system messages differently</span>
<span class="fc" id="L128">                    MessageRole.USER -&gt; &quot;user&quot;</span>
<span class="nc" id="L129">                    MessageRole.ASSISTANT -&gt; &quot;assistant&quot;</span>
                },
<span class="fc" id="L131">                content = message.content</span>
<span class="fc" id="L132">            )</span>
        }
        
<span class="fc" id="L135">        return ClaudeRequest(</span>
<span class="fc" id="L136">            model = request.model,</span>
<span class="fc" id="L137">            messages = claudeMessages,</span>
<span class="fc" id="L138">            maxTokens = request.maxTokens,</span>
<span class="fc" id="L139">            temperature = request.temperature,</span>
<span class="fc" id="L140">            stream = request.stream</span>
        )
    }

    /**
     * Convert Claude response to AI format
     */
    private fun convertToAiResponse(claudeResponse: ClaudeResponse): AiResponse {
<span class="pc bpc" id="L148" title="2 of 4 branches missed.">        val content = claudeResponse.content.firstOrNull()?.text</span>
<span class="nc" id="L149">            ?: throw ClaudeException(&quot;No content in Claude response&quot;)</span>
        
<span class="pc bpc" id="L151" title="9 of 12 branches missed.">        val finishReason = when (claudeResponse.stopReason) {</span>
<span class="fc" id="L152">            &quot;end_turn&quot; -&gt; FinishReason.STOP</span>
<span class="nc" id="L153">            &quot;max_tokens&quot; -&gt; FinishReason.LENGTH</span>
<span class="nc" id="L154">            &quot;stop_sequence&quot; -&gt; FinishReason.STOP</span>
<span class="nc" id="L155">            else -&gt; FinishReason.STOP</span>
        }
        
<span class="fc" id="L158">        return AiResponse(</span>
<span class="fc" id="L159">            content = content,</span>
<span class="fc" id="L160">            model = claudeResponse.model,</span>
<span class="fc" id="L161">            usage = TokenUsage(</span>
<span class="fc" id="L162">                promptTokens = claudeResponse.usage.inputTokens,</span>
<span class="fc" id="L163">                completionTokens = claudeResponse.usage.outputTokens,</span>
<span class="fc" id="L164">                totalTokens = claudeResponse.usage.inputTokens + claudeResponse.usage.outputTokens</span>
            ),
<span class="fc" id="L166">            finishReason = finishReason</span>
        )
    }

    /**
     * Create headers for Claude API requests
     */
    private fun createHeaders(): Map&lt;String, String&gt; {
<span class="fc" id="L174">        return mapOf(</span>
<span class="fc" id="L175">            &quot;x-api-key&quot; to config.apiKey,</span>
<span class="fc" id="L176">            &quot;Content-Type&quot; to &quot;application/json&quot;,</span>
<span class="fc" id="L177">            &quot;anthropic-version&quot; to &quot;2023-06-01&quot;</span>
        )
    }

    /**
     * Handle HTTP exceptions and convert to Claude exceptions
     */
    private fun handleHttpException(e: HttpException): ClaudeException {
<span class="fc" id="L185">        return try {</span>
<span class="fc" id="L186">            val errorResponse = json.decodeFromString&lt;ClaudeErrorResponse&gt;(e.responseBody)</span>
<span class="fc" id="L187">            ClaudeException(</span>
<span class="fc" id="L188">                message = errorResponse.error.message,</span>
<span class="fc" id="L189">                errorType = errorResponse.error.type,</span>
<span class="fc" id="L190">                cause = e</span>
            )
<span class="nc" id="L192">        } catch (parseException: Exception) {</span>
<span class="pc" id="L193">            ClaudeException(</span>
<span class="nc" id="L194">                message = &quot;Claude API error: ${e.statusCode.value} - ${e.responseBody}&quot;,</span>
<span class="nc" id="L195">                cause = e</span>
            )
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>