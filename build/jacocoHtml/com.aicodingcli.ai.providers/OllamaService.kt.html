<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OllamaService.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.ai.providers</a> &gt; <span class="el_source">OllamaService.kt</span></div><h1>OllamaService.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.ai.providers

import com.aicodingcli.ai.*
import com.aicodingcli.http.AiHttpClient
import com.aicodingcli.http.HttpException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString

/**
 * Ollama service implementation
 */
<span class="fc" id="L15">class OllamaService(</span>
<span class="fc" id="L16">    override val config: AiServiceConfig,</span>
<span class="fc" id="L17">    private val httpClient: AiHttpClient = AiHttpClient()</span>
<span class="fc" id="L18">) : BaseAiService(config) {</span>

<span class="fc" id="L20">    private val json = Json {</span>
<span class="fc" id="L21">        ignoreUnknownKeys = true</span>
<span class="fc" id="L22">        isLenient = true</span>
<span class="fc" id="L23">    }</span>

<span class="pc bpc" id="L25" title="1 of 2 branches missed.">    private val baseUrl = config.baseUrl ?: &quot;http://localhost:11434&quot;</span>

<span class="fc" id="L27">    override suspend fun chat(request: AiRequest): AiResponse {</span>
<span class="fc" id="L28">        validateRequest(request)</span>
        
<span class="fc" id="L30">        try {</span>
<span class="fc" id="L31">            val ollamaRequest = convertToOllamaRequest(request)</span>
<span class="fc" id="L32">            val requestBody = json.encodeToString(ollamaRequest)</span>
            
<span class="fc" id="L34">            val response = httpClient.post(</span>
<span class="fc" id="L35">                url = &quot;$baseUrl/api/chat&quot;,</span>
<span class="fc" id="L36">                body = requestBody,</span>
<span class="fc" id="L37">                headers = createHeaders()</span>
            )
            
<span class="fc" id="L40">            val ollamaResponse = json.decodeFromString&lt;OllamaResponse&gt;(response.body)</span>
<span class="fc" id="L41">            return convertToAiResponse(ollamaResponse)</span>
            
<span class="fc" id="L43">        } catch (e: HttpException) {</span>
<span class="fc" id="L44">            throw handleHttpException(e)</span>
<span class="nc" id="L45">        } catch (e: Exception) {</span>
<span class="nc" id="L46">            throw OllamaException(&quot;Failed to process chat request: ${e.message}&quot;, cause = e)</span>
        }
    }

    override suspend fun streamChat(request: AiRequest): Flow&lt;AiStreamChunk&gt; {
<span class="fc" id="L51">        validateRequest(request)</span>

<span class="fc" id="L53">        return flow {</span>
<span class="fc" id="L54">            try {</span>
<span class="fc" id="L55">                val ollamaRequest = convertToOllamaRequest(request.copy(stream = true))</span>
<span class="fc" id="L56">                val requestBody = json.encodeToString(ollamaRequest)</span>

<span class="fc" id="L58">                httpClient.postStream(</span>
<span class="fc" id="L59">                    url = &quot;$baseUrl/api/chat&quot;,</span>
<span class="fc" id="L60">                    body = requestBody,</span>
<span class="fc" id="L61">                    headers = createHeaders()</span>
<span class="fc" id="L62">                ).collect { data -&gt;</span>
<span class="pc bpc" id="L63" title="2 of 4 branches missed.">                    if (data.isNotBlank()) {</span>
<span class="fc" id="L64">                        try {</span>
<span class="fc" id="L65">                            val streamResponse = json.decodeFromString&lt;OllamaResponse&gt;(data)</span>
<span class="fc" id="L66">                            val content = streamResponse.message.content</span>

<span class="fc bfc" id="L68" title="All 2 branches covered.">                            val finishReason = if (streamResponse.done) {</span>
<span class="fc" id="L69">                                when (streamResponse.doneReason) {</span>
<span class="pc bpc" id="L70" title="1 of 2 branches missed.">                                    &quot;stop&quot; -&gt; FinishReason.STOP</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">                                    &quot;length&quot; -&gt; FinishReason.LENGTH</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">                                    null -&gt; FinishReason.STOP</span>
<span class="nc" id="L73">                                    else -&gt; FinishReason.STOP</span>
                                }
                            } else {
<span class="fc" id="L76">                                null</span>
                            }

<span class="fc" id="L79">                            emit(AiStreamChunk(content, finishReason))</span>
<span class="nc" id="L80">                        } catch (e: Exception) {</span>
                            // Skip malformed JSON chunks
                        }
                    }
<span class="fc" id="L84">                }</span>

<span class="nc" id="L86">            } catch (e: HttpException) {</span>
<span class="nc" id="L87">                throw handleHttpException(e)</span>
<span class="nc" id="L88">            } catch (e: Exception) {</span>
<span class="nc" id="L89">                throw OllamaException(&quot;Failed to process streaming chat request: ${e.message}&quot;, cause = e)</span>
            }
<span class="fc" id="L91">        }</span>
    }

<span class="fc" id="L94">    override suspend fun testConnection(): Boolean {</span>
<span class="fc" id="L95">        return try {</span>
<span class="fc" id="L96">            val response = httpClient.get(</span>
<span class="fc" id="L97">                url = &quot;$baseUrl/api/tags&quot;,</span>
<span class="fc" id="L98">                headers = createHeaders()</span>
            )
            
            // Try to parse the response to ensure it's valid
<span class="fc" id="L102">            json.decodeFromString&lt;OllamaModelsResponse&gt;(response.body)</span>
<span class="fc" id="L103">            true</span>
            
<span class="fc" id="L105">        } catch (e: Exception) {</span>
<span class="fc bfc" id="L106" title="All 2 branches covered.">            false</span>
        }
    }

    /**
     * Convert AI request to Ollama format
     */
    private fun convertToOllamaRequest(request: AiRequest): OllamaRequest {
<span class="fc" id="L114">        val ollamaMessages = request.messages.map { message -&gt;</span>
<span class="fc" id="L115">            OllamaMessage(</span>
<span class="pc bpc" id="L116" title="1 of 3 branches missed.">                role = when (message.role) {</span>
<span class="nc" id="L117">                    MessageRole.SYSTEM -&gt; &quot;system&quot;</span>
<span class="fc" id="L118">                    MessageRole.USER -&gt; &quot;user&quot;</span>
<span class="fc" id="L119">                    MessageRole.ASSISTANT -&gt; &quot;assistant&quot;</span>
                },
<span class="fc" id="L121">                content = message.content</span>
<span class="fc" id="L122">            )</span>
        }
        
<span class="fc" id="L125">        val options = OllamaOptions(</span>
<span class="fc" id="L126">            temperature = request.temperature,</span>
<span class="fc" id="L127">            numPredict = request.maxTokens</span>
        )
        
<span class="fc" id="L130">        return OllamaRequest(</span>
<span class="fc" id="L131">            model = request.model,</span>
<span class="fc" id="L132">            messages = ollamaMessages,</span>
<span class="fc" id="L133">            stream = request.stream,</span>
<span class="fc" id="L134">            options = options</span>
        )
    }

    /**
     * Convert Ollama response to AI format
     */
    private fun convertToAiResponse(ollamaResponse: OllamaResponse): AiResponse {
<span class="fc" id="L142">        val content = ollamaResponse.message.content</span>
        
<span class="fc" id="L144">        val finishReason = when (ollamaResponse.doneReason) {</span>
<span class="fc bfc" id="L145" title="All 2 branches covered.">            &quot;stop&quot; -&gt; FinishReason.STOP</span>
<span class="pc bpc" id="L146" title="1 of 2 branches missed.">            &quot;length&quot; -&gt; FinishReason.LENGTH</span>
<span class="pc bpc" id="L147" title="2 of 4 branches missed.">            null -&gt; if (ollamaResponse.done) FinishReason.STOP else FinishReason.STOP</span>
<span class="nc" id="L148">            else -&gt; FinishReason.STOP</span>
        }
        
        // Calculate token usage from Ollama's metrics
<span class="pc bpc" id="L152" title="1 of 2 branches missed.">        val promptTokens = ollamaResponse.promptEvalCount ?: 0</span>
<span class="pc bpc" id="L153" title="1 of 2 branches missed.">        val completionTokens = ollamaResponse.evalCount ?: 0</span>
<span class="fc" id="L154">        val totalTokens = promptTokens + completionTokens</span>
        
<span class="fc" id="L156">        return AiResponse(</span>
<span class="fc" id="L157">            content = content,</span>
<span class="fc" id="L158">            model = ollamaResponse.model,</span>
<span class="fc" id="L159">            usage = TokenUsage(</span>
<span class="fc" id="L160">                promptTokens = promptTokens,</span>
<span class="fc" id="L161">                completionTokens = completionTokens,</span>
<span class="fc" id="L162">                totalTokens = totalTokens</span>
            ),
<span class="fc" id="L164">            finishReason = finishReason</span>
        )
    }

    /**
     * Create headers for Ollama API requests
     */
    private fun createHeaders(): Map&lt;String, String&gt; {
<span class="fc" id="L172">        return mapOf(</span>
<span class="fc" id="L173">            &quot;Content-Type&quot; to &quot;application/json&quot;</span>
        )
        // Note: Ollama doesn't require authentication headers
    }

    /**
     * Handle HTTP exceptions and convert to Ollama exceptions
     */
    private fun handleHttpException(e: HttpException): OllamaException {
<span class="fc" id="L182">        return try {</span>
<span class="fc" id="L183">            val errorResponse = json.decodeFromString&lt;OllamaErrorResponse&gt;(e.responseBody)</span>
<span class="fc" id="L184">            OllamaException(</span>
<span class="fc" id="L185">                message = errorResponse.error,</span>
<span class="fc" id="L186">                cause = e</span>
            )
<span class="nc" id="L188">        } catch (parseException: Exception) {</span>
<span class="pc" id="L189">            OllamaException(</span>
<span class="nc" id="L190">                message = &quot;Ollama API error: ${e.statusCode.value} - ${e.responseBody}&quot;,</span>
<span class="nc" id="L191">                cause = e</span>
            )
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>