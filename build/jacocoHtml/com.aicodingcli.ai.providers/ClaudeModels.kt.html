<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ClaudeModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.ai.providers</a> &gt; <span class="el_source">ClaudeModels.kt</span></div><h1>ClaudeModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.ai.providers

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Claude API request models
 */
<span class="pc bpc" id="L9" title="20 of 34 branches missed.">@Serializable</span>
<span class="fc" id="L10">data class ClaudeRequest(</span>
<span class="pc" id="L11">    val model: String,</span>
<span class="pc" id="L12">    val messages: List&lt;ClaudeMessage&gt;,</span>
<span class="pc" id="L13">    @SerialName(&quot;max_tokens&quot;)</span>
    val maxTokens: Int,
<span class="pc" id="L15">    val temperature: Float? = null,</span>
<span class="pc" id="L16">    val stream: Boolean = false,</span>
<span class="pc" id="L17">    val system: String? = null,</span>
<span class="pc" id="L18">    @SerialName(&quot;stop_sequences&quot;)</span>
<span class="pc" id="L19">    val stopSequences: List&lt;String&gt;? = null</span>
<span class="fc" id="L20">)</span>

<span class="pc bnc" id="L22" title="All 2 branches missed.">@Serializable</span>
<span class="fc" id="L23">data class ClaudeMessage(</span>
<span class="pc" id="L24">    val role: String,</span>
<span class="pc" id="L25">    val content: String</span>
)

/**
 * Claude API response models
 */
<span class="pc bpc" id="L31" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L32">data class ClaudeResponse(</span>
<span class="nc" id="L33">    val id: String,</span>
<span class="nc" id="L34">    val type: String,</span>
<span class="nc" id="L35">    val role: String,</span>
<span class="pc" id="L36">    val content: List&lt;ClaudeContent&gt;,</span>
<span class="pc" id="L37">    val model: String,</span>
<span class="pc" id="L38">    @SerialName(&quot;stop_reason&quot;)</span>
    val stopReason: String?,
<span class="nc" id="L40">    @SerialName(&quot;stop_sequence&quot;)</span>
    val stopSequence: String?,
<span class="pc" id="L42">    val usage: ClaudeUsage</span>
)

<span class="pc bpc" id="L45" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L46">data class ClaudeContent(</span>
<span class="nc" id="L47">    val type: String,</span>
<span class="pc" id="L48">    val text: String</span>
)

<span class="pc bpc" id="L51" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L52">data class ClaudeUsage(</span>
<span class="pc" id="L53">    @SerialName(&quot;input_tokens&quot;)</span>
    val inputTokens: Int,
<span class="pc" id="L55">    @SerialName(&quot;output_tokens&quot;)</span>
    val outputTokens: Int
)

/**
 * Claude streaming response models
 */
<span class="nc bnc" id="L62" title="All 34 branches missed.">@Serializable</span>
<span class="nc" id="L63">data class ClaudeStreamEvent(</span>
<span class="nc" id="L64">    val type: String,</span>
<span class="nc" id="L65">    val index: Int? = null,</span>
<span class="nc" id="L66">    val delta: ClaudeStreamDelta? = null,</span>
<span class="nc" id="L67">    val message: ClaudeStreamMessage? = null,</span>
<span class="nc" id="L68">    val usage: ClaudeUsage? = null</span>
<span class="nc" id="L69">)</span>

<span class="nc bnc" id="L71" title="All 18 branches missed.">@Serializable</span>
<span class="nc" id="L72">data class ClaudeStreamMessage(</span>
<span class="nc" id="L73">    val id: String,</span>
<span class="nc" id="L74">    val type: String,</span>
<span class="nc" id="L75">    val role: String,</span>
<span class="nc" id="L76">    val content: List&lt;ClaudeContent&gt;,</span>
<span class="nc" id="L77">    val model: String,</span>
<span class="nc" id="L78">    @SerialName(&quot;stop_reason&quot;)</span>
<span class="nc" id="L79">    val stopReason: String? = null,</span>
<span class="nc" id="L80">    @SerialName(&quot;stop_sequence&quot;)</span>
<span class="nc" id="L81">    val stopSequence: String? = null,</span>
<span class="nc" id="L82">    val usage: ClaudeUsage</span>
<span class="nc" id="L83">)</span>

<span class="nc bnc" id="L85" title="All 26 branches missed.">@Serializable</span>
<span class="nc" id="L86">data class ClaudeStreamDelta(</span>
<span class="nc" id="L87">    val type: String,</span>
<span class="nc" id="L88">    val text: String? = null,</span>
<span class="nc" id="L89">    @SerialName(&quot;stop_reason&quot;)</span>
<span class="nc" id="L90">    val stopReason: String? = null,</span>
<span class="nc" id="L91">    @SerialName(&quot;stop_sequence&quot;)</span>
<span class="nc" id="L92">    val stopSequence: String? = null</span>
<span class="nc" id="L93">)</span>

/**
 * Claude error response models
 */
<span class="pc bpc" id="L98" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L99">data class ClaudeErrorResponse(</span>
<span class="nc" id="L100">    val type: String,</span>
<span class="pc" id="L101">    val error: ClaudeError</span>
)

<span class="pc bpc" id="L104" title="1 of 2 branches missed.">@Serializable</span>
<span class="nc" id="L105">data class ClaudeError(</span>
<span class="pc" id="L106">    val type: String,</span>
<span class="pc" id="L107">    val message: String</span>
)

/**
 * Claude specific exception
 */
<span class="pc" id="L113">class ClaudeException(</span>
    message: String,
<span class="pc" id="L115">    val errorType: String? = null,</span>
<span class="nc" id="L116">    cause: Throwable? = null</span>
<span class="pc" id="L117">) : Exception(message, cause)</span>
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>