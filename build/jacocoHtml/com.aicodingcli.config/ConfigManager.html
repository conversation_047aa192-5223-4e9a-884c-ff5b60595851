<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConfigManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.config</a> &gt; <span class="el_class">ConfigManager</span></div><h1>ConfigManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">168 of 395</td><td class="ctr2">57%</td><td class="bar">13 of 22</td><td class="ctr2">40%</td><td class="ctr1">12</td><td class="ctr2">23</td><td class="ctr1">21</td><td class="ctr2">72</td><td class="ctr1">3</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a8"><a href="ConfigManager.kt.html#L102" class="el_method">removeProvider(AiProvider, Continuation)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="59" alt="59"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i2">9</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="ConfigManager.kt.html#L127" class="el_method">hasProvider(AiProvider, Continuation)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="21" alt="21"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="ConfigManager.kt.html#L87" class="el_method">setDefaultProvider(AiProvider, Continuation)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="65" height="10" title="32" alt="32"/></td><td class="ctr2" id="c8">61%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a11"><a href="ConfigManager.kt.html#L75" class="el_method">updateProviderConfig(AiProvider, AiServiceConfig, Continuation)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="81" height="10" title="40" alt="40"/></td><td class="ctr2" id="c5">70%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="ConfigManager.kt.html#L49" class="el_method">saveConfig(AppConfig, Continuation)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="56" height="10" title="28" alt="28"/></td><td class="ctr2" id="c7">62%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="1" alt="1"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="ConfigManager.kt.html#L119" class="el_method">getConfiguredProviders(Continuation)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="16" alt="16"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="ConfigManager.kt.html#L28" class="el_method">loadConfig(Continuation)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="99" height="10" title="49" alt="49"/></td><td class="ctr2" id="c4">83%</td><td class="bar" id="d7"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i0">12</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="ConfigManager.kt.html#L66" class="el_method">getCurrentProviderConfig(Continuation)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="18" alt="18"/></td><td class="ctr2" id="c6">69%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a0"><a href="ConfigManager.kt.html#L14" class="el_method">ConfigManager(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="21" alt="21"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="ConfigManager.kt.html#L136" class="el_method">createDefaultConfig()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="20" alt="20"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="ConfigManager.kt.html#L14" class="el_method">ConfigManager(String, int, DefaultConstructorMarker)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="11" alt="11"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a6"><a href="ConfigManager.kt.html#L18" class="el_method">json$lambda$0(JsonBuilder)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="8" alt="8"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>