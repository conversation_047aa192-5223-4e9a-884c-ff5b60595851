<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AppConfig.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.config</a> &gt; <span class="el_source">AppConfig.kt</span></div><h1>AppConfig.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.config

import com.aicodingcli.ai.AiProvider
import com.aicodingcli.ai.AiServiceConfig
import kotlinx.serialization.Serializable

/**
 * Application configuration
 */
<span class="pc bpc" id="L10" title="7 of 18 branches missed.">@Serializable</span>
<span class="pc" id="L11">data class AppConfig(</span>
<span class="pc" id="L12">    val defaultProvider: AiProvider = AiProvider.OPENAI,</span>
<span class="pc" id="L13">    val providers: Map&lt;AiProvider, AiServiceConfig&gt; = emptyMap()</span>
<span class="nc" id="L14">) {</span>
    /**
     * Get configuration for the default provider
     */
    fun getDefaultProviderConfig(): AiServiceConfig? {
<span class="fc" id="L19">        return providers[defaultProvider]</span>
    }

    /**
     * Get configuration for a specific provider
     */
    fun getProviderConfig(provider: AiProvider): AiServiceConfig? {
<span class="nc" id="L26">        return providers[provider]</span>
    }

    /**
     * Check if a provider is configured
     */
    fun hasProvider(provider: AiProvider): Boolean {
<span class="fc" id="L33">        return providers.containsKey(provider)</span>
    }

    /**
     * Get all configured providers
     */
    fun getConfiguredProviders(): Set&lt;AiProvider&gt; {
<span class="nc" id="L40">        return providers.keys</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>