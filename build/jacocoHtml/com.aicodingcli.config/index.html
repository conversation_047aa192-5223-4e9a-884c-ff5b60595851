<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.config</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.config</span></div><h1>com.aicodingcli.config</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">211 of 563</td><td class="ctr2">62%</td><td class="bar">20 of 40</td><td class="ctr2">50%</td><td class="ctr1">22</td><td class="ctr2">44</td><td class="ctr1">24</td><td class="ctr2">81</td><td class="ctr1">6</td><td class="ctr2">24</td><td class="ctr1">0</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a2"><a href="ConfigManager.html" class="el_class">ConfigManager</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="168" alt="168"/><img src="../jacoco-resources/greenbar.gif" width="68" height="10" title="227" alt="227"/></td><td class="ctr2" id="c2">57%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="9" alt="9"/></td><td class="ctr2" id="e1">40%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">23</td><td class="ctr1" id="h0">21</td><td class="ctr2" id="i0">72</td><td class="ctr1" id="j0">3</td><td class="ctr2" id="k0">12</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="AppConfig.html" class="el_class">AppConfig</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="43" alt="43"/><img src="../jacoco-resources/greenbar.gif" width="37" height="10" title="122" alt="122"/></td><td class="ctr2" id="c1">73%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="11" alt="11"/></td><td class="ctr2" id="e0">61%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g1">20</td><td class="ctr1" id="h1">3</td><td class="ctr2" id="i1">9</td><td class="ctr1" id="j1">3</td><td class="ctr2" id="k1">11</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="AppConfig$Companion.html" class="el_class">AppConfig.Companion</a></td><td class="bar" id="b2"/><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>