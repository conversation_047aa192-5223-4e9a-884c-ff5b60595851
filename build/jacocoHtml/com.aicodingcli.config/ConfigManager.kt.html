<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConfigManager.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.config</a> &gt; <span class="el_source">ConfigManager.kt</span></div><h1>ConfigManager.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.config

import com.aicodingcli.ai.AiProvider
import com.aicodingcli.ai.AiServiceConfig
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import java.io.File
import java.io.IOException

/**
 * Configuration manager for handling application configuration
 */
<span class="fc" id="L14">class ConfigManager(private val configDir: String = System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli&quot;) {</span>
    
<span class="fc" id="L16">    private val configFile = File(configDir, &quot;config.json&quot;)</span>
<span class="fc" id="L17">    private val json = Json {</span>
<span class="fc" id="L18">        prettyPrint = true</span>
<span class="fc" id="L19">        ignoreUnknownKeys = true</span>
<span class="fc" id="L20">    }</span>
    
    private var currentConfig: AppConfig? = null

    /**
     * Load configuration from file or create default if not exists
     */
    suspend fun loadConfig(): AppConfig {
<span class="fc" id="L28">        return try {</span>
<span class="fc bfc" id="L29" title="All 2 branches covered.">            if (configFile.exists()) {</span>
<span class="fc" id="L30">                val configContent = configFile.readText()</span>
<span class="fc" id="L31">                val config = json.decodeFromString&lt;AppConfig&gt;(configContent)</span>
<span class="fc" id="L32">                currentConfig = config</span>
<span class="fc" id="L33">                config</span>
            } else {
<span class="fc" id="L35">                val defaultConfig = createDefaultConfig()</span>
<span class="fc" id="L36">                saveConfig(defaultConfig)</span>
<span class="fc" id="L37">                currentConfig = defaultConfig</span>
<span class="fc" id="L38">                defaultConfig</span>
            }
<span class="nc" id="L40">        } catch (e: Exception) {</span>
<span class="pc" id="L41">            throw IOException(&quot;Failed to load configuration: ${e.message}&quot;, e)</span>
        }
    }

    /**
     * Save configuration to file
     */
    suspend fun saveConfig(config: AppConfig) {
<span class="fc" id="L49">        try {</span>
            // Ensure config directory exists
<span class="pc bpc" id="L51" title="1 of 2 branches missed.">            if (!File(configDir).exists()) {</span>
<span class="nc" id="L52">                File(configDir).mkdirs()</span>
            }
            
<span class="fc" id="L55">            val configContent = json.encodeToString(config)</span>
<span class="fc" id="L56">            configFile.writeText(configContent)</span>
<span class="fc" id="L57">            currentConfig = config</span>
<span class="nc" id="L58">        } catch (e: Exception) {</span>
<span class="nc" id="L59">            throw IOException(&quot;Failed to save configuration: ${e.message}&quot;, e)</span>
        }
<span class="fc" id="L61">    }</span>

    /**
     * Get current provider configuration
     */
<span class="fc" id="L66">    suspend fun getCurrentProviderConfig(): AiServiceConfig {</span>
<span class="fc bfc" id="L67" title="All 2 branches covered.">        val config = currentConfig ?: loadConfig()</span>
<span class="pc bpc" id="L68" title="1 of 2 branches missed.">        return config.getDefaultProviderConfig()</span>
<span class="nc" id="L69">            ?: throw IllegalStateException(&quot;No configuration found for default provider: ${config.defaultProvider}&quot;)</span>
    }

    /**
     * Update provider configuration
     */
<span class="pc" id="L75">    suspend fun updateProviderConfig(provider: AiProvider, serviceConfig: AiServiceConfig) {</span>
<span class="pc bpc" id="L76" title="1 of 2 branches missed.">        val config = currentConfig ?: loadConfig()</span>
<span class="fc" id="L77">        val updatedProviders = config.providers.toMutableMap()</span>
<span class="fc" id="L78">        updatedProviders[provider] = serviceConfig</span>
        
<span class="fc" id="L80">        val updatedConfig = config.copy(providers = updatedProviders)</span>
<span class="fc" id="L81">        saveConfig(updatedConfig)</span>
<span class="fc" id="L82">    }</span>

    /**
     * Set default provider
     */
<span class="pc" id="L87">    suspend fun setDefaultProvider(provider: AiProvider) {</span>
<span class="pc bpc" id="L88" title="1 of 2 branches missed.">        val config = currentConfig ?: loadConfig()</span>
        
        // Ensure the provider is configured
<span class="pc bpc" id="L91" title="1 of 2 branches missed.">        if (!config.hasProvider(provider)) {</span>
<span class="nc" id="L92">            throw IllegalArgumentException(&quot;Provider $provider is not configured&quot;)</span>
        }
        
<span class="fc" id="L95">        val updatedConfig = config.copy(defaultProvider = provider)</span>
<span class="fc" id="L96">        saveConfig(updatedConfig)</span>
<span class="fc" id="L97">    }</span>

    /**
     * Remove provider configuration
     */
<span class="nc" id="L102">    suspend fun removeProvider(provider: AiProvider) {</span>
<span class="nc bnc" id="L103" title="All 2 branches missed.">        val config = currentConfig ?: loadConfig()</span>
        
<span class="nc bnc" id="L105" title="All 2 branches missed.">        if (config.defaultProvider == provider) {</span>
<span class="nc" id="L106">            throw IllegalArgumentException(&quot;Cannot remove default provider. Set another provider as default first.&quot;)</span>
        }
        
<span class="nc" id="L109">        val updatedProviders = config.providers.toMutableMap()</span>
<span class="nc" id="L110">        updatedProviders.remove(provider)</span>
        
<span class="nc" id="L112">        val updatedConfig = config.copy(providers = updatedProviders)</span>
<span class="nc" id="L113">        saveConfig(updatedConfig)</span>
<span class="nc" id="L114">    }</span>

    /**
     * Get all configured providers
     */
<span class="nc" id="L119">    suspend fun getConfiguredProviders(): Set&lt;AiProvider&gt; {</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">        val config = currentConfig ?: loadConfig()</span>
<span class="nc" id="L121">        return config.getConfiguredProviders()</span>
    }

    /**
     * Check if provider is configured
     */
<span class="nc" id="L127">    suspend fun hasProvider(provider: AiProvider): Boolean {</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">        val config = currentConfig ?: loadConfig()</span>
<span class="nc" id="L129">        return config.hasProvider(provider)</span>
    }

    /**
     * Create default configuration
     */
    private fun createDefaultConfig(): AppConfig {
<span class="fc" id="L136">        val defaultProviders = mapOf(</span>
<span class="fc" id="L137">            AiProvider.OPENAI to AiServiceConfig(</span>
<span class="fc" id="L138">                provider = AiProvider.OPENAI,</span>
<span class="fc" id="L139">                apiKey = &quot;your-openai-api-key&quot;,</span>
<span class="fc" id="L140">                model = &quot;gpt-3.5-turbo&quot;,</span>
<span class="fc" id="L141">                baseUrl = &quot;https://api.openai.com/v1&quot;,</span>
<span class="fc" id="L142">                temperature = 0.7f,</span>
<span class="fc" id="L143">                maxTokens = 1000,</span>
<span class="fc" id="L144">                timeout = 30000L</span>
            )
        )
        
<span class="fc" id="L148">        return AppConfig(</span>
<span class="fc" id="L149">            defaultProvider = AiProvider.OPENAI,</span>
<span class="fc" id="L150">            providers = defaultProviders</span>
        )
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>