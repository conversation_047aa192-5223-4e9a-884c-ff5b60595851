<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginContext.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">PluginContext.kt</span></div><h1>PluginContext.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

import com.aicodingcli.ai.AiServiceFactory
import com.aicodingcli.config.ConfigManager
import com.aicodingcli.history.HistoryManager
import java.util.concurrent.ConcurrentHashMap

/**
 * Default implementation of PluginContext
 */
<span class="nc" id="L11">class DefaultPluginContext(</span>
<span class="nc" id="L12">    override val configManager: ConfigManager,</span>
<span class="nc" id="L13">    override val historyManager: HistoryManager,</span>
<span class="nc" id="L14">    override val aiServiceFactory: AiServiceFactory,</span>
<span class="nc" id="L15">    override val logger: PluginLogger,</span>
<span class="nc" id="L16">    private val pluginMetadata: PluginMetadata</span>
) : PluginContext {
    
<span class="nc" id="L19">    private val registeredCommands = mutableListOf&lt;PluginCommand&gt;()</span>
<span class="nc" id="L20">    private val registeredEventHandlers = mutableListOf&lt;PluginEventHandler&gt;()</span>
<span class="nc" id="L21">    private val sharedData = ConcurrentHashMap&lt;String, Any&gt;()</span>
    
    override fun registerCommand(command: Any) {
<span class="nc" id="L24">        val pluginCommand = command as PluginCommand</span>
<span class="nc" id="L25">        registeredCommands.add(pluginCommand)</span>
<span class="nc" id="L26">        logger.debug(&quot;Registered command '${pluginCommand.name}' from plugin ${pluginMetadata.id}&quot;)</span>
<span class="nc" id="L27">    }</span>

    override fun registerEventHandler(handler: Any) {
<span class="nc" id="L30">        val eventHandler = handler as PluginEventHandler</span>
<span class="nc" id="L31">        registeredEventHandlers.add(eventHandler)</span>
<span class="nc" id="L32">        logger.debug(&quot;Registered event handler for events ${eventHandler.eventTypes} from plugin ${pluginMetadata.id}&quot;)</span>
<span class="nc" id="L33">    }</span>
    
    override fun getSharedData(key: String): Any? {
<span class="nc" id="L36">        return sharedData[key]</span>
    }
    
    override fun setSharedData(key: String, value: Any) {
<span class="nc" id="L40">        sharedData[key] = value</span>
<span class="nc" id="L41">        logger.debug(&quot;Set shared data '$key' from plugin ${pluginMetadata.id}&quot;)</span>
<span class="nc" id="L42">    }</span>
    
    override fun hasPermission(permission: PluginPermission): Boolean {
<span class="nc" id="L45">        return pluginMetadata.permissions.contains(permission)</span>
    }
    
    /**
     * Get all registered commands from this context
     */
<span class="nc" id="L51">    fun getRegisteredCommands(): List&lt;PluginCommand&gt; = registeredCommands.toList()</span>
    
    /**
     * Get all registered event handlers from this context
     */
<span class="nc" id="L56">    fun getRegisteredEventHandlers(): List&lt;PluginEventHandler&gt; = registeredEventHandlers.toList()</span>
    
    /**
     * Clear all registered commands and handlers (used during plugin unload)
     */
    fun clearRegistrations() {
<span class="nc" id="L62">        registeredCommands.clear()</span>
<span class="nc" id="L63">        registeredEventHandlers.clear()</span>
<span class="nc" id="L64">        logger.debug(&quot;Cleared all registrations for plugin ${pluginMetadata.id}&quot;)</span>
<span class="nc" id="L65">    }</span>
}

/**
 * Default implementation of PluginLogger
 */
<span class="pc" id="L71">class DefaultPluginLogger(</span>
<span class="fc" id="L72">    private val pluginId: String,</span>
<span class="pc" id="L73">    private val enableDebug: Boolean = false</span>
<span class="nc" id="L74">) : PluginLogger {</span>
    
    private fun formatMessage(level: String, message: String): String {
<span class="fc" id="L77">        val timestamp = java.time.LocalDateTime.now().format(</span>
<span class="fc" id="L78">            java.time.format.DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd HH:mm:ss&quot;)</span>
        )
<span class="fc" id="L80">        return &quot;[$timestamp] [$level] [Plugin:$pluginId] $message&quot;</span>
    }
    
    override fun debug(message: String) {
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">        if (enableDebug) {</span>
<span class="fc" id="L85">            println(formatMessage(&quot;DEBUG&quot;, message))</span>
        }
<span class="fc" id="L87">    }</span>
    
    override fun info(message: String) {
<span class="fc" id="L90">        println(formatMessage(&quot;INFO&quot;, message))</span>
<span class="fc" id="L91">    }</span>
    
    override fun warn(message: String) {
<span class="nc" id="L94">        println(formatMessage(&quot;WARN&quot;, message))</span>
<span class="nc" id="L95">    }</span>
    
    override fun error(message: String, throwable: Throwable?) {
<span class="nc bnc" id="L98" title="All 2 branches missed.">        val errorMessage = if (throwable != null) {</span>
<span class="nc" id="L99">            &quot;$message: ${throwable.message}&quot;</span>
        } else {
<span class="nc" id="L101">            message</span>
        }
<span class="nc" id="L103">        System.err.println(formatMessage(&quot;ERROR&quot;, errorMessage))</span>
        
<span class="nc bnc" id="L105" title="All 4 branches missed.">        if (throwable != null &amp;&amp; enableDebug) {</span>
<span class="nc" id="L106">            throwable.printStackTrace()</span>
        }
<span class="nc" id="L108">    }</span>
}

/**
 * Plugin context factory
 */
object PluginContextFactory {
    /**
     * Create a plugin context for a specific plugin
     */
<span class="nc" id="L118">    fun createContext(</span>
        pluginMetadata: PluginMetadata,
        configManager: ConfigManager,
        historyManager: HistoryManager,
        aiServiceFactory: AiServiceFactory,
<span class="nc" id="L123">        enableDebugLogging: Boolean = false</span>
    ): DefaultPluginContext {
<span class="nc" id="L125">        val logger = DefaultPluginLogger(pluginMetadata.id, enableDebugLogging)</span>
        
<span class="nc" id="L127">        return DefaultPluginContext(</span>
<span class="nc" id="L128">            configManager = configManager,</span>
<span class="nc" id="L129">            historyManager = historyManager,</span>
<span class="nc" id="L130">            aiServiceFactory = aiServiceFactory,</span>
<span class="nc" id="L131">            logger = logger,</span>
<span class="nc" id="L132">            pluginMetadata = pluginMetadata</span>
        )
    }
}

/**
 * Plugin event dispatcher
 */
<span class="fc" id="L140">class PluginEventDispatcher {</span>
<span class="fc" id="L141">    private val eventHandlers = mutableMapOf&lt;PluginEventType, MutableList&lt;PluginEventHandler&gt;&gt;()</span>
    
    /**
     * Register an event handler
     */
    fun registerHandler(handler: PluginEventHandler) {
<span class="nc" id="L147">        handler.eventTypes.forEach { eventType -&gt;</span>
<span class="nc" id="L148">            eventHandlers.getOrPut(eventType) { mutableListOf() }.add(handler)</span>
<span class="nc" id="L149">        }</span>
<span class="nc" id="L150">    }</span>
    
    /**
     * Unregister an event handler
     */
    fun unregisterHandler(handler: PluginEventHandler) {
<span class="nc" id="L156">        handler.eventTypes.forEach { eventType -&gt;</span>
<span class="nc bnc" id="L157" title="All 2 branches missed.">            eventHandlers[eventType]?.remove(handler)</span>
<span class="nc" id="L158">        }</span>
<span class="nc" id="L159">    }</span>
    
    /**
     * Dispatch an event to all registered handlers
     */
    suspend fun dispatchEvent(event: PluginEvent) {
<span class="pc bpc" id="L165" title="1 of 2 branches missed.">        val handlers = eventHandlers[event.type] ?: return</span>
        
<span class="nc" id="L167">        handlers.forEach { handler -&gt;</span>
<span class="nc" id="L168">            try {</span>
<span class="nc" id="L169">                handler.handleEvent(event)</span>
<span class="nc" id="L170">            } catch (e: Exception) {</span>
                // Log error but don't stop other handlers
<span class="nc" id="L172">                System.err.println(&quot;Error in event handler for ${event.type}: ${e.message}&quot;)</span>
            }
<span class="nc" id="L174">        }</span>
<span class="nc" id="L175">    }</span>
    
    /**
     * Get all registered handlers for an event type
     */
    fun getHandlers(eventType: PluginEventType): List&lt;PluginEventHandler&gt; {
<span class="pc bpc" id="L181" title="3 of 4 branches missed.">        return eventHandlers[eventType]?.toList() ?: emptyList()</span>
    }
    
    /**
     * Clear all event handlers
     */
    fun clearAllHandlers() {
<span class="nc" id="L188">        eventHandlers.clear()</span>
<span class="nc" id="L189">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>