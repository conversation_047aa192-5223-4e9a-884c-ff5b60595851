<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginSecurity.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">PluginSecurity.kt</span></div><h1>PluginSecurity.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

import java.io.File
import java.net.URL
import java.security.Permission
import java.util.concurrent.ConcurrentHashMap

/**
 * Plugin security manager for controlling plugin access to system resources
 */
<span class="fc" id="L11">class PluginSecurityManager {</span>
<span class="fc" id="L12">    private val pluginSandboxes = ConcurrentHashMap&lt;String, PluginSandbox&gt;()</span>
    
    /**
     * Validate if a plugin has the required permissions for an operation
     */
    fun validatePermissions(plugin: Plugin, operation: PluginOperation): Boolean {
<span class="fc" id="L18">        val requiredPermission = operation.requiredPermission</span>
<span class="fc" id="L19">        return plugin.metadata.permissions.any { permission -&gt;</span>
<span class="fc" id="L20">            isPermissionCompatible(permission, requiredPermission)</span>
        }
    }
    
    /**
     * Create a sandbox for a plugin based on its permissions
     */
    fun createSandbox(plugin: Plugin): PluginSandbox {
<span class="fc" id="L28">        val sandbox = PluginSandbox(</span>
<span class="fc" id="L29">            pluginId = plugin.metadata.id,</span>
<span class="fc" id="L30">            allowedPaths = extractAllowedPaths(plugin.metadata.permissions),</span>
<span class="fc" id="L31">            allowedNetworkHosts = extractAllowedNetworkHosts(plugin.metadata.permissions),</span>
<span class="fc" id="L32">            allowedCommands = extractAllowedCommands(plugin.metadata.permissions),</span>
<span class="fc" id="L33">            hasConfigAccess = hasConfigPermission(plugin.metadata.permissions),</span>
<span class="fc" id="L34">            hasHistoryAccess = hasHistoryPermission(plugin.metadata.permissions)</span>
        )
        
<span class="fc" id="L37">        pluginSandboxes[plugin.metadata.id] = sandbox</span>
<span class="fc" id="L38">        return sandbox</span>
    }
    
    /**
     * Remove sandbox for a plugin
     */
    fun removeSandbox(pluginId: String) {
<span class="fc" id="L45">        pluginSandboxes.remove(pluginId)</span>
<span class="fc" id="L46">    }</span>
    
    /**
     * Get sandbox for a plugin
     */
    fun getSandbox(pluginId: String): PluginSandbox? {
<span class="fc" id="L52">        return pluginSandboxes[pluginId]</span>
    }
    
    /**
     * Check if a plugin can access a file path
     */
<span class="nc" id="L58">    fun checkFileAccess(pluginId: String, path: String, write: Boolean = false): Boolean {</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">        val sandbox = pluginSandboxes[pluginId] ?: return false</span>
<span class="nc" id="L60">        return sandbox.checkFileAccess(path, write)</span>
    }
    
    /**
     * Check if a plugin can access a network host
     */
    fun checkNetworkAccess(pluginId: String, host: String): Boolean {
<span class="nc bnc" id="L67" title="All 2 branches missed.">        val sandbox = pluginSandboxes[pluginId] ?: return false</span>
<span class="nc" id="L68">        return sandbox.checkNetworkAccess(host)</span>
    }
    
    /**
     * Check if a plugin can execute a system command
     */
    fun checkCommandExecution(pluginId: String, command: String): Boolean {
<span class="nc bnc" id="L75" title="All 2 branches missed.">        val sandbox = pluginSandboxes[pluginId] ?: return false</span>
<span class="nc" id="L76">        return sandbox.checkCommandExecution(command)</span>
    }
    
    private fun isPermissionCompatible(granted: PluginPermission, required: PluginPermission): Boolean {
<span class="fc" id="L80">        return when {</span>
<span class="fc bfc" id="L81" title="All 2 branches covered.">            granted::class == required::class -&gt; {</span>
<span class="fc" id="L82">                when (granted) {</span>
<span class="pc bpc" id="L83" title="1 of 2 branches missed.">                    is PluginPermission.FileSystemPermission -&gt; {</span>
<span class="fc" id="L84">                        val requiredFs = required as PluginPermission.FileSystemPermission</span>
<span class="fc bfc" id="L85" title="All 2 branches covered.">                        granted.allowedPaths.any { allowedPath -&gt;</span>
<span class="fc" id="L86">                            requiredFs.allowedPaths.any { requiredPath -&gt;</span>
<span class="fc" id="L87">                                requiredPath.startsWith(allowedPath)</span>
<span class="fc" id="L88">                            }</span>
<span class="pc bpc" id="L89" title="2 of 4 branches missed.">                        } &amp;&amp; (!requiredFs.readOnly || granted.readOnly)</span>
                    }
<span class="nc bnc" id="L91" title="All 2 branches missed.">                    is PluginPermission.NetworkPermission -&gt; {</span>
<span class="nc" id="L92">                        val requiredNet = required as PluginPermission.NetworkPermission</span>
<span class="nc bnc" id="L93" title="All 2 branches missed.">                        granted.allowedHosts.containsAll(requiredNet.allowedHosts) ||</span>
<span class="nc bnc" id="L94" title="All 2 branches missed.">                        granted.allowedHosts.contains(&quot;*&quot;)</span>
                    }
<span class="nc bnc" id="L96" title="All 2 branches missed.">                    is PluginPermission.SystemPermission -&gt; {</span>
<span class="nc" id="L97">                        val requiredSys = required as PluginPermission.SystemPermission</span>
<span class="nc bnc" id="L98" title="All 2 branches missed.">                        granted.allowedCommands.containsAll(requiredSys.allowedCommands) ||</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">                        granted.allowedCommands.contains(&quot;*&quot;)</span>
                    }
<span class="nc bnc" id="L101" title="All 2 branches missed.">                    is PluginPermission.ConfigPermission -&gt; true</span>
<span class="nc" id="L102">                    is PluginPermission.HistoryPermission -&gt; true</span>
                }
            }
<span class="fc" id="L105">            else -&gt; false</span>
        }
    }
    
    private fun extractAllowedPaths(permissions: List&lt;PluginPermission&gt;): List&lt;String&gt; {
<span class="fc" id="L110">        return permissions.filterIsInstance&lt;PluginPermission.FileSystemPermission&gt;()</span>
<span class="fc" id="L111">            .flatMap { it.allowedPaths }</span>
    }
    
    private fun extractAllowedNetworkHosts(permissions: List&lt;PluginPermission&gt;): List&lt;String&gt; {
<span class="fc" id="L115">        return permissions.filterIsInstance&lt;PluginPermission.NetworkPermission&gt;()</span>
<span class="fc" id="L116">            .flatMap { it.allowedHosts }</span>
    }
    
    private fun extractAllowedCommands(permissions: List&lt;PluginPermission&gt;): List&lt;String&gt; {
<span class="fc" id="L120">        return permissions.filterIsInstance&lt;PluginPermission.SystemPermission&gt;()</span>
<span class="fc" id="L121">            .flatMap { it.allowedCommands }</span>
    }
    
    private fun hasConfigPermission(permissions: List&lt;PluginPermission&gt;): Boolean {
<span class="fc" id="L125">        return permissions.any { it is PluginPermission.ConfigPermission }</span>
    }
    
    private fun hasHistoryPermission(permissions: List&lt;PluginPermission&gt;): Boolean {
<span class="fc" id="L129">        return permissions.any { it is PluginPermission.HistoryPermission }</span>
    }
}

/**
 * Plugin sandbox that enforces security restrictions
 */
<span class="fc" id="L136">class PluginSandbox(</span>
<span class="fc" id="L137">    val pluginId: String,</span>
<span class="fc" id="L138">    private val allowedPaths: List&lt;String&gt;,</span>
<span class="fc" id="L139">    private val allowedNetworkHosts: List&lt;String&gt;,</span>
<span class="fc" id="L140">    private val allowedCommands: List&lt;String&gt;,</span>
<span class="fc" id="L141">    private val hasConfigAccess: Boolean,</span>
<span class="fc" id="L142">    private val hasHistoryAccess: Boolean</span>
) {
    
    /**
     * Check if the plugin can access a file path
     */
<span class="fc" id="L148">    fun checkFileAccess(path: String, write: Boolean = false): Boolean {</span>
<span class="fc" id="L149">        val normalizedPath = File(path).canonicalPath</span>
        
<span class="fc" id="L151">        return allowedPaths.any { allowedPath -&gt;</span>
<span class="fc" id="L152">            val normalizedAllowedPath = File(allowedPath).canonicalPath</span>
<span class="fc" id="L153">            normalizedPath.startsWith(normalizedAllowedPath)</span>
        }
    }
    
    /**
     * Check if the plugin can access a network host
     */
    fun checkNetworkAccess(host: String): Boolean {
<span class="fc bfc" id="L161" title="All 2 branches covered.">        return allowedNetworkHosts.contains(host) ||</span>
<span class="fc bfc" id="L162" title="All 2 branches covered.">               allowedNetworkHosts.contains(&quot;*&quot;) ||</span>
<span class="fc bfc" id="L163" title="All 2 branches covered.">               allowedNetworkHosts.any { allowedHost -&gt;</span>
<span class="fc" id="L164">                   when {</span>
<span class="fc bfc" id="L165" title="All 2 branches covered.">                       allowedHost.startsWith(&quot;*.&quot;) -&gt; {</span>
<span class="fc" id="L166">                           val domain = allowedHost.substring(2)</span>
<span class="pc bpc" id="L167" title="1 of 4 branches missed.">                           host.endsWith(&quot;.$domain&quot;) || host == domain</span>
                       }
<span class="pc bpc" id="L169" title="2 of 4 branches missed.">                       else -&gt; host.endsWith(&quot;.$allowedHost&quot;) || host == allowedHost</span>
<span class="fc" id="L170">                   }</span>
               }
    }
    
    /**
     * Check if the plugin can execute a system command
     */
    fun checkCommandExecution(command: String): Boolean {
<span class="fc bfc" id="L178" title="All 2 branches covered.">        return allowedCommands.contains(command) ||</span>
<span class="fc bfc" id="L179" title="All 2 branches covered.">               allowedCommands.contains(&quot;*&quot;) ||</span>
<span class="fc bfc" id="L180" title="All 2 branches covered.">               allowedCommands.any { allowedCommand -&gt;</span>
<span class="fc" id="L181">                   command.startsWith(allowedCommand)</span>
               }
    }
    
    /**
     * Check if the plugin can access configuration
     */
<span class="fc" id="L188">    fun checkConfigAccess(): Boolean = hasConfigAccess</span>
    
    /**
     * Check if the plugin can access conversation history
     */
<span class="fc" id="L193">    fun checkHistoryAccess(): Boolean = hasHistoryAccess</span>
    
    /**
     * Get sandbox information
     */
    fun getInfo(): PluginSandboxInfo {
<span class="fc" id="L199">        return PluginSandboxInfo(</span>
<span class="fc" id="L200">            pluginId = pluginId,</span>
<span class="fc" id="L201">            allowedPaths = allowedPaths,</span>
<span class="fc" id="L202">            allowedNetworkHosts = allowedNetworkHosts,</span>
<span class="fc" id="L203">            allowedCommands = allowedCommands,</span>
<span class="fc" id="L204">            hasConfigAccess = hasConfigAccess,</span>
<span class="fc" id="L205">            hasHistoryAccess = hasHistoryAccess</span>
        )
    }
}

/**
 * Information about a plugin sandbox
 */
<span class="fc" id="L213">data class PluginSandboxInfo(</span>
<span class="fc" id="L214">    val pluginId: String,</span>
<span class="fc" id="L215">    val allowedPaths: List&lt;String&gt;,</span>
<span class="fc" id="L216">    val allowedNetworkHosts: List&lt;String&gt;,</span>
<span class="fc" id="L217">    val allowedCommands: List&lt;String&gt;,</span>
<span class="fc" id="L218">    val hasConfigAccess: Boolean,</span>
<span class="fc" id="L219">    val hasHistoryAccess: Boolean</span>
)

/**
 * Plugin operation that requires specific permissions
 */
<span class="fc" id="L225">data class PluginOperation(</span>
<span class="pc" id="L226">    val type: PluginOperationType,</span>
<span class="fc" id="L227">    val requiredPermission: PluginPermission,</span>
<span class="pc" id="L228">    val description: String</span>
)

/**
 * Types of plugin operations
 */
enum class PluginOperationType {
<span class="fc" id="L235">    FILE_READ,</span>
<span class="fc" id="L236">    FILE_WRITE,</span>
<span class="fc" id="L237">    NETWORK_REQUEST,</span>
<span class="fc" id="L238">    COMMAND_EXECUTION,</span>
<span class="fc" id="L239">    CONFIG_ACCESS,</span>
<span class="fc" id="L240">    HISTORY_ACCESS</span>
}

/**
 * Security exception thrown when a plugin violates security restrictions
 */
<span class="fc" id="L246">class PluginSecurityException(</span>
<span class="fc" id="L247">    val pluginId: String,</span>
<span class="fc" id="L248">    val operation: PluginOperationType,</span>
    message: String,
<span class="fc" id="L250">    cause: Throwable? = null</span>
<span class="fc" id="L251">) : SecurityException(message, cause)</span>

/**
 * Plugin security policy that defines default security settings
 */
object PluginSecurityPolicy {
    
    /**
     * Default allowed paths for plugins (relative to user home)
     */
<span class="pc" id="L261">    val DEFAULT_ALLOWED_PATHS = listOf(</span>
<span class="fc" id="L262">        System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli/plugins&quot;,</span>
<span class="fc" id="L263">        System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli/temp&quot;,</span>
<span class="fc" id="L264">        System.getProperty(&quot;java.io.tmpdir&quot;)</span>
    )
    
    /**
     * Default allowed network hosts
     */
<span class="pc" id="L270">    val DEFAULT_ALLOWED_HOSTS = listOf(</span>
<span class="fc" id="L271">        &quot;api.openai.com&quot;,</span>
<span class="fc" id="L272">        &quot;api.anthropic.com&quot;,</span>
<span class="fc" id="L273">        &quot;localhost&quot;,</span>
<span class="fc" id="L274">        &quot;127.0.0.1&quot;</span>
    )
    
    /**
     * Default allowed commands (very restrictive)
     */
<span class="pc" id="L280">    val DEFAULT_ALLOWED_COMMANDS = emptyList&lt;String&gt;()</span>
    
    /**
     * Create a default security policy for a plugin
     */
    fun createDefaultPolicy(pluginId: String): List&lt;PluginPermission&gt; {
<span class="fc" id="L286">        return listOf(</span>
<span class="fc" id="L287">            PluginPermission.FileSystemPermission(</span>
<span class="fc" id="L288">                allowedPaths = DEFAULT_ALLOWED_PATHS,</span>
<span class="fc" id="L289">                readOnly = true</span>
            ),
<span class="fc" id="L291">            PluginPermission.NetworkPermission(</span>
<span class="fc" id="L292">                allowedHosts = DEFAULT_ALLOWED_HOSTS</span>
            )
        )
    }
    
    /**
     * Validate if a permission request is reasonable
     */
    fun validatePermissionRequest(permission: PluginPermission): PluginValidationResult {
<span class="fc" id="L301">        val warnings = mutableListOf&lt;String&gt;()</span>
<span class="fc" id="L302">        val errors = mutableListOf&lt;String&gt;()</span>
        
<span class="fc" id="L304">        when (permission) {</span>
<span class="fc bfc" id="L305" title="All 2 branches covered.">            is PluginPermission.FileSystemPermission -&gt; {</span>
<span class="fc" id="L306">                permission.allowedPaths.forEach { path -&gt;</span>
<span class="pc bpc" id="L307" title="2 of 6 branches missed.">                    if (path == &quot;/&quot; || path == &quot;C:\\&quot; || path.contains(&quot;..&quot;)) {</span>
<span class="fc" id="L308">                        errors.add(&quot;Dangerous file system access requested: $path&quot;)</span>
                    }
<span class="pc bpc" id="L310" title="1 of 4 branches missed.">                    if (!permission.readOnly &amp;&amp; path.startsWith(&quot;/&quot;)) {</span>
<span class="fc" id="L311">                        warnings.add(&quot;Write access to system paths requested: $path&quot;)</span>
                    }
<span class="fc" id="L313">                }</span>
            }
<span class="fc bfc" id="L315" title="All 2 branches covered.">            is PluginPermission.NetworkPermission -&gt; {</span>
<span class="pc bpc" id="L316" title="1 of 2 branches missed.">                if (permission.allowedHosts.contains(&quot;*&quot;)) {</span>
<span class="fc" id="L317">                    warnings.add(&quot;Unrestricted network access requested&quot;)</span>
                }
            }
<span class="pc bpc" id="L320" title="1 of 2 branches missed.">            is PluginPermission.SystemPermission -&gt; {</span>
<span class="pc bpc" id="L321" title="1 of 2 branches missed.">                if (permission.allowedCommands.contains(&quot;*&quot;)) {</span>
<span class="nc" id="L322">                    errors.add(&quot;Unrestricted system command execution requested&quot;)</span>
                }
<span class="fc" id="L324">                permission.allowedCommands.forEach { command -&gt;</span>
<span class="pc bpc" id="L325" title="1 of 2 branches missed.">                    if (command in listOf(&quot;rm&quot;, &quot;del&quot;, &quot;format&quot;, &quot;sudo&quot;, &quot;su&quot;)) {</span>
<span class="fc" id="L326">                        errors.add(&quot;Dangerous system command requested: $command&quot;)</span>
                    }
<span class="fc" id="L328">                }</span>
            }
            else -&gt; {
                // Config and History permissions are generally safe
            }
        }
        
<span class="fc" id="L335">        return PluginValidationResult(</span>
<span class="fc" id="L336">            isValid = errors.isEmpty(),</span>
<span class="fc" id="L337">            errors = errors,</span>
<span class="fc" id="L338">            warnings = warnings</span>
        )
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>