<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginTemplateGenerator.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">PluginTemplateGenerator.kt</span></div><h1>PluginTemplateGenerator.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Base specification for plugin generation
 */
sealed class PluginSpec {
    abstract val pluginId: String
    abstract val pluginName: String
    abstract val version: String
    abstract val description: String
    abstract val author: String
    abstract val packageName: String
}

/**
 * Specification for command plugin generation
 */
<span class="nc" id="L22">data class CommandPluginSpec(</span>
<span class="nc" id="L23">    override val pluginId: String,</span>
<span class="nc" id="L24">    override val pluginName: String,</span>
<span class="nc" id="L25">    override val version: String = &quot;1.0.0&quot;,</span>
<span class="nc" id="L26">    override val description: String,</span>
<span class="nc" id="L27">    override val author: String,</span>
<span class="nc" id="L28">    override val packageName: String,</span>
<span class="nc" id="L29">    val commandName: String,</span>
<span class="nc" id="L30">    val commandDescription: String</span>
<span class="nc" id="L31">) : PluginSpec()</span>

/**
 * Specification for AI service plugin generation
 */
<span class="nc" id="L36">data class AiServicePluginSpec(</span>
<span class="nc" id="L37">    override val pluginId: String,</span>
<span class="nc" id="L38">    override val pluginName: String,</span>
<span class="nc" id="L39">    override val version: String = &quot;1.0.0&quot;,</span>
<span class="nc" id="L40">    override val description: String,</span>
<span class="nc" id="L41">    override val author: String,</span>
<span class="nc" id="L42">    override val packageName: String,</span>
<span class="nc" id="L43">    val providerName: String,</span>
<span class="nc" id="L44">    val defaultBaseUrl: String,</span>
<span class="nc" id="L45">    val defaultModel: String,</span>
<span class="nc" id="L46">    val requiresApiKey: Boolean = true</span>
<span class="nc" id="L47">) : PluginSpec()</span>

/**
 * Specification for event plugin generation
 */
<span class="nc" id="L52">data class EventPluginSpec(</span>
<span class="nc" id="L53">    override val pluginId: String,</span>
<span class="nc" id="L54">    override val pluginName: String,</span>
<span class="nc" id="L55">    override val version: String = &quot;1.0.0&quot;,</span>
<span class="nc" id="L56">    override val description: String,</span>
<span class="nc" id="L57">    override val author: String,</span>
<span class="nc" id="L58">    override val packageName: String,</span>
<span class="nc" id="L59">    val eventTypes: List&lt;String&gt;</span>
<span class="nc" id="L60">) : PluginSpec()</span>

/**
 * Generated plugin template
 */
<span class="nc" id="L65">data class PluginTemplate(</span>
<span class="nc" id="L66">    val files: Map&lt;String, String&gt;,</span>
<span class="nc" id="L67">    val buildScript: String,</span>
<span class="nc" id="L68">    val readme: String</span>
)

/**
 * Generator for creating plugin templates and scaffolding
 */
<span class="nc" id="L74">class PluginTemplateGenerator {</span>
    
    /**
     * Generate a command plugin template
     */
    fun generateCommandPlugin(spec: CommandPluginSpec): PluginTemplate {
<span class="nc" id="L80">        val packagePath = spec.packageName.replace(&quot;.&quot;, &quot;/&quot;)</span>
<span class="nc" id="L81">        val className = &quot;${spec.pluginName.toCamelCase()}Plugin&quot;</span>
        
<span class="nc" id="L83">        val files = mutableMapOf&lt;String, String&gt;()</span>
        
        // Main plugin class
<span class="nc" id="L86">        files[&quot;src/main/kotlin/$packagePath/$className.kt&quot;] = generateCommandPluginClass(spec, className)</span>
        
        // Plugin metadata
<span class="nc" id="L89">        files[&quot;src/main/resources/plugin.json&quot;] = generatePluginMetadata(spec)</span>
        
        // Build script
<span class="nc" id="L92">        files[&quot;build.gradle.kts&quot;] = generateBuildScript(spec)</span>
        
        // README
<span class="nc" id="L95">        files[&quot;README.md&quot;] = generateReadme(spec)</span>
        
        // Example test
<span class="nc" id="L98">        files[&quot;src/test/kotlin/$packagePath/${className}Test.kt&quot;] = generateCommandPluginTest(spec, className)</span>
        
<span class="nc" id="L100">        return PluginTemplate(</span>
<span class="nc" id="L101">            files = files,</span>
<span class="nc" id="L102">            buildScript = files[&quot;build.gradle.kts&quot;]!!,</span>
<span class="nc" id="L103">            readme = files[&quot;README.md&quot;]!!</span>
        )
    }
    
    /**
     * Generate an AI service plugin template
     */
    fun generateAiServicePlugin(spec: AiServicePluginSpec): PluginTemplate {
<span class="nc" id="L111">        val packagePath = spec.packageName.replace(&quot;.&quot;, &quot;/&quot;)</span>
<span class="nc" id="L112">        val className = &quot;${spec.providerName.toCamelCase()}ServicePlugin&quot;</span>
<span class="nc" id="L113">        val serviceClassName = &quot;${spec.providerName.toCamelCase()}Service&quot;</span>
        
<span class="nc" id="L115">        val files = mutableMapOf&lt;String, String&gt;()</span>
        
        // Main plugin class
<span class="nc" id="L118">        files[&quot;src/main/kotlin/$packagePath/$className.kt&quot;] = generateAiServicePluginClass(spec, className, serviceClassName)</span>
        
        // AI service implementation
<span class="nc" id="L121">        files[&quot;src/main/kotlin/$packagePath/$serviceClassName.kt&quot;] = generateAiServiceClass(spec, serviceClassName)</span>
        
        // Plugin metadata
<span class="nc" id="L124">        files[&quot;src/main/resources/plugin.json&quot;] = generatePluginMetadata(spec)</span>
        
        // Build script
<span class="nc" id="L127">        files[&quot;build.gradle.kts&quot;] = generateBuildScript(spec)</span>
        
        // README
<span class="nc" id="L130">        files[&quot;README.md&quot;] = generateReadme(spec)</span>
        
        // Example test
<span class="nc" id="L133">        files[&quot;src/test/kotlin/$packagePath/${className}Test.kt&quot;] = generateAiServicePluginTest(spec, className)</span>
        
<span class="nc" id="L135">        return PluginTemplate(</span>
<span class="nc" id="L136">            files = files,</span>
<span class="nc" id="L137">            buildScript = files[&quot;build.gradle.kts&quot;]!!,</span>
<span class="nc" id="L138">            readme = files[&quot;README.md&quot;]!!</span>
        )
    }
    
    /**
     * Generate an event plugin template
     */
    fun generateEventPlugin(spec: EventPluginSpec): PluginTemplate {
<span class="nc" id="L146">        val packagePath = spec.packageName.replace(&quot;.&quot;, &quot;/&quot;)</span>
<span class="nc" id="L147">        val className = &quot;${spec.pluginName.toCamelCase()}Plugin&quot;</span>
        
<span class="nc" id="L149">        val files = mutableMapOf&lt;String, String&gt;()</span>
        
        // Main plugin class
<span class="nc" id="L152">        files[&quot;src/main/kotlin/$packagePath/$className.kt&quot;] = generateEventPluginClass(spec, className)</span>
        
        // Plugin metadata
<span class="nc" id="L155">        files[&quot;src/main/resources/plugin.json&quot;] = generatePluginMetadata(spec)</span>
        
        // Build script
<span class="nc" id="L158">        files[&quot;build.gradle.kts&quot;] = generateBuildScript(spec)</span>
        
        // README
<span class="nc" id="L161">        files[&quot;README.md&quot;] = generateReadme(spec)</span>
        
        // Example test
<span class="nc" id="L164">        files[&quot;src/test/kotlin/$packagePath/${className}Test.kt&quot;] = generateEventPluginTest(spec, className)</span>
        
<span class="nc" id="L166">        return PluginTemplate(</span>
<span class="nc" id="L167">            files = files,</span>
<span class="nc" id="L168">            buildScript = files[&quot;build.gradle.kts&quot;]!!,</span>
<span class="nc" id="L169">            readme = files[&quot;README.md&quot;]!!</span>
        )
    }
    
    /**
     * Create plugin project structure on disk
     */
    fun createPluginProject(template: PluginTemplate, outputDir: String) {
<span class="nc" id="L177">        val projectDir = File(outputDir)</span>
<span class="nc bnc" id="L178" title="All 2 branches missed.">        if (!projectDir.exists()) {</span>
<span class="nc" id="L179">            projectDir.mkdirs()</span>
        }
        
<span class="nc" id="L182">        template.files.forEach { (filePath, content) -&gt;</span>
<span class="nc" id="L183">            val file = File(projectDir, filePath)</span>
<span class="nc" id="L184">            file.parentFile.mkdirs()</span>
<span class="nc" id="L185">            file.writeText(content)</span>
<span class="nc" id="L186">        }</span>
        
<span class="nc" id="L188">        println(&quot;✅ Plugin project created at: ${projectDir.absolutePath}&quot;)</span>
<span class="nc" id="L189">        println(&quot;📁 Files created:&quot;)</span>
<span class="nc" id="L190">        template.files.keys.sorted().forEach { filePath -&gt;</span>
<span class="nc" id="L191">            println(&quot;  - $filePath&quot;)</span>
<span class="nc" id="L192">        }</span>
<span class="nc" id="L193">    }</span>
    
    private fun generateCommandPluginClass(spec: CommandPluginSpec, className: String): String {
<span class="nc" id="L196">        return &quot;&quot;&quot;</span>
<span class="nc" id="L197">package ${spec.packageName}</span>

import com.aicodingcli.plugins.*

/**
<span class="nc" id="L202"> * ${spec.description}</span>
 */
<span class="nc" id="L204">class $className : BaseCommandPlugin() {</span>
    
    override val metadata = PluginMetadata(
<span class="nc" id="L207">        id = &quot;${spec.pluginId}&quot;,</span>
<span class="nc" id="L208">        name = &quot;${spec.pluginName}&quot;,</span>
<span class="nc" id="L209">        version = &quot;${spec.version}&quot;,</span>
<span class="nc" id="L210">        description = &quot;${spec.description}&quot;,</span>
<span class="nc" id="L211">        author = &quot;${spec.author}&quot;,</span>
<span class="nc" id="L212">        mainClass = &quot;${spec.packageName}.$className&quot;</span>
    )
    
    override val commands = listOf(
        createCommand(
<span class="nc" id="L217">            name = &quot;${spec.commandName}&quot;,</span>
<span class="nc" id="L218">            description = &quot;${spec.commandDescription}&quot;,</span>
<span class="nc" id="L219">            usage = &quot;${spec.commandName} [options]&quot;,</span>
            options = listOf(
                CommandOption(
                    name = &quot;output&quot;,
                    shortName = &quot;o&quot;,
                    description = &quot;Output format&quot;,
                    required = false,
                    defaultValue = &quot;text&quot;
                )
            )
        ) { args, context -&gt;
            // TODO: Implement your command logic here
            val output = args.getOptionOrDefault(&quot;output&quot;, &quot;text&quot;)
            
<span class="nc" id="L233">            context.logger.info(&quot;Executing ${spec.commandName} command with output format: ${'$'}output&quot;)</span>
            
<span class="nc" id="L235">            CommandResult.success(&quot;${spec.commandName} executed successfully!&quot;)</span>
        }
    )
}
<span class="nc" id="L239">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateAiServicePluginClass(spec: AiServicePluginSpec, className: String, serviceClassName: String): String {
<span class="nc" id="L243">        return &quot;&quot;&quot;</span>
<span class="nc" id="L244">package ${spec.packageName}</span>

import com.aicodingcli.plugins.*
import com.aicodingcli.ai.*

/**
<span class="nc" id="L250"> * ${spec.description}</span>
 */
<span class="nc" id="L252">class $className : BaseAiServicePlugin() {</span>
    
    override val metadata = PluginMetadata(
<span class="nc" id="L255">        id = &quot;${spec.pluginId}&quot;,</span>
<span class="nc" id="L256">        name = &quot;${spec.pluginName}&quot;,</span>
<span class="nc" id="L257">        version = &quot;${spec.version}&quot;,</span>
<span class="nc" id="L258">        description = &quot;${spec.description}&quot;,</span>
<span class="nc" id="L259">        author = &quot;${spec.author}&quot;,</span>
<span class="nc" id="L260">        mainClass = &quot;${spec.packageName}.$className&quot;</span>
    )
    
<span class="nc" id="L263">    override val supportedProvider = AiProvider.${spec.providerName.uppercase()}</span>
    
    override fun createAiService(config: AiServiceConfig): AiService {
<span class="nc" id="L266">        return $serviceClassName(config)</span>
    }
    
    override fun getSupportedModels(): List&lt;String&gt; {
        return listOf(
<span class="nc" id="L271">            // TODO: Add supported models for ${spec.providerName}</span>
            &quot;model-1&quot;,
            &quot;model-2&quot;
        )
    }
    
    override fun getDefaultBaseUrl(): String {
<span class="nc" id="L278">        return &quot;${spec.defaultBaseUrl}&quot;</span>
    }
    
    override fun getDefaultModel(): String {
<span class="nc" id="L282">        return &quot;${spec.defaultModel}&quot;</span>
    }
    
    override fun requiresApiKey(): Boolean {
<span class="nc" id="L286">        return ${spec.requiresApiKey}</span>
    }
}
<span class="nc" id="L289">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateAiServiceClass(spec: AiServicePluginSpec, serviceClassName: String): String {
<span class="nc" id="L293">        return &quot;&quot;&quot;</span>
<span class="nc" id="L294">package ${spec.packageName}</span>

import com.aicodingcli.ai.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

/**
<span class="nc" id="L301"> * AI service implementation for ${spec.providerName}</span>
 */
<span class="nc" id="L303">class $serviceClassName(override val config: AiServiceConfig) : BaseAiService(config) {</span>
    
    override suspend fun chat(request: AiRequest): AiResponse {
        validateRequest(request)
        
<span class="nc" id="L308">        // TODO: Implement actual API call to ${spec.providerName}</span>
        // This is a placeholder implementation
        
        return AiResponse(
<span class="nc" id="L312">            content = &quot;Response from ${spec.providerName}: ${'$'}{request.messages.last().content}&quot;,</span>
            model = request.model,
            usage = TokenUsage(
                promptTokens = 10,
                completionTokens = 20,
                totalTokens = 30
            ),
            finishReason = FinishReason.STOP
        )
    }
    
    override suspend fun streamChat(request: AiRequest): Flow&lt;AiStreamChunk&gt; {
        validateRequest(request)
        
<span class="nc" id="L326">        // TODO: Implement actual streaming API call to ${spec.providerName}</span>
        // This is a placeholder implementation
        
        return flowOf(
            AiStreamChunk(&quot;Response &quot;, null),
            AiStreamChunk(&quot;from &quot;, null),
<span class="nc" id="L332">            AiStreamChunk(&quot;${spec.providerName}&quot;, FinishReason.STOP)</span>
        )
    }
    
    override suspend fun testConnection(): Boolean {
        return try {
<span class="nc" id="L338">            // TODO: Implement actual connection test to ${spec.providerName}</span>
            // This is a placeholder implementation
            true
        } catch (e: Exception) {
            false
        }
    }
}
<span class="nc" id="L346">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateEventPluginClass(spec: EventPluginSpec, className: String): String {
<span class="nc" id="L350">        return &quot;&quot;&quot;</span>
<span class="nc" id="L351">package ${spec.packageName}</span>

import com.aicodingcli.plugins.*

/**
<span class="nc" id="L356"> * ${spec.description}</span>
 */
<span class="nc" id="L358">class $className : Plugin {</span>
    
    override val metadata = PluginMetadata(
<span class="nc" id="L361">        id = &quot;${spec.pluginId}&quot;,</span>
<span class="nc" id="L362">        name = &quot;${spec.pluginName}&quot;,</span>
<span class="nc" id="L363">        version = &quot;${spec.version}&quot;,</span>
<span class="nc" id="L364">        description = &quot;${spec.description}&quot;,</span>
<span class="nc" id="L365">        author = &quot;${spec.author}&quot;,</span>
<span class="nc" id="L366">        mainClass = &quot;${spec.packageName}.$className&quot;</span>
    )
    
    private lateinit var context: PluginContext
    
    override fun initialize(context: PluginContext) {
        this.context = context
        
        // Register event handler
        context.registerEventHandler(object : PluginEventHandler {
            override val eventTypes = setOf(
<span class="nc" id="L377">                ${spec.eventTypes.joinToString(&quot;,\n                &quot;) { &quot;PluginEventType.$it&quot; }}</span>
            )
            
            override suspend fun handleEvent(event: PluginEvent) {
                when (event.type) {
<span class="nc" id="L382">                    ${spec.eventTypes.joinToString(&quot;\n                    &quot;) { </span>
<span class="nc" id="L383">                        &quot;PluginEventType.$it -&gt; handle${it.toCamelCase()}Event(event)&quot;</span>
                    }}
                    else -&gt; {
                        // Ignore other events
                    }
                }
            }
        })
        
<span class="nc" id="L392">        context.logger.info(&quot;${spec.pluginName} plugin initialized&quot;)</span>
    }
    
    override fun shutdown() {
<span class="nc" id="L396">        context.logger.info(&quot;${spec.pluginName} plugin shut down&quot;)</span>
    }
    
<span class="nc" id="L399">    ${spec.eventTypes.joinToString(&quot;\n    \n    &quot;) { eventType -&gt;</span>
<span class="nc" id="L400">        &quot;&quot;&quot;private suspend fun handle${eventType.toCamelCase()}Event(event: PluginEvent) {</span>
<span class="nc" id="L401">        // TODO: Implement $eventType event handling</span>
<span class="nc" id="L402">        context.logger.info(&quot;Handling $eventType event: ${'$'}{event.data}&quot;)</span>
<span class="nc" id="L403">    }&quot;&quot;&quot;</span>
    }}
}
<span class="nc" id="L406">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generatePluginMetadata(spec: PluginSpec): String {
<span class="nc" id="L410">        return &quot;&quot;&quot;</span>
{
<span class="nc" id="L412">  &quot;id&quot;: &quot;${spec.pluginId}&quot;,</span>
<span class="nc" id="L413">  &quot;name&quot;: &quot;${spec.pluginName}&quot;,</span>
<span class="nc" id="L414">  &quot;version&quot;: &quot;${spec.version}&quot;,</span>
<span class="nc" id="L415">  &quot;description&quot;: &quot;${spec.description}&quot;,</span>
<span class="nc" id="L416">  &quot;author&quot;: &quot;${spec.author}&quot;,</span>
<span class="nc" id="L417">  &quot;mainClass&quot;: &quot;${spec.packageName}.${spec.pluginName.toCamelCase()}Plugin&quot;,</span>
  &quot;dependencies&quot;: [],
  &quot;permissions&quot;: [
    {
      &quot;type&quot;: &quot;filesystem&quot;,
      &quot;allowedPaths&quot;: [&quot;./temp&quot;],
      &quot;readOnly&quot;: false
    }
  ]
}
<span class="nc" id="L427">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateBuildScript(spec: PluginSpec): String {
<span class="nc" id="L431">        return &quot;&quot;&quot;</span>
plugins {
    kotlin(&quot;jvm&quot;) version &quot;1.9.10&quot;
    kotlin(&quot;plugin.serialization&quot;) version &quot;1.9.10&quot;
    id(&quot;com.github.johnrengelman.shadow&quot;) version &quot;8.1.1&quot;
}

<span class="nc" id="L438">group = &quot;${spec.packageName}&quot;</span>
<span class="nc" id="L439">version = &quot;${spec.version}&quot;</span>

repositories {
    mavenCentral()
}

dependencies {
    implementation(&quot;org.jetbrains.kotlin:kotlin-stdlib&quot;)
    implementation(&quot;org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3&quot;)
    implementation(&quot;org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0&quot;)
    implementation(&quot;io.ktor:ktor-client-core:2.3.4&quot;)
    implementation(&quot;io.ktor:ktor-client-cio:2.3.4&quot;)
    implementation(&quot;io.ktor:ktor-client-content-negotiation:2.3.4&quot;)
    implementation(&quot;io.ktor:ktor-serialization-kotlinx-json:2.3.4&quot;)
    
    // Add AI Coding CLI as a dependency (you'll need to publish it to a repository)
    // implementation(&quot;com.aicodingcli:core:0.1.0&quot;)
    
    testImplementation(&quot;org.jetbrains.kotlin:kotlin-test&quot;)
    testImplementation(&quot;org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3&quot;)
    testImplementation(&quot;io.mockk:mockk:1.13.7&quot;)
}

tasks.test {
    useJUnitPlatform()
}

kotlin {
    jvmToolchain(11)
}

tasks.shadowJar {
    archiveClassifier.set(&quot;&quot;)
    manifest {
<span class="nc" id="L473">        attributes[&quot;Main-Class&quot;] = &quot;${spec.packageName}.${spec.pluginName.toCamelCase()}Plugin&quot;</span>
    }
}

tasks.build {
    dependsOn(tasks.shadowJar)
}
<span class="nc" id="L480">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateReadme(spec: PluginSpec): String {
<span class="nc" id="L484">        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd&quot;))</span>
        
<span class="nc" id="L486">        return &quot;&quot;&quot;</span>
<span class="nc" id="L487"># ${spec.pluginName}</span>

<span class="nc" id="L489">${spec.description}</span>

## Information

<span class="nc" id="L493">- **Plugin ID**: ${spec.pluginId}</span>
<span class="nc" id="L494">- **Version**: ${spec.version}</span>
<span class="nc" id="L495">- **Author**: ${spec.author}</span>
<span class="nc" id="L496">- **Created**: $timestamp</span>

## Building

To build the plugin:

```bash
./gradlew build
```

This will create a JAR file in `build/libs/` that can be installed in AI Coding CLI.

## Installation

1. Build the plugin (see above)
2. Install using AI Coding CLI:

```bash
<span class="nc" id="L514">ai-coding-cli plugin install build/libs/${spec.pluginId}-${spec.version}.jar</span>
```

## Usage

<span class="nc" id="L519">${when (spec) {</span>
<span class="nc bnc" id="L520" title="All 2 branches missed.">    is CommandPluginSpec -&gt; &quot;&quot;&quot;</span>
This plugin provides the following command:

```bash
<span class="nc" id="L524">ai-coding-cli ${spec.commandName} [options]</span>
```

<span class="nc" id="L527">${spec.commandDescription}</span>
&quot;&quot;&quot;
<span class="nc bnc" id="L529" title="All 2 branches missed.">    is AiServicePluginSpec -&gt; &quot;&quot;&quot;</span>
<span class="nc" id="L530">This plugin adds support for ${spec.providerName} AI service.</span>

After installation, you can use it by configuring the provider:

```bash
<span class="nc" id="L535">ai-coding-cli config set ${spec.providerName.lowercase()}.api_key your-api-key</span>
<span class="nc" id="L536">ai-coding-cli config provider ${spec.providerName.lowercase()}</span>
```
&quot;&quot;&quot;
<span class="nc bnc" id="L539" title="All 2 branches missed.">    is EventPluginSpec -&gt; &quot;&quot;&quot;</span>
This plugin handles the following events:
<span class="nc" id="L541">${spec.eventTypes.joinToString(&quot;\n&quot;) { &quot;- $it&quot; }}</span>

The plugin will automatically respond to these events when they occur.
&quot;&quot;&quot;
<span class="nc" id="L545">    else -&gt; &quot;See plugin documentation for usage instructions.&quot;</span>
}}

## Development

This plugin was generated using the AI Coding CLI plugin template generator.

To modify the plugin:

1. Edit the source files in `src/main/kotlin/`
2. Update the plugin metadata in `src/main/resources/plugin.json` if needed
3. Rebuild and reinstall the plugin

## License

[Add your license information here]
<span class="nc" id="L561">        &quot;&quot;&quot;.trimIndent()</span>
    }
    
    private fun generateCommandPluginTest(spec: CommandPluginSpec, className: String): String {
<span class="nc" id="L565">        return &quot;&quot;&quot;</span>
<span class="nc" id="L566">package ${spec.packageName}</span>

import com.aicodingcli.plugins.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import io.mockk.mockk

<span class="nc" id="L575">class ${className}Test {</span>

<span class="nc" id="L577">    private lateinit var plugin: $className</span>
    private lateinit var mockContext: PluginContext

    @BeforeEach
    fun setUp() {
<span class="nc" id="L582">        plugin = $className()</span>
        mockContext = mockk&lt;PluginContext&gt;(relaxed = true)
    }

    @Test
    fun `should have correct metadata`() {
<span class="nc" id="L588">        assertEquals(&quot;${spec.pluginId}&quot;, plugin.metadata.id)</span>
<span class="nc" id="L589">        assertEquals(&quot;${spec.pluginName}&quot;, plugin.metadata.name)</span>
<span class="nc" id="L590">        assertEquals(&quot;${spec.version}&quot;, plugin.metadata.version)</span>
<span class="nc" id="L591">        assertEquals(&quot;${spec.author}&quot;, plugin.metadata.author)</span>
    }

    @Test
<span class="nc" id="L595">    fun `should provide ${spec.commandName} command`() {</span>
<span class="nc" id="L596">        assertTrue(plugin.hasCommand(&quot;${spec.commandName}&quot;))</span>
<span class="nc" id="L597">        val command = plugin.getCommand(&quot;${spec.commandName}&quot;)</span>
        assertNotNull(command)
<span class="nc" id="L599">        assertEquals(&quot;${spec.commandName}&quot;, command?.name)</span>
    }

    @Test
<span class="nc" id="L603">    fun `should execute ${spec.commandName} command successfully`() = runBlocking {</span>
        plugin.initialize(mockContext)

<span class="nc" id="L606">        val command = plugin.getCommand(&quot;${spec.commandName}&quot;)</span>
        assertNotNull(command)

        val args = CommandArgs(
            args = emptyList(),
            options = mapOf(&quot;output&quot; to &quot;text&quot;),
            rawArgs = arrayOf()
        )

        val result = command!!.handler(args, mockContext)
        assertTrue(result.success)
        assertNotNull(result.message)
    }
}
<span class="nc" id="L620">        &quot;&quot;&quot;.trimIndent()</span>
    }

    private fun generateAiServicePluginTest(spec: AiServicePluginSpec, className: String): String {
<span class="nc" id="L624">        return &quot;&quot;&quot;</span>
<span class="nc" id="L625">package ${spec.packageName}</span>

import com.aicodingcli.plugins.*
import com.aicodingcli.ai.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import io.mockk.mockk

<span class="nc" id="L635">class ${className}Test {</span>

<span class="nc" id="L637">    private lateinit var plugin: $className</span>
    private lateinit var mockContext: PluginContext

    @BeforeEach
    fun setUp() {
<span class="nc" id="L642">        plugin = $className()</span>
        mockContext = mockk&lt;PluginContext&gt;(relaxed = true)
    }

    @Test
    fun `should have correct metadata`() {
<span class="nc" id="L648">        assertEquals(&quot;${spec.pluginId}&quot;, plugin.metadata.id)</span>
<span class="nc" id="L649">        assertEquals(&quot;${spec.pluginName}&quot;, plugin.metadata.name)</span>
<span class="nc" id="L650">        assertEquals(&quot;${spec.version}&quot;, plugin.metadata.version)</span>
<span class="nc" id="L651">        assertEquals(&quot;${spec.author}&quot;, plugin.metadata.author)</span>
    }

    @Test
    fun `should support correct AI provider`() {
<span class="nc" id="L656">        assertEquals(AiProvider.${spec.providerName.uppercase()}, plugin.supportedProvider)</span>
    }

    @Test
    fun `should create AI service`() {
        val config = AiServiceConfig(
<span class="nc" id="L662">            provider = AiProvider.${spec.providerName.uppercase()},</span>
            apiKey = &quot;test-key&quot;,
<span class="nc" id="L664">            model = &quot;${spec.defaultModel}&quot;,</span>
<span class="nc" id="L665">            baseUrl = &quot;${spec.defaultBaseUrl}&quot;</span>
        )

        val service = plugin.createAiService(config)
        assertNotNull(service)
        assertEquals(config, service.config)
    }

    @Test
    fun `should validate configuration`() {
        val validConfig = AiServiceConfig(
<span class="nc" id="L676">            provider = AiProvider.${spec.providerName.uppercase()},</span>
            apiKey = &quot;test-key&quot;,
<span class="nc" id="L678">            model = &quot;${spec.defaultModel}&quot;,</span>
<span class="nc" id="L679">            baseUrl = &quot;${spec.defaultBaseUrl}&quot;</span>
        )

        val result = plugin.validateConfig(validConfig)
        assertTrue(result.isValid)
    }

    @Test
    fun `should provide supported models`() {
        val models = plugin.getSupportedModels()
        assertNotNull(models)
        assertTrue(models.isNotEmpty())
    }
}
<span class="nc" id="L693">        &quot;&quot;&quot;.trimIndent()</span>
    }

    private fun generateEventPluginTest(spec: EventPluginSpec, className: String): String {
<span class="nc" id="L697">        return &quot;&quot;&quot;</span>
<span class="nc" id="L698">package ${spec.packageName}</span>

import com.aicodingcli.plugins.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import io.mockk.mockk
import io.mockk.verify

<span class="nc" id="L708">class ${className}Test {</span>

<span class="nc" id="L710">    private lateinit var plugin: $className</span>
    private lateinit var mockContext: PluginContext

    @BeforeEach
    fun setUp() {
<span class="nc" id="L715">        plugin = $className()</span>
        mockContext = mockk&lt;PluginContext&gt;(relaxed = true)
    }

    @Test
    fun `should have correct metadata`() {
<span class="nc" id="L721">        assertEquals(&quot;${spec.pluginId}&quot;, plugin.metadata.id)</span>
<span class="nc" id="L722">        assertEquals(&quot;${spec.pluginName}&quot;, plugin.metadata.name)</span>
<span class="nc" id="L723">        assertEquals(&quot;${spec.version}&quot;, plugin.metadata.version)</span>
<span class="nc" id="L724">        assertEquals(&quot;${spec.author}&quot;, plugin.metadata.author)</span>
    }

    @Test
    fun `should initialize and register event handler`() {
        plugin.initialize(mockContext)

        verify { mockContext.registerEventHandler(any()) }
    }

    @Test
    fun `should handle events correctly`() = runBlocking {
        plugin.initialize(mockContext)

        // Test would require more complex setup to verify event handling
        // This is a basic structure for event plugin testing
        assertTrue(true) // Placeholder assertion
    }
}
<span class="nc" id="L743">        &quot;&quot;&quot;.trimIndent()</span>
    }

    private fun String.toCamelCase(): String {
<span class="nc" id="L747">        return split(&quot;-&quot;, &quot;_&quot;, &quot; &quot;)</span>
<span class="nc" id="L748">            .joinToString(&quot;&quot;) { word -&gt;</span>
<span class="nc bnc" id="L749" title="All 4 branches missed.">                word.lowercase().replaceFirstChar { it.uppercase() }</span>
            }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>