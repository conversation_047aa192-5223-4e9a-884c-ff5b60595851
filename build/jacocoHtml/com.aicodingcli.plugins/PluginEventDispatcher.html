<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginEventDispatcher</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_class">PluginEventDispatcher</span></div><h1>PluginEventDispatcher</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">82 of 112</td><td class="ctr2">26%</td><td class="bar">6 of 8</td><td class="ctr2">25%</td><td class="ctr1">7</td><td class="ctr2">10</td><td class="ctr1">17</td><td class="ctr2">21</td><td class="ctr1">3</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a1"><a href="PluginContext.kt.html#L165" class="el_method">dispatchEvent(PluginEvent, Continuation)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="33" alt="33"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="11" alt="11"/></td><td class="ctr2" id="c2">25%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h0">7</td><td class="ctr2" id="i0">8</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="PluginContext.kt.html#L147" class="el_method">registerHandler(PluginEventHandler)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="21" alt="21"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="PluginContext.kt.html#L156" class="el_method">unregisterHandler(PluginEventHandler)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="54" height="10" title="20" alt="20"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i2">4</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="PluginContext.kt.html#L181" class="el_method">getHandlers(PluginEventType)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="10" alt="10"/></td><td class="ctr2" id="c1">71%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">25%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="PluginContext.kt.html#L188" class="el_method">clearAllHandlers()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="PluginContext.kt.html#L140" class="el_method">PluginEventDispatcher()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="9" alt="9"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>