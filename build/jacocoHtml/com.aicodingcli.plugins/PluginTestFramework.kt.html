<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginTestFramework.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">PluginTestFramework.kt</span></div><h1>PluginTestFramework.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

import com.aicodingcli.ai.*
import com.aicodingcli.config.ConfigManager
import com.aicodingcli.history.HistoryManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import java.util.concurrent.ConcurrentHashMap

/**
 * Test framework for plugin development and testing
 */
<span class="fc" id="L13">class PluginTestFramework {</span>
    
    /**
     * Create a test plugin context with mock services
     */
<span class="fc" id="L18">    fun createTestContext(</span>
<span class="fc" id="L19">        pluginMetadata: PluginMetadata = createTestPluginMetadata(),</span>
<span class="fc" id="L20">        enableDebugLogging: Boolean = true</span>
    ): TestPluginContext {
<span class="fc" id="L22">        val mockConfigManager = MockConfigManager()</span>
<span class="fc" id="L23">        val mockHistoryManager = MockHistoryManager()</span>
<span class="fc" id="L24">        val mockAiServiceFactory = MockAiServiceFactory()</span>
<span class="fc" id="L25">        val logger = DefaultPluginLogger(pluginMetadata.id, enableDebugLogging)</span>

<span class="fc" id="L27">        return TestPluginContext(</span>
<span class="fc" id="L28">            mockConfigManager = mockConfigManager,</span>
<span class="fc" id="L29">            mockHistoryManager = mockHistoryManager,</span>
<span class="fc" id="L30">            mockAiServiceFactory = mockAiServiceFactory,</span>
<span class="fc" id="L31">            logger = logger,</span>
<span class="fc" id="L32">            pluginMetadata = pluginMetadata</span>
        )
    }
    
    /**
     * Create a mock AI service for testing
     */
<span class="nc" id="L39">    fun mockAiService(</span>
        provider: AiProvider,
<span class="nc" id="L41">        responses: List&lt;String&gt; = listOf(&quot;Mock response&quot;),</span>
<span class="nc" id="L42">        shouldFail: Boolean = false</span>
    ): AiService {
<span class="nc" id="L44">        return MockAiService(provider, responses, shouldFail)</span>
    }
    
    /**
     * Simulate command execution for testing
     */
<span class="fc" id="L50">    suspend fun simulateCommand(</span>
        plugin: CommandPlugin,
        commandName: String,
<span class="fc" id="L53">        args: Array&lt;String&gt; = emptyArray(),</span>
<span class="fc" id="L54">        options: Map&lt;String, String&gt; = emptyMap()</span>
    ): CommandResult {
<span class="fc" id="L56">        val context = createTestContext(plugin.metadata)</span>
<span class="fc" id="L57">        plugin.initialize(context)</span>
        
<span class="fc bfc" id="L59" title="All 2 branches covered.">        val command = plugin.getCommand(commandName)</span>
<span class="fc" id="L60">            ?: return CommandResult.failure(&quot;Command not found: $commandName&quot;)</span>
        
<span class="fc" id="L62">        val commandArgs = CommandArgs(</span>
<span class="fc" id="L63">            args = args.toList(),</span>
<span class="fc" id="L64">            options = options,</span>
<span class="fc" id="L65">            rawArgs = args</span>
        )
        
<span class="fc" id="L68">        return command.handler(commandArgs, context)</span>
    }
    
    /**
     * Test plugin loading and validation
     */
    fun testPluginValidation(plugin: Plugin): PluginTestResult {
<span class="fc" id="L75">        val errors = mutableListOf&lt;String&gt;()</span>
<span class="fc" id="L76">        val warnings = mutableListOf&lt;String&gt;()</span>
        
<span class="fc" id="L78">        try {</span>
            // Test metadata
<span class="pc bpc" id="L80" title="1 of 2 branches missed.">            if (plugin.metadata.id.isBlank()) {</span>
<span class="nc" id="L81">                errors.add(&quot;Plugin ID cannot be blank&quot;)</span>
            }
            
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">            if (plugin.metadata.name.isBlank()) {</span>
<span class="nc" id="L85">                errors.add(&quot;Plugin name cannot be blank&quot;)</span>
            }
            
<span class="pc bpc" id="L88" title="1 of 2 branches missed.">            if (plugin.metadata.version.isBlank()) {</span>
<span class="nc" id="L89">                errors.add(&quot;Plugin version cannot be blank&quot;)</span>
            }
            
            // Test initialization
<span class="fc" id="L93">            val context = createTestContext(plugin.metadata)</span>
<span class="fc" id="L94">            plugin.initialize(context)</span>
            
            // Test plugin-specific functionality
<span class="fc" id="L97">            when (plugin) {</span>
<span class="fc bfc" id="L98" title="All 2 branches covered.">                is CommandPlugin -&gt; {</span>
<span class="pc bpc" id="L99" title="1 of 2 branches missed.">                    if (plugin.commands.isEmpty()) {</span>
<span class="nc" id="L100">                        warnings.add(&quot;Command plugin has no commands&quot;)</span>
                    }
                    
<span class="fc" id="L103">                    plugin.commands.forEach { command -&gt;</span>
<span class="pc bpc" id="L104" title="1 of 2 branches missed.">                        if (command.name.isBlank()) {</span>
<span class="nc" id="L105">                            errors.add(&quot;Command name cannot be blank&quot;)</span>
                        }
<span class="pc bpc" id="L107" title="1 of 2 branches missed.">                        if (command.description.isBlank()) {</span>
<span class="nc" id="L108">                            warnings.add(&quot;Command '${command.name}' has no description&quot;)</span>
                        }
<span class="fc" id="L110">                    }</span>
                }
                
<span class="pc bpc" id="L113" title="1 of 2 branches missed.">                is AiServicePlugin -&gt; {</span>
<span class="fc" id="L114">                    try {</span>
<span class="fc" id="L115">                        val testConfig = AiServiceConfig(</span>
<span class="fc" id="L116">                            provider = plugin.supportedProvider,</span>
<span class="fc" id="L117">                            apiKey = &quot;test-key&quot;,</span>
<span class="fc" id="L118">                            model = &quot;test-model&quot;,</span>
<span class="fc" id="L119">                            baseUrl = &quot;https://test.example.com&quot;</span>
                        )
<span class="fc" id="L121">                        plugin.createAiService(testConfig)</span>
<span class="nc" id="L122">                    } catch (e: Exception) {</span>
<span class="nc" id="L123">                        errors.add(&quot;Failed to create AI service: ${e.message}&quot;)</span>
                    }
                }
            }
            
            // Test shutdown
<span class="fc" id="L129">            plugin.shutdown()</span>
            
<span class="nc" id="L131">        } catch (e: Exception) {</span>
<span class="nc" id="L132">            errors.add(&quot;Plugin test failed: ${e.message}&quot;)</span>
        }
        
<span class="fc" id="L135">        return PluginTestResult(</span>
<span class="fc" id="L136">            success = errors.isEmpty(),</span>
<span class="fc" id="L137">            errors = errors,</span>
<span class="fc" id="L138">            warnings = warnings</span>
        )
    }
    
    /**
     * Create test plugin metadata
     */
    private fun createTestPluginMetadata(): PluginMetadata {
<span class="fc" id="L146">        return PluginMetadata(</span>
<span class="fc" id="L147">            id = &quot;test-plugin&quot;,</span>
<span class="fc" id="L148">            name = &quot;Test Plugin&quot;,</span>
<span class="fc" id="L149">            version = &quot;1.0.0&quot;,</span>
<span class="fc" id="L150">            description = &quot;Test plugin for framework testing&quot;,</span>
<span class="fc" id="L151">            author = &quot;Test Framework&quot;,</span>
<span class="fc" id="L152">            mainClass = &quot;com.test.TestPlugin&quot;</span>
        )
    }
}

/**
 * Test plugin context implementation
 */
<span class="fc" id="L160">class TestPluginContext(</span>
<span class="fc" id="L161">    private val mockConfigManager: MockConfigManager,</span>
<span class="fc" id="L162">    private val mockHistoryManager: MockHistoryManager,</span>
<span class="fc" id="L163">    private val mockAiServiceFactory: MockAiServiceFactory,</span>
<span class="fc" id="L164">    override val logger: PluginLogger,</span>
<span class="fc" id="L165">    private val pluginMetadata: PluginMetadata</span>
) : PluginContext {

<span class="nc" id="L168">    override val configManager: ConfigManager get() = throw NotImplementedError(&quot;Use mock methods directly&quot;)</span>
<span class="nc" id="L169">    override val historyManager: HistoryManager get() = throw NotImplementedError(&quot;Use mock methods directly&quot;)</span>
<span class="nc" id="L170">    override val aiServiceFactory: AiServiceFactory get() = throw NotImplementedError(&quot;Use mock methods directly&quot;)</span>
    
<span class="fc" id="L172">    private val registeredCommands = mutableListOf&lt;PluginCommand&gt;()</span>
<span class="fc" id="L173">    private val registeredEventHandlers = mutableListOf&lt;PluginEventHandler&gt;()</span>
<span class="fc" id="L174">    private val sharedData = ConcurrentHashMap&lt;String, Any&gt;()</span>
    
    override fun registerCommand(command: Any) {
<span class="fc" id="L177">        val pluginCommand = command as PluginCommand</span>
<span class="fc" id="L178">        registeredCommands.add(pluginCommand)</span>
<span class="fc" id="L179">        logger.debug(&quot;Registered test command: ${pluginCommand.name}&quot;)</span>
<span class="fc" id="L180">    }</span>
    
    override fun registerEventHandler(handler: Any) {
<span class="nc" id="L183">        val eventHandler = handler as PluginEventHandler</span>
<span class="nc" id="L184">        registeredEventHandlers.add(eventHandler)</span>
<span class="nc" id="L185">        logger.debug(&quot;Registered test event handler for: ${eventHandler.eventTypes}&quot;)</span>
<span class="nc" id="L186">    }</span>
    
<span class="fc" id="L188">    override fun getSharedData(key: String): Any? = sharedData[key]</span>
    
    override fun setSharedData(key: String, value: Any) {
<span class="fc" id="L191">        sharedData[key] = value</span>
<span class="fc" id="L192">    }</span>
    
<span class="fc" id="L194">    override fun hasPermission(permission: PluginPermission): Boolean = true</span>
    
<span class="fc" id="L196">    fun getRegisteredCommands(): List&lt;PluginCommand&gt; = registeredCommands.toList()</span>
<span class="nc" id="L197">    fun getRegisteredEventHandlers(): List&lt;PluginEventHandler&gt; = registeredEventHandlers.toList()</span>
}

/**
 * Mock configuration manager for testing
 */
<span class="fc" id="L203">class MockConfigManager {</span>
<span class="fc" id="L204">    private val mockConfig = com.aicodingcli.config.AppConfig(</span>
<span class="fc" id="L205">        defaultProvider = AiProvider.OPENAI,</span>
<span class="fc" id="L206">        providers = mapOf(</span>
<span class="fc" id="L207">            AiProvider.OPENAI to AiServiceConfig(</span>
<span class="fc" id="L208">                provider = AiProvider.OPENAI,</span>
<span class="fc" id="L209">                apiKey = &quot;test-key&quot;,</span>
<span class="fc" id="L210">                model = &quot;gpt-3.5-turbo&quot;,</span>
<span class="fc" id="L211">                baseUrl = &quot;https://api.openai.com/v1&quot;</span>
            )
        )
    )

<span class="nc" id="L216">    suspend fun loadConfig() = mockConfig</span>
<span class="nc" id="L217">    suspend fun saveConfig(config: com.aicodingcli.config.AppConfig) {}</span>
<span class="nc" id="L218">    suspend fun getCurrentProviderConfig() = mockConfig.providers[AiProvider.OPENAI]!!</span>
}

/**
 * Mock history manager for testing
 */
<span class="fc" id="L224">class MockHistoryManager {</span>
    fun createConversation(
        title: String,
        provider: AiProvider,
        model: String
<span class="nc" id="L229">    ) = com.aicodingcli.history.ConversationSession(</span>
<span class="nc" id="L230">        title = title,</span>
<span class="nc" id="L231">        provider = provider,</span>
<span class="nc" id="L232">        model = model</span>
<span class="nc" id="L233">    )</span>
}

/**
 * Mock AI service factory for testing
 */
<span class="fc" id="L239">class MockAiServiceFactory {</span>
    fun createService(config: AiServiceConfig): AiService {
<span class="nc" id="L241">        return MockAiService(config.provider)</span>
    }
}

/**
 * Mock AI service for testing
 */
<span class="nc" id="L248">class MockAiService(</span>
<span class="nc" id="L249">    private val provider: AiProvider,</span>
<span class="nc" id="L250">    private val responses: List&lt;String&gt; = listOf(&quot;Mock response&quot;),</span>
<span class="nc" id="L251">    private val shouldFail: Boolean = false</span>
<span class="nc" id="L252">) : AiService {</span>
    
<span class="nc" id="L254">    override val config = AiServiceConfig(</span>
<span class="nc" id="L255">        provider = provider,</span>
<span class="nc" id="L256">        apiKey = &quot;test-key&quot;,</span>
<span class="nc" id="L257">        model = &quot;test-model&quot;,</span>
<span class="nc" id="L258">        baseUrl = &quot;https://test.example.com&quot;</span>
    )
    
    override suspend fun chat(request: AiRequest): AiResponse {
<span class="nc bnc" id="L262" title="All 2 branches missed.">        if (shouldFail) {</span>
<span class="nc" id="L263">            throw RuntimeException(&quot;Mock AI service failure&quot;)</span>
        }
        
<span class="nc" id="L266">        return AiResponse(</span>
<span class="nc bnc" id="L267" title="All 2 branches missed.">            content = responses.firstOrNull() ?: &quot;Mock response&quot;,</span>
<span class="nc" id="L268">            model = request.model,</span>
<span class="nc" id="L269">            usage = TokenUsage(10, 20, 30),</span>
<span class="nc" id="L270">            finishReason = FinishReason.STOP</span>
        )
    }
    
    override suspend fun streamChat(request: AiRequest): Flow&lt;AiStreamChunk&gt; {
<span class="nc bnc" id="L275" title="All 2 branches missed.">        if (shouldFail) {</span>
<span class="nc" id="L276">            throw RuntimeException(&quot;Mock AI service failure&quot;)</span>
        }
        
<span class="nc" id="L279">        return flowOf(</span>
<span class="nc" id="L280">            AiStreamChunk(&quot;Mock &quot;, null),</span>
<span class="nc" id="L281">            AiStreamChunk(&quot;streaming &quot;, null),</span>
<span class="nc" id="L282">            AiStreamChunk(&quot;response&quot;, FinishReason.STOP)</span>
        )
    }
    
<span class="nc bnc" id="L286" title="All 2 branches missed.">    override suspend fun testConnection(): Boolean = !shouldFail</span>
}

/**
 * Result of plugin testing
 */
<span class="fc" id="L292">data class PluginTestResult(</span>
<span class="fc" id="L293">    val success: Boolean,</span>
<span class="fc" id="L294">    val errors: List&lt;String&gt;,</span>
<span class="pc" id="L295">    val warnings: List&lt;String&gt;</span>
)
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>