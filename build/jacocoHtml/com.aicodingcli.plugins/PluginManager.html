<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_class">PluginManager</span></div><h1>PluginManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">987 of 1,198</td><td class="ctr2">17%</td><td class="bar">120 of 128</td><td class="ctr2">6%</td><td class="ctr1">70</td><td class="ctr2">83</td><td class="ctr1">124</td><td class="ctr2">160</td><td class="ctr1">7</td><td class="ctr2">19</td></tr></tfoot><tbody><tr><td id="a18"><a href="PluginManager.kt.html#L260" class="el_method">validatePlugin(String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="265" alt="265"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="53" alt="53"/></td><td class="ctr2" id="c10">16%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="47" alt="47"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="3" alt="3"/></td><td class="ctr2" id="e4">6%</td><td class="ctr1" id="f1">24</td><td class="ctr2" id="g0">26</td><td class="ctr1" id="h1">30</td><td class="ctr2" id="i1">40</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="PluginManager.kt.html#L39" class="el_method">loadPlugin(String, Continuation)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="87" height="10" title="233" alt="233"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h0">42</td><td class="ctr2" id="i0">42</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a11"><a href="PluginManager.kt.html#L326" class="el_method">readPluginMetadata(ClassLoader)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="211" alt="211"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="115" height="10" title="48" alt="48"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f0">25</td><td class="ctr2" id="g1">25</td><td class="ctr1" id="h3">15</td><td class="ctr2" id="i3">15</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a15"><a href="PluginManager.kt.html#L124" class="el_method">unloadPlugin(String, Continuation)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="76" alt="76"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="11" alt="11"/></td><td class="ctr2" id="c11">12%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="PluginManager.kt.html#L212" class="el_method">installPlugin(String, Continuation)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="70" alt="70"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a12"><a href="PluginManager.kt.html#L163" class="el_method">reloadPlugin(String, Continuation)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="49" alt="49"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h5">5</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a13"><a href="PluginManager.kt.html#L241" class="el_method">uninstallPlugin$lambda$4(String, File)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="27" alt="27"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a17"><a href="PluginManager.kt.html#L347" class="el_method">validateDependencies(List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="25" alt="25"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="PluginManager.kt.html#L232" class="el_method">uninstallPlugin(String, Continuation)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="26" alt="26"/></td><td class="ctr2" id="c9">59%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a6"><a href="PluginManager.kt.html#L355" class="el_method">isPluginAvailable(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="PluginManager.kt.html#L179" class="el_method">getLoadedPlugins()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="11" alt="11"/></td><td class="ctr2" id="c8">73%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="PluginManager.kt.html#L186" class="el_method">getPlugin(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c6">83%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a3"><a href="PluginManager.kt.html#L193" class="el_method">getPluginState(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="10" alt="10"/></td><td class="ctr2" id="c7">83%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a9"><a href="PluginManager.kt.html#L17" class="el_method">PluginManager(String, ConfigManager, HistoryManager, AiServiceFactory, boolean)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="54" alt="54"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i4">14</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a10"><a href="PluginManager.kt.html#L17" class="el_method">PluginManager(String, ConfigManager, HistoryManager, AiServiceFactory, boolean, int, DefaultConstructorMarker)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="20" alt="20"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a16"><a href="PluginManager.kt.html#L253" class="el_method">updatePlugin(String, Continuation)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a7"><a href="PluginManager.kt.html#L28" class="el_method">json$lambda$0(JsonBuilder)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a4"><a href="PluginManager.kt.html#L199" class="el_method">getRegistry()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a0"><a href="PluginManager.kt.html#L204" class="el_method">getEventDispatcher()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>