<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CommandPlugin.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">CommandPlugin.kt</span></div><h1>CommandPlugin.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

/**
 * Plugin that provides CLI commands
 */
interface CommandPlugin : Plugin {
    /**
     * List of commands provided by this plugin
     */
    val commands: List&lt;PluginCommand&gt;
    
    /**
     * Get a specific command by name
     */
    fun getCommand(name: String): PluginCommand? {
<span class="fc bfc" id="L16" title="All 4 branches covered.">        return commands.find { it.name == name }</span>
    }
    
    /**
     * Check if this plugin provides a specific command
     */
    fun hasCommand(name: String): Boolean {
<span class="fc" id="L23">        return commands.any { it.name == name }</span>
    }
}

/**
 * Base implementation for command plugins
 */
<span class="fc" id="L30">abstract class BaseCommandPlugin : CommandPlugin {</span>
    private var isInitialized = false
    private lateinit var pluginContext: PluginContext
    
    override fun initialize(context: PluginContext) {
<span class="pc bpc" id="L35" title="1 of 2 branches missed.">        if (isInitialized) {</span>
<span class="nc" id="L36">            throw IllegalStateException(&quot;Plugin ${metadata.id} is already initialized&quot;)</span>
        }
        
<span class="fc" id="L39">        this.pluginContext = context</span>
        
        // Register all commands
<span class="fc" id="L42">        commands.forEach { command -&gt;</span>
<span class="fc" id="L43">            context.registerCommand(command)</span>
<span class="fc" id="L44">        }</span>
        
        // Perform plugin-specific initialization
<span class="fc" id="L47">        onInitialize(context)</span>
        
<span class="fc" id="L49">        isInitialized = true</span>
<span class="fc" id="L50">        context.logger.info(&quot;Command plugin ${metadata.name} initialized with ${commands.size} commands&quot;)</span>
<span class="fc" id="L51">    }</span>
    
    override fun shutdown() {
<span class="pc bpc" id="L54" title="1 of 2 branches missed.">        if (!isInitialized) {</span>
<span class="nc" id="L55">            return</span>
        }
        
        // Perform plugin-specific cleanup
<span class="fc" id="L59">        onShutdown()</span>
        
<span class="fc" id="L61">        isInitialized = false</span>
<span class="fc" id="L62">        pluginContext.logger.info(&quot;Command plugin ${metadata.name} shut down&quot;)</span>
<span class="fc" id="L63">    }</span>
    
    /**
     * Get the plugin context (only available after initialization)
     */
    protected fun getContext(): PluginContext {
<span class="nc bnc" id="L69" title="All 2 branches missed.">        if (!isInitialized) {</span>
<span class="nc" id="L70">            throw IllegalStateException(&quot;Plugin ${metadata.id} is not initialized&quot;)</span>
        }
<span class="nc" id="L72">        return pluginContext</span>
    }
    
    /**
     * Check if the plugin is initialized
     */
<span class="nc" id="L78">    protected fun isInitialized(): Boolean = isInitialized</span>
    
    /**
     * Called when the plugin is being initialized
     * Override this method to perform plugin-specific initialization
     */
    protected open fun onInitialize(context: PluginContext) {
        // Default implementation does nothing
<span class="fc" id="L86">    }</span>
    
    /**
     * Called when the plugin is being shut down
     * Override this method to perform plugin-specific cleanup
     */
    protected open fun onShutdown() {
        // Default implementation does nothing
<span class="fc" id="L94">    }</span>
    
    /**
     * Helper method to create a command with common error handling
     */
<span class="fc" id="L99">    protected fun createCommand(</span>
        name: String,
        description: String,
        usage: String,
<span class="fc" id="L103">        options: List&lt;CommandOption&gt; = emptyList(),</span>
        handler: suspend (args: CommandArgs, context: PluginContext) -&gt; CommandResult
    ): PluginCommand {
<span class="fc" id="L106">        return PluginCommand(</span>
<span class="fc" id="L107">            name = name,</span>
<span class="fc" id="L108">            description = description,</span>
<span class="fc" id="L109">            usage = usage,</span>
<span class="fc" id="L110">            options = options,</span>
<span class="fc" id="L111">            handler = { args, context -&gt;</span>
<span class="fc" id="L112">                try {</span>
<span class="fc" id="L113">                    handler(args, context)</span>
<span class="nc" id="L114">                } catch (e: Exception) {</span>
<span class="nc" id="L115">                    context.logger.error(&quot;Error executing command '$name'&quot;, e)</span>
<span class="pc" id="L116">                    CommandResult.error(&quot;Command execution failed: ${e.message}&quot;, e)</span>
<span class="fc" id="L117">                }</span>
            }
        )
    }
    
    /**
     * Helper method to validate required arguments
     */
    protected fun validateArgs(args: CommandArgs, minArgs: Int): CommandResult? {
<span class="nc bnc" id="L126" title="All 2 branches missed.">        if (args.args.size &lt; minArgs) {</span>
<span class="nc" id="L127">            return CommandResult.failure(&quot;Insufficient arguments. Expected at least $minArgs, got ${args.args.size}&quot;)</span>
        }
<span class="nc" id="L129">        return null</span>
    }
    
    /**
     * Helper method to validate required options
     */
    protected fun validateOptions(args: CommandArgs, requiredOptions: List&lt;String&gt;): CommandResult? {
<span class="nc bnc" id="L136" title="All 2 branches missed.">        val missingOptions = requiredOptions.filter { !args.hasOption(it) }</span>
<span class="nc bnc" id="L137" title="All 4 branches missed.">        if (missingOptions.isNotEmpty()) {</span>
<span class="nc" id="L138">            return CommandResult.failure(&quot;Missing required options: ${missingOptions.joinToString(&quot;, &quot;)}&quot;)</span>
        }
<span class="nc" id="L140">        return null</span>
    }
}

/**
 * Simple command plugin implementation for single commands
 */
<span class="nc" id="L147">abstract class SingleCommandPlugin : BaseCommandPlugin() {</span>
    /**
     * The single command provided by this plugin
     */
    abstract val command: PluginCommand
    
    override val commands: List&lt;PluginCommand&gt;
<span class="nc" id="L154">        get() = listOf(command)</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>