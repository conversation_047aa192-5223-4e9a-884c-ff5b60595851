<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginRegistry.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">PluginRegistry.kt</span></div><h1>PluginRegistry.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

import com.aicodingcli.ai.AiProvider
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import java.util.concurrent.ConcurrentHashMap

/**
 * Registry for managing loaded plugins and their capabilities
 */
<span class="fc" id="L12">class PluginRegistry {</span>
<span class="fc" id="L13">    private val plugins = ConcurrentHashMap&lt;String, Plugin&gt;()</span>
<span class="fc" id="L14">    private val pluginContexts = ConcurrentHashMap&lt;String, DefaultPluginContext&gt;()</span>
<span class="fc" id="L15">    private val commandPlugins = ConcurrentHashMap&lt;String, CommandPlugin&gt;()</span>
<span class="fc" id="L16">    private val aiServicePlugins = ConcurrentHashMap&lt;AiProvider, AiServicePlugin&gt;()</span>
<span class="fc" id="L17">    private val commandMappings = ConcurrentHashMap&lt;String, String&gt;() // command name -&gt; plugin id</span>
    
    /**
     * Register a plugin with the registry
     */
    fun registerPlugin(plugin: Plugin, context: DefaultPluginContext) {
<span class="nc" id="L23">        val pluginId = plugin.metadata.id</span>
        
        // Store plugin and context
<span class="nc" id="L26">        plugins[pluginId] = plugin</span>
<span class="nc" id="L27">        pluginContexts[pluginId] = context</span>
        
        // Register specific plugin types
<span class="nc" id="L30">        when (plugin) {</span>
<span class="nc bnc" id="L31" title="All 2 branches missed.">            is CommandPlugin -&gt; registerCommandPlugin(plugin)</span>
<span class="nc bnc" id="L32" title="All 2 branches missed.">            is AiServicePlugin -&gt; registerAiServicePlugin(plugin)</span>
        }
<span class="nc" id="L34">    }</span>
    
    /**
     * Unregister a plugin from the registry
     */
    fun unregisterPlugin(pluginId: String) {
<span class="nc bnc" id="L40" title="All 2 branches missed.">        val plugin = plugins[pluginId] ?: return</span>
        
        // Unregister specific plugin types
<span class="nc" id="L43">        when (plugin) {</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">            is CommandPlugin -&gt; unregisterCommandPlugin(plugin)</span>
<span class="nc bnc" id="L45" title="All 2 branches missed.">            is AiServicePlugin -&gt; unregisterAiServicePlugin(plugin)</span>
        }
        
        // Remove from main registry
<span class="nc" id="L49">        plugins.remove(pluginId)</span>
<span class="nc" id="L50">        pluginContexts.remove(pluginId)</span>
<span class="nc" id="L51">    }</span>
    
    /**
     * Register a command plugin
     */
    private fun registerCommandPlugin(plugin: CommandPlugin) {
<span class="nc" id="L57">        val pluginId = plugin.metadata.id</span>
<span class="nc" id="L58">        commandPlugins[pluginId] = plugin</span>
        
        // Register command mappings
<span class="nc" id="L61">        plugin.commands.forEach { command -&gt;</span>
<span class="nc bnc" id="L62" title="All 2 branches missed.">            if (commandMappings.containsKey(command.name)) {</span>
<span class="nc" id="L63">                val existingPluginId = commandMappings[command.name]</span>
<span class="nc" id="L64">                throw IllegalStateException(&quot;Command '${command.name}' is already registered by plugin $existingPluginId&quot;)</span>
            }
<span class="nc" id="L66">            commandMappings[command.name] = pluginId</span>
<span class="nc" id="L67">        }</span>
<span class="nc" id="L68">    }</span>
    
    /**
     * Unregister a command plugin
     */
    private fun unregisterCommandPlugin(plugin: CommandPlugin) {
<span class="nc" id="L74">        val pluginId = plugin.metadata.id</span>
<span class="nc" id="L75">        commandPlugins.remove(pluginId)</span>
        
        // Remove command mappings
<span class="nc" id="L78">        plugin.commands.forEach { command -&gt;</span>
<span class="nc" id="L79">            commandMappings.remove(command.name)</span>
<span class="nc" id="L80">        }</span>
<span class="nc" id="L81">    }</span>
    
    /**
     * Register an AI service plugin
     */
    private fun registerAiServicePlugin(plugin: AiServicePlugin) {
<span class="nc" id="L87">        val provider = plugin.supportedProvider</span>
        
<span class="nc bnc" id="L89" title="All 2 branches missed.">        if (aiServicePlugins.containsKey(provider)) {</span>
<span class="nc" id="L90">            val existingPlugin = aiServicePlugins[provider]</span>
<span class="nc bnc" id="L91" title="All 4 branches missed.">            throw IllegalStateException(&quot;AI provider $provider is already registered by plugin ${existingPlugin?.metadata?.id}&quot;)</span>
        }
        
<span class="nc" id="L94">        aiServicePlugins[provider] = plugin</span>
<span class="nc" id="L95">        AiServicePluginRegistry.register(plugin)</span>
<span class="nc" id="L96">    }</span>
    
    /**
     * Unregister an AI service plugin
     */
    private fun unregisterAiServicePlugin(plugin: AiServicePlugin) {
<span class="nc" id="L102">        aiServicePlugins.remove(plugin.supportedProvider)</span>
<span class="nc" id="L103">        AiServicePluginRegistry.unregister(plugin.supportedProvider)</span>
<span class="nc" id="L104">    }</span>
    
    /**
     * Get a plugin by ID
     */
    fun getPlugin(pluginId: String): Plugin? {
<span class="fc" id="L110">        return plugins[pluginId]</span>
    }
    
    /**
     * Get all registered plugins
     */
    fun getAllPlugins(): Map&lt;String, Plugin&gt; {
<span class="fc" id="L117">        return plugins.toMap()</span>
    }
    
    /**
     * Get a command plugin that provides a specific command
     */
    fun getCommandPlugin(commandName: String): CommandPlugin? {
<span class="pc bpc" id="L124" title="1 of 2 branches missed.">        val pluginId = commandMappings[commandName] ?: return null</span>
<span class="nc" id="L125">        return commandPlugins[pluginId]</span>
    }
    
    /**
     * Get a specific command by name
     */
    fun getCommand(commandName: String): PluginCommand? {
<span class="pc bpc" id="L132" title="1 of 2 branches missed.">        val plugin = getCommandPlugin(commandName) ?: return null</span>
<span class="nc" id="L133">        return plugin.getCommand(commandName)</span>
    }
    
    /**
     * Get all available commands from all plugins
     */
    fun getAllCommands(): Map&lt;String, PluginCommand&gt; {
<span class="fc" id="L140">        val commands = mutableMapOf&lt;String, PluginCommand&gt;()</span>
        
<span class="fc" id="L142">        commandPlugins.values.forEach { plugin -&gt;</span>
<span class="nc" id="L143">            plugin.commands.forEach { command -&gt;</span>
<span class="nc" id="L144">                commands[command.name] = command</span>
<span class="nc" id="L145">            }</span>
<span class="nc" id="L146">        }</span>
        
<span class="fc" id="L148">        return commands</span>
    }
    
    /**
     * Get all command plugins
     */
    fun getCommandPlugins(): Map&lt;String, CommandPlugin&gt; {
<span class="fc" id="L155">        return commandPlugins.toMap()</span>
    }
    
    /**
     * Get an AI service plugin for a specific provider
     */
    fun getAiServicePlugin(provider: AiProvider): AiServicePlugin? {
<span class="nc" id="L162">        return aiServicePlugins[provider]</span>
    }
    
    /**
     * Get all AI service plugins
     */
    fun getAiServicePlugins(): Map&lt;AiProvider, AiServicePlugin&gt; {
<span class="fc" id="L169">        return aiServicePlugins.toMap()</span>
    }
    
    /**
     * Check if a command is available
     */
    fun hasCommand(commandName: String): Boolean {
<span class="fc" id="L176">        return commandMappings.containsKey(commandName)</span>
    }
    
    /**
     * Check if an AI provider is supported
     */
    fun hasAiProvider(provider: AiProvider): Boolean {
<span class="nc" id="L183">        return aiServicePlugins.containsKey(provider)</span>
    }
    
    /**
     * Get plugin context for a specific plugin
     */
    fun getPluginContext(pluginId: String): DefaultPluginContext? {
<span class="nc" id="L190">        return pluginContexts[pluginId]</span>
    }
    
    /**
     * Get plugins by type
     */
    fun &lt;T : Plugin&gt; getPluginsByType(clazz: Class&lt;T&gt;): List&lt;T&gt; {
<span class="nc" id="L197">        return plugins.values.filterIsInstance(clazz)</span>
    }
    
    /**
     * Get plugin statistics
     */
    fun getStatistics(): PluginRegistryStatistics {
<span class="fc" id="L204">        return PluginRegistryStatistics(</span>
<span class="fc" id="L205">            totalPlugins = plugins.size,</span>
<span class="fc" id="L206">            commandPlugins = commandPlugins.size,</span>
<span class="fc" id="L207">            aiServicePlugins = aiServicePlugins.size,</span>
<span class="fc" id="L208">            totalCommands = commandMappings.size,</span>
<span class="fc" id="L209">            supportedAiProviders = aiServicePlugins.keys.toList()</span>
        )
    }
    
    /**
     * Clear all registrations (used for testing or shutdown)
     */
    fun clear() {
<span class="fc" id="L217">        plugins.clear()</span>
<span class="fc" id="L218">        pluginContexts.clear()</span>
<span class="fc" id="L219">        commandPlugins.clear()</span>
<span class="fc" id="L220">        aiServicePlugins.clear()</span>
<span class="fc" id="L221">        commandMappings.clear()</span>
<span class="fc" id="L222">    }</span>
}

/**
 * Statistics about the plugin registry
 */
<span class="fc" id="L228">data class PluginRegistryStatistics(</span>
<span class="fc" id="L229">    val totalPlugins: Int,</span>
<span class="fc" id="L230">    val commandPlugins: Int,</span>
<span class="fc" id="L231">    val aiServicePlugins: Int,</span>
<span class="fc" id="L232">    val totalCommands: Int,</span>
<span class="fc" id="L233">    val supportedAiProviders: List&lt;AiProvider&gt;</span>
)

/**
 * Plugin discovery service for finding available plugins
 */
<span class="fc" id="L239">class PluginDiscoveryService(private val pluginDir: String) {</span>
    
    /**
     * Discover all plugin files in the plugin directory
     */
    fun discoverPlugins(): List&lt;PluginInfo&gt; {
<span class="fc" id="L245">        val pluginDirectory = java.io.File(pluginDir)</span>
<span class="pc bpc" id="L246" title="1 of 4 branches missed.">        if (!pluginDirectory.exists() || !pluginDirectory.isDirectory) {</span>
<span class="fc" id="L247">            return emptyList()</span>
        }
        
<span class="fc" id="L250">        return pluginDirectory.listFiles { file -&gt;</span>
<span class="nc bnc" id="L251" title="All 4 branches missed.">            file.isFile &amp;&amp; file.name.endsWith(&quot;.jar&quot;)</span>
<span class="pc bpc" id="L252" title="1 of 2 branches missed.">        }?.mapNotNull { file -&gt;</span>
<span class="nc" id="L253">            try {</span>
<span class="nc" id="L254">                val classLoader = java.net.URLClassLoader(arrayOf(file.toURI().toURL()))</span>
<span class="nc" id="L255">                val metadataStream = classLoader.getResourceAsStream(&quot;plugin.json&quot;)</span>
                
<span class="nc bnc" id="L257" title="All 2 branches missed.">                if (metadataStream != null) {</span>
<span class="nc" id="L258">                    val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }</span>
<span class="nc bnc" id="L259" title="All 2 branches missed.">                    val jsonText = metadataStream.bufferedReader().use { it.readText() }</span>
<span class="nc" id="L260">                    val jsonElement = Json.parseToJsonElement(jsonText).jsonObject</span>
<span class="nc" id="L261">                    val metadata = PluginMetadata(</span>
<span class="nc bnc" id="L262" title="All 6 branches missed.">                        id = jsonElement[&quot;id&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L263" title="All 6 branches missed.">                        name = jsonElement[&quot;name&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L264" title="All 6 branches missed.">                        version = jsonElement[&quot;version&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L265" title="All 6 branches missed.">                        description = jsonElement[&quot;description&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L266" title="All 6 branches missed.">                        author = jsonElement[&quot;author&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L267" title="All 6 branches missed.">                        mainClass = jsonElement[&quot;mainClass&quot;]?.jsonPrimitive?.content ?: &quot;&quot;</span>
                    )
                    
<span class="nc" id="L270">                    PluginInfo(</span>
<span class="nc" id="L271">                        metadata = metadata,</span>
<span class="nc" id="L272">                        filePath = file.absolutePath,</span>
<span class="nc" id="L273">                        fileSize = file.length(),</span>
<span class="nc" id="L274">                        lastModified = file.lastModified()</span>
                    )
                } else {
<span class="nc" id="L277">                    null</span>
                }
<span class="nc" id="L279">            } catch (e: Exception) {</span>
<span class="nc" id="L280">                null // Skip invalid plugins</span>
<span class="nc" id="L281">            }</span>
<span class="nc" id="L282">        } ?: emptyList()</span>
    }
    
    /**
     * Get plugin info for a specific file
     */
    fun getPluginInfo(pluginPath: String): PluginInfo? {
<span class="fc" id="L289">        return try {</span>
<span class="fc" id="L290">            val file = java.io.File(pluginPath)</span>
<span class="fc" id="L291">            val classLoader = java.net.URLClassLoader(arrayOf(file.toURI().toURL()))</span>
<span class="fc" id="L292">            val metadataStream = classLoader.getResourceAsStream(&quot;plugin.json&quot;)</span>
            
<span class="pc bpc" id="L294" title="1 of 2 branches missed.">            if (metadataStream != null) {</span>
<span class="nc" id="L295">                val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }</span>
<span class="nc bnc" id="L296" title="All 2 branches missed.">                val jsonText = metadataStream.bufferedReader().use { it.readText() }</span>
<span class="nc" id="L297">                val jsonElement = Json.parseToJsonElement(jsonText).jsonObject</span>
<span class="nc" id="L298">                val metadata = PluginMetadata(</span>
<span class="nc bnc" id="L299" title="All 6 branches missed.">                    id = jsonElement[&quot;id&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L300" title="All 6 branches missed.">                    name = jsonElement[&quot;name&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L301" title="All 6 branches missed.">                    version = jsonElement[&quot;version&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L302" title="All 6 branches missed.">                    description = jsonElement[&quot;description&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L303" title="All 6 branches missed.">                    author = jsonElement[&quot;author&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L304" title="All 6 branches missed.">                    mainClass = jsonElement[&quot;mainClass&quot;]?.jsonPrimitive?.content ?: &quot;&quot;</span>
                )
                
<span class="nc" id="L307">                PluginInfo(</span>
<span class="nc" id="L308">                    metadata = metadata,</span>
<span class="nc" id="L309">                    filePath = file.absolutePath,</span>
<span class="nc" id="L310">                    fileSize = file.length(),</span>
<span class="nc" id="L311">                    lastModified = file.lastModified()</span>
                )
            } else {
<span class="fc" id="L314">                null</span>
            }
<span class="nc" id="L316">        } catch (e: Exception) {</span>
<span class="pc" id="L317">            null</span>
        }
    }
}

/**
 * Information about a discovered plugin
 */
<span class="fc" id="L325">data class PluginInfo(</span>
<span class="fc" id="L326">    val metadata: PluginMetadata,</span>
<span class="fc" id="L327">    val filePath: String,</span>
<span class="fc" id="L328">    val fileSize: Long,</span>
<span class="fc" id="L329">    val lastModified: Long</span>
)
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>