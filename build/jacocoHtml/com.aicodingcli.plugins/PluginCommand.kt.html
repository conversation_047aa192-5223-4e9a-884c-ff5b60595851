<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginCommand.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">PluginCommand.kt</span></div><h1>PluginCommand.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

/**
 * Command provided by a plugin
 */
<span class="pc" id="L6">data class PluginCommand(</span>
    /**
     * Name of the command
     */
<span class="fc" id="L10">    val name: String,</span>
    
    /**
     * Description of what the command does
     */
<span class="fc" id="L15">    val description: String,</span>
    
    /**
     * Usage string showing how to use the command
     */
<span class="pc" id="L20">    val usage: String,</span>
    
    /**
     * List of command options/flags
     */
<span class="pc" id="L25">    val options: List&lt;CommandOption&gt; = emptyList(),</span>
    
    /**
     * Handler function that executes the command
     */
<span class="fc" id="L30">    val handler: suspend (args: CommandArgs, context: PluginContext) -&gt; CommandResult</span>
<span class="nc" id="L31">)</span>

/**
 * Command option/flag definition
 */
<span class="fc" id="L36">data class CommandOption(</span>
    /**
     * Long name of the option (e.g., &quot;output&quot;)
     */
<span class="fc" id="L40">    val name: String,</span>
    
    /**
     * Short name of the option (e.g., &quot;o&quot;)
     */
<span class="fc" id="L45">    val shortName: String?,</span>
    
    /**
     * Description of what the option does
     */
<span class="fc" id="L50">    val description: String,</span>
    
    /**
     * Whether this option is required
     */
<span class="pc" id="L55">    val required: Boolean = false,</span>
    
    /**
     * Whether this option takes a value
     */
<span class="pc" id="L60">    val hasValue: Boolean = true,</span>
    
    /**
     * Default value for the option
     */
<span class="fc" id="L65">    val defaultValue: String? = null</span>
<span class="fc" id="L66">)</span>

/**
 * Command arguments passed to plugin command handlers
 */
<span class="fc" id="L71">data class CommandArgs(</span>
    /**
     * Positional arguments
     */
<span class="fc" id="L75">    val args: List&lt;String&gt;,</span>
    
    /**
     * Named options/flags
     */
<span class="pc" id="L80">    val options: Map&lt;String, String?&gt;,</span>
    
    /**
     * Raw command line arguments
     */
<span class="pc" id="L85">    val rawArgs: Array&lt;String&gt;</span>
) {
    /**
     * Get a positional argument by index
     */
<span class="fc" id="L90">    fun getArg(index: Int): String? = args.getOrNull(index)</span>
    
    /**
     * Get an option value by name
     */
<span class="fc" id="L95">    fun getOption(name: String): String? = options[name]</span>
    
    /**
     * Check if an option is present (for flags without values)
     */
<span class="fc" id="L100">    fun hasOption(name: String): Boolean = options.containsKey(name)</span>
    
    /**
     * Get an option value with a default
     */
<span class="fc bfc" id="L105" title="All 2 branches covered.">    fun getOptionOrDefault(name: String, default: String): String = options[name] ?: default</span>
}

/**
 * Result of executing a plugin command
 */
<span class="pc" id="L111">data class CommandResult(</span>
    /**
     * Whether the command executed successfully
     */
<span class="fc" id="L115">    val success: Boolean,</span>
    
    /**
     * Message to display to the user
     */
<span class="fc" id="L120">    val message: String?,</span>
    
    /**
     * Additional data returned by the command
     */
<span class="pc" id="L125">    val data: Any? = null,</span>
    
    /**
     * Exit code for the command
     */
<span class="pc bnc" id="L130" title="All 2 branches missed.">    val exitCode: Int = if (success) 0 else 1</span>
<span class="nc" id="L131">) {</span>
    companion object {
        /**
         * Create a successful result
         */
<span class="pc" id="L136">        fun success(message: String? = null, data: Any? = null): CommandResult {</span>
<span class="fc" id="L137">            return CommandResult(true, message, data, 0)</span>
        }
        
        /**
         * Create a failure result
         */
<span class="fc" id="L143">        fun failure(message: String, exitCode: Int = 1): CommandResult {</span>
<span class="fc" id="L144">            return CommandResult(false, message, null, exitCode)</span>
        }
        
        /**
         * Create an error result
         */
<span class="fc" id="L150">        fun error(message: String, throwable: Throwable? = null): CommandResult {</span>
<span class="fc bfc" id="L151" title="All 2 branches covered.">            val errorMessage = if (throwable != null) {</span>
<span class="fc" id="L152">                &quot;$message: ${throwable.message}&quot;</span>
            } else {
<span class="fc" id="L154">                message</span>
            }
<span class="fc" id="L156">            return CommandResult(false, errorMessage, throwable, 1)</span>
        }
    }
}

/**
 * Plugin event handler interface
 */
interface PluginEventHandler {
    /**
     * Handle a plugin event
     */
    suspend fun handleEvent(event: PluginEvent)
    
    /**
     * Get the types of events this handler is interested in
     */
    val eventTypes: Set&lt;PluginEventType&gt;
}

/**
 * Plugin event types
 */
enum class PluginEventType {
<span class="fc" id="L180">    PLUGIN_LOADED,</span>
<span class="fc" id="L181">    PLUGIN_UNLOADED,</span>
<span class="fc" id="L182">    COMMAND_EXECUTED,</span>
<span class="fc" id="L183">    CONFIG_CHANGED,</span>
<span class="fc" id="L184">    CONVERSATION_STARTED,</span>
<span class="fc" id="L185">    CONVERSATION_ENDED,</span>
<span class="fc" id="L186">    AI_REQUEST_SENT,</span>
<span class="fc" id="L187">    AI_RESPONSE_RECEIVED</span>
}

/**
 * Plugin event data
 */
<span class="fc" id="L193">data class PluginEvent(</span>
<span class="fc" id="L194">    val type: PluginEventType,</span>
<span class="pc" id="L195">    val source: String,</span>
<span class="pc" id="L196">    val data: Map&lt;String, Any&gt; = emptyMap(),</span>
<span class="pc" id="L197">    val timestamp: Long = System.currentTimeMillis()</span>
<span class="fc" id="L198">)</span>
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>