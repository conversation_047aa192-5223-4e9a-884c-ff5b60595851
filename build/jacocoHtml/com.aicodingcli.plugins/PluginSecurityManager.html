<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginSecurityManager</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_class">PluginSecurityManager</span></div><h1>PluginSecurityManager</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">120 of 301</td><td class="ctr2">60%</td><td class="bar">23 of 30</td><td class="ctr2">23%</td><td class="ctr1">17</td><td class="ctr2">30</td><td class="ctr1">17</td><td class="ctr2">53</td><td class="ctr1">4</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a11"><a href="PluginSecurity.kt.html#L80" class="el_method">isPermissionCompatible(PluginPermission, PluginPermission)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="63" alt="63"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="51" alt="51"/></td><td class="ctr2" id="c10">44%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">29%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">13</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i0">21</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="PluginSecurity.kt.html#L59" class="el_method">checkFileAccess(String, String, boolean)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="16" alt="16"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="PluginSecurity.kt.html#L67" class="el_method">checkNetworkAccess(String, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="15" alt="15"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a0"><a href="PluginSecurity.kt.html#L75" class="el_method">checkCommandExecution(String, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="15" alt="15"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="PluginSecurity.kt.html#L58" class="el_method">checkFileAccess$default(PluginSecurityManager, String, String, boolean, int, Object)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="11" alt="11"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="PluginSecurity.kt.html#L28" class="el_method">createSandbox(Plugin)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="43" alt="43"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i1">9</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a14"><a href="PluginSecurity.kt.html#L18" class="el_method">validatePermissions(Plugin, PluginOperation)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="15" alt="15"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i2">3</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a7"><a href="PluginSecurity.kt.html#L110" class="el_method">extractAllowedPaths(List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="12" alt="12"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="PluginSecurity.kt.html#L115" class="el_method">extractAllowedNetworkHosts(List)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="12" alt="12"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="PluginSecurity.kt.html#L120" class="el_method">extractAllowedCommands(List)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="12" alt="12"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a12"><a href="PluginSecurity.kt.html#L11" class="el_method">PluginSecurityManager()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="8" alt="8"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a9"><a href="PluginSecurity.kt.html#L125" class="el_method">hasConfigPermission(List)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="8" alt="8"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a10"><a href="PluginSecurity.kt.html#L129" class="el_method">hasHistoryPermission(List)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="8" alt="8"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a13"><a href="PluginSecurity.kt.html#L45" class="el_method">removeSandbox(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a8"><a href="PluginSecurity.kt.html#L52" class="el_method">getSandbox(String)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>