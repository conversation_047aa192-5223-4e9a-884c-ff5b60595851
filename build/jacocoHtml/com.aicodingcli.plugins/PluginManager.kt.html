<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginManager.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">PluginManager.kt</span></div><h1>PluginManager.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

import com.aicodingcli.ai.AiServiceFactory
import com.aicodingcli.config.ConfigManager
import com.aicodingcli.history.HistoryManager
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import java.io.File
import java.net.URLClassLoader
import java.util.concurrent.ConcurrentHashMap
import java.util.jar.JarFile

/**
 * Plugin manager responsible for loading, managing, and unloading plugins
 */
<span class="fc" id="L17">class PluginManager(</span>
<span class="fc" id="L18">    private val pluginDir: String = System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli/plugins&quot;,</span>
<span class="fc" id="L19">    private val configManager: ConfigManager,</span>
<span class="fc" id="L20">    private val historyManager: HistoryManager,</span>
<span class="fc" id="L21">    private val aiServiceFactory: AiServiceFactory,</span>
<span class="fc" id="L22">    private val enableDebugLogging: Boolean = false</span>
<span class="fc" id="L23">) {</span>
<span class="fc" id="L24">    private val loadedPlugins = ConcurrentHashMap&lt;String, LoadedPlugin&gt;()</span>
<span class="fc" id="L25">    private val pluginClassLoaders = ConcurrentHashMap&lt;String, ClassLoader&gt;()</span>
<span class="fc" id="L26">    private val pluginRegistry = PluginRegistry()</span>
<span class="fc" id="L27">    private val eventDispatcher = PluginEventDispatcher()</span>
<span class="fc" id="L28">    private val json = Json { ignoreUnknownKeys = true }</span>
    
<span class="fc" id="L30">    init {</span>
        // Ensure plugin directory exists
<span class="fc" id="L32">        File(pluginDir).mkdirs()</span>
<span class="fc" id="L33">    }</span>
    
    /**
     * Load a plugin from a JAR file
     */
    suspend fun loadPlugin(pluginPath: String): Plugin {
<span class="nc" id="L39">        val pluginFile = File(pluginPath)</span>
<span class="nc bnc" id="L40" title="All 2 branches missed.">        if (!pluginFile.exists()) {</span>
<span class="nc" id="L41">            throw PluginLoadException(&quot;Plugin file not found: $pluginPath&quot;)</span>
        }
        
        // Validate plugin before loading
<span class="nc" id="L45">        val validationResult = validatePlugin(pluginPath)</span>
<span class="nc bnc" id="L46" title="All 2 branches missed.">        if (!validationResult.isValid) {</span>
<span class="nc" id="L47">            throw PluginLoadException(&quot;Plugin validation failed: ${validationResult.errors.joinToString(&quot;, &quot;)}&quot;)</span>
        }
        
<span class="nc" id="L50">        try {</span>
            // Create class loader for the plugin
<span class="nc" id="L52">            val classLoader = URLClassLoader(arrayOf(pluginFile.toURI().toURL()))</span>
            
            // Read plugin metadata
<span class="nc" id="L55">            val metadata = readPluginMetadata(classLoader)</span>
            
            // Check if plugin is already loaded
<span class="nc bnc" id="L58" title="All 2 branches missed.">            if (loadedPlugins.containsKey(metadata.id)) {</span>
<span class="nc" id="L59">                throw PluginLoadException(&quot;Plugin ${metadata.id} is already loaded&quot;)</span>
            }
            
            // Validate dependencies
<span class="nc" id="L63">            validateDependencies(metadata.dependencies)</span>
            
            // Load plugin main class
<span class="nc" id="L66">            val pluginClass = classLoader.loadClass(metadata.mainClass)</span>
<span class="nc" id="L67">            val plugin = pluginClass.getDeclaredConstructor().newInstance() as Plugin</span>
            
            // Verify metadata matches
<span class="nc bnc" id="L70" title="All 2 branches missed.">            if (plugin.metadata.id != metadata.id) {</span>
<span class="nc" id="L71">                throw PluginLoadException(&quot;Plugin metadata mismatch: expected ${metadata.id}, got ${plugin.metadata.id}&quot;)</span>
            }
            
            // Create plugin context
<span class="nc" id="L75">            val context = PluginContextFactory.createContext(</span>
<span class="nc" id="L76">                pluginMetadata = metadata,</span>
<span class="nc" id="L77">                configManager = configManager,</span>
<span class="nc" id="L78">                historyManager = historyManager,</span>
<span class="nc" id="L79">                aiServiceFactory = aiServiceFactory,</span>
<span class="nc" id="L80">                enableDebugLogging = enableDebugLogging</span>
            )
            
            // Initialize plugin
<span class="nc" id="L84">            plugin.initialize(context)</span>
            
            // Store loaded plugin info
<span class="nc" id="L87">            val loadedPlugin = LoadedPlugin(</span>
<span class="nc" id="L88">                plugin = plugin,</span>
<span class="nc" id="L89">                context = context,</span>
<span class="nc" id="L90">                classLoader = classLoader,</span>
<span class="nc" id="L91">                filePath = pluginPath,</span>
<span class="nc" id="L92">                state = PluginState.RUNNING</span>
            )
            
<span class="nc" id="L95">            loadedPlugins[metadata.id] = loadedPlugin</span>
<span class="nc" id="L96">            pluginClassLoaders[metadata.id] = classLoader</span>
            
            // Register plugin with registry
<span class="nc" id="L99">            pluginRegistry.registerPlugin(plugin, context)</span>
            
            // Register event handlers
<span class="nc" id="L102">            context.getRegisteredEventHandlers().forEach { handler -&gt;</span>
<span class="nc" id="L103">                eventDispatcher.registerHandler(handler)</span>
<span class="nc" id="L104">            }</span>
            
            // Dispatch plugin loaded event
<span class="nc" id="L107">            eventDispatcher.dispatchEvent(PluginEvent(</span>
<span class="nc" id="L108">                type = PluginEventType.PLUGIN_LOADED,</span>
<span class="nc" id="L109">                source = metadata.id,</span>
<span class="nc" id="L110">                data = mapOf(&quot;plugin&quot; to plugin, &quot;metadata&quot; to metadata)</span>
            ))
            
<span class="nc" id="L113">            return plugin</span>
            
<span class="nc" id="L115">        } catch (e: Exception) {</span>
<span class="nc" id="L116">            throw PluginLoadException(&quot;Failed to load plugin from $pluginPath&quot;, e)</span>
        }
    }
    
    /**
     * Unload a plugin
     */
    suspend fun unloadPlugin(pluginId: String): Boolean {
<span class="pc bpc" id="L124" title="1 of 2 branches missed.">        val loadedPlugin = loadedPlugins[pluginId] ?: return false</span>
        
<span class="nc" id="L126">        try {</span>
            // Dispatch plugin unloading event
<span class="nc" id="L128">            eventDispatcher.dispatchEvent(PluginEvent(</span>
<span class="nc" id="L129">                type = PluginEventType.PLUGIN_UNLOADED,</span>
<span class="nc" id="L130">                source = pluginId,</span>
<span class="nc" id="L131">                data = mapOf(&quot;plugin&quot; to loadedPlugin.plugin)</span>
            ))
            
            // Unregister event handlers
<span class="nc" id="L135">            loadedPlugin.context.getRegisteredEventHandlers().forEach { handler -&gt;</span>
<span class="nc" id="L136">                eventDispatcher.unregisterHandler(handler)</span>
<span class="nc" id="L137">            }</span>
            
            // Unregister from registry
<span class="nc" id="L140">            pluginRegistry.unregisterPlugin(pluginId)</span>
            
            // Shutdown plugin
<span class="nc" id="L143">            loadedPlugin.plugin.shutdown()</span>
            
            // Clear context registrations
<span class="nc" id="L146">            loadedPlugin.context.clearRegistrations()</span>
            
            // Remove from loaded plugins
<span class="nc" id="L149">            loadedPlugins.remove(pluginId)</span>
<span class="nc" id="L150">            pluginClassLoaders.remove(pluginId)</span>
            
<span class="nc" id="L152">            return true</span>
            
<span class="nc" id="L154">        } catch (e: Exception) {</span>
<span class="nc" id="L155">            throw PluginExecutionException(&quot;Failed to unload plugin $pluginId&quot;, e)</span>
        }
    }
    
    /**
     * Reload a plugin
     */
    suspend fun reloadPlugin(pluginId: String): Plugin {
<span class="nc bnc" id="L163" title="All 2 branches missed.">        val loadedPlugin = loadedPlugins[pluginId]</span>
<span class="nc" id="L164">            ?: throw PluginLoadException(&quot;Plugin $pluginId is not loaded&quot;)</span>
        
<span class="nc" id="L166">        val pluginPath = loadedPlugin.filePath</span>
        
        // Unload current plugin
<span class="nc" id="L169">        unloadPlugin(pluginId)</span>
        
        // Load plugin again
<span class="nc" id="L172">        return loadPlugin(pluginPath)</span>
    }
    
    /**
     * Get all loaded plugins
     */
    fun getLoadedPlugins(): List&lt;Plugin&gt; {
<span class="pc" id="L179">        return loadedPlugins.values.map { it.plugin }</span>
    }
    
    /**
     * Get a specific plugin by ID
     */
    fun getPlugin(pluginId: String): Plugin? {
<span class="pc bpc" id="L186" title="1 of 2 branches missed.">        return loadedPlugins[pluginId]?.plugin</span>
    }
    
    /**
     * Get plugin state
     */
    fun getPluginState(pluginId: String): PluginState? {
<span class="pc bpc" id="L193" title="1 of 2 branches missed.">        return loadedPlugins[pluginId]?.state</span>
    }
    
    /**
     * Get the plugin registry
     */
<span class="fc" id="L199">    fun getRegistry(): PluginRegistry = pluginRegistry</span>
    
    /**
     * Get the event dispatcher
     */
<span class="fc" id="L204">    fun getEventDispatcher(): PluginEventDispatcher = eventDispatcher</span>
    
    /**
     * Install a plugin from a file or URL
     */
    suspend fun installPlugin(pluginSource: String): Boolean {
        // TODO: Implement plugin installation from URL or file
        // For now, just copy to plugin directory if it's a local file
<span class="nc" id="L212">        val sourceFile = File(pluginSource)</span>
<span class="nc bnc" id="L213" title="All 2 branches missed.">        if (!sourceFile.exists()) {</span>
<span class="nc" id="L214">            throw PluginLoadException(&quot;Plugin source not found: $pluginSource&quot;)</span>
        }
        
<span class="nc" id="L217">        val metadata = readPluginMetadata(URLClassLoader(arrayOf(sourceFile.toURI().toURL())))</span>
<span class="nc" id="L218">        val targetFile = File(pluginDir, &quot;${metadata.id}-${metadata.version}.jar&quot;)</span>
        
<span class="nc" id="L220">        sourceFile.copyTo(targetFile, overwrite = true)</span>
        
        // Load the installed plugin
<span class="nc" id="L223">        loadPlugin(targetFile.absolutePath)</span>
        
<span class="nc" id="L225">        return true</span>
    }
    
    /**
     * Uninstall a plugin
     */
    suspend fun uninstallPlugin(pluginId: String): Boolean {
<span class="fc" id="L232">        val loadedPlugin = loadedPlugins[pluginId]</span>
        
        // Unload if currently loaded
<span class="pc bpc" id="L235" title="1 of 2 branches missed.">        if (loadedPlugin != null) {</span>
<span class="nc" id="L236">            unloadPlugin(pluginId)</span>
        }
        
        // Remove plugin file
<span class="pc" id="L240">        val pluginFiles = File(pluginDir).listFiles { file -&gt;</span>
<span class="nc bnc" id="L241" title="All 4 branches missed.">            file.name.startsWith(&quot;$pluginId-&quot;) &amp;&amp; file.name.endsWith(&quot;.jar&quot;)</span>
        }
        
<span class="pc bpc" id="L244" title="1 of 2 branches missed.">        return pluginFiles?.any { it.delete() } ?: false</span>
    }
    
    /**
     * Update a plugin
     */
    suspend fun updatePlugin(pluginId: String): Boolean {
        // TODO: Implement plugin update mechanism
        // This would typically involve checking for updates from a repository
<span class="fc" id="L253">        throw NotImplementedError(&quot;Plugin update functionality not yet implemented&quot;)</span>
    }
    
    /**
     * Validate a plugin file
     */
    fun validatePlugin(pluginPath: String): PluginValidationResult {
<span class="fc" id="L260">        val errors = mutableListOf&lt;String&gt;()</span>
<span class="fc" id="L261">        val warnings = mutableListOf&lt;String&gt;()</span>
        
<span class="fc" id="L263">        try {</span>
<span class="fc" id="L264">            val pluginFile = File(pluginPath)</span>
<span class="fc bfc" id="L265" title="All 2 branches covered.">            if (!pluginFile.exists()) {</span>
<span class="fc" id="L266">                errors.add(&quot;Plugin file does not exist: $pluginPath&quot;)</span>
<span class="fc" id="L267">                return PluginValidationResult(false, errors, warnings)</span>
            }
            
<span class="pc bpc" id="L270" title="1 of 2 branches missed.">            if (!pluginFile.name.endsWith(&quot;.jar&quot;)) {</span>
<span class="fc" id="L271">                errors.add(&quot;Plugin file must be a JAR file&quot;)</span>
<span class="fc" id="L272">                return PluginValidationResult(false, errors, warnings)</span>
            }
            
            // Validate JAR structure
<span class="nc" id="L276">            val classLoader = URLClassLoader(arrayOf(pluginFile.toURI().toURL()))</span>
            
            // Check for plugin.json
<span class="nc" id="L279">            val metadataStream = classLoader.getResourceAsStream(&quot;plugin.json&quot;)</span>
<span class="nc bnc" id="L280" title="All 2 branches missed.">            if (metadataStream == null) {</span>
<span class="nc" id="L281">                errors.add(&quot;Plugin metadata file (plugin.json) not found&quot;)</span>
<span class="nc" id="L282">                return PluginValidationResult(false, errors, warnings)</span>
            }
            
            // Validate metadata
<span class="nc bnc" id="L286" title="All 2 branches missed.">            val jsonText = metadataStream.bufferedReader().use { it.readText() }</span>
<span class="nc" id="L287">            val jsonElement = Json.parseToJsonElement(jsonText).jsonObject</span>
<span class="nc" id="L288">            val metadata = PluginMetadata(</span>
<span class="nc bnc" id="L289" title="All 6 branches missed.">                id = jsonElement[&quot;id&quot;]?.jsonPrimitive?.content ?: throw PluginLoadException(&quot;Missing plugin id&quot;),</span>
<span class="nc bnc" id="L290" title="All 6 branches missed.">                name = jsonElement[&quot;name&quot;]?.jsonPrimitive?.content ?: throw PluginLoadException(&quot;Missing plugin name&quot;),</span>
<span class="nc bnc" id="L291" title="All 6 branches missed.">                version = jsonElement[&quot;version&quot;]?.jsonPrimitive?.content ?: throw PluginLoadException(&quot;Missing plugin version&quot;),</span>
<span class="nc bnc" id="L292" title="All 6 branches missed.">                description = jsonElement[&quot;description&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L293" title="All 6 branches missed.">                author = jsonElement[&quot;author&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L294" title="All 6 branches missed.">                mainClass = jsonElement[&quot;mainClass&quot;]?.jsonPrimitive?.content ?: throw PluginLoadException(&quot;Missing main class&quot;)</span>
            )
            
            // Check main class exists
<span class="nc" id="L298">            try {</span>
<span class="nc" id="L299">                val mainClass = classLoader.loadClass(metadata.mainClass)</span>
<span class="nc bnc" id="L300" title="All 2 branches missed.">                if (!Plugin::class.java.isAssignableFrom(mainClass)) {</span>
<span class="nc" id="L301">                    errors.add(&quot;Main class ${metadata.mainClass} does not implement Plugin interface&quot;)</span>
                }
<span class="nc" id="L303">            } catch (e: ClassNotFoundException) {</span>
<span class="nc" id="L304">                errors.add(&quot;Main class ${metadata.mainClass} not found in plugin&quot;)</span>
            }
            
            // Validate dependencies
<span class="nc" id="L308">            metadata.dependencies.forEach { dependency -&gt;</span>
<span class="nc bnc" id="L309" title="All 4 branches missed.">                if (!isPluginAvailable(dependency.id) &amp;&amp; !dependency.optional) {</span>
<span class="nc" id="L310">                    warnings.add(&quot;Required dependency ${dependency.id} is not available&quot;)</span>
                }
<span class="nc" id="L312">            }</span>
            
<span class="nc" id="L314">        } catch (e: Exception) {</span>
<span class="nc" id="L315">            errors.add(&quot;Plugin validation failed: ${e.message}&quot;)</span>
        }
        
<span class="nc" id="L318">        return PluginValidationResult(</span>
<span class="nc" id="L319">            isValid = errors.isEmpty(),</span>
<span class="nc" id="L320">            errors = errors,</span>
<span class="nc" id="L321">            warnings = warnings</span>
        )
    }
    
    private fun readPluginMetadata(classLoader: ClassLoader): PluginMetadata {
<span class="nc bnc" id="L326" title="All 2 branches missed.">        val metadataStream = classLoader.getResourceAsStream(&quot;plugin.json&quot;)</span>
<span class="nc" id="L327">            ?: throw PluginLoadException(&quot;Plugin metadata file (plugin.json) not found&quot;)</span>

<span class="nc bnc" id="L329" title="All 2 branches missed.">        val jsonText = metadataStream.bufferedReader().use { it.readText() }</span>
<span class="nc" id="L330">        val jsonElement = Json.parseToJsonElement(jsonText).jsonObject</span>

<span class="nc" id="L332">        return PluginMetadata(</span>
<span class="nc bnc" id="L333" title="All 6 branches missed.">            id = jsonElement[&quot;id&quot;]?.jsonPrimitive?.content ?: throw PluginLoadException(&quot;Missing plugin id&quot;),</span>
<span class="nc bnc" id="L334" title="All 6 branches missed.">            name = jsonElement[&quot;name&quot;]?.jsonPrimitive?.content ?: throw PluginLoadException(&quot;Missing plugin name&quot;),</span>
<span class="nc bnc" id="L335" title="All 6 branches missed.">            version = jsonElement[&quot;version&quot;]?.jsonPrimitive?.content ?: throw PluginLoadException(&quot;Missing plugin version&quot;),</span>
<span class="nc bnc" id="L336" title="All 6 branches missed.">            description = jsonElement[&quot;description&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L337" title="All 6 branches missed.">            author = jsonElement[&quot;author&quot;]?.jsonPrimitive?.content ?: &quot;&quot;,</span>
<span class="nc bnc" id="L338" title="All 6 branches missed.">            mainClass = jsonElement[&quot;mainClass&quot;]?.jsonPrimitive?.content ?: throw PluginLoadException(&quot;Missing main class&quot;),</span>
<span class="nc" id="L339">            dependencies = emptyList(), // TODO: Parse dependencies</span>
<span class="nc" id="L340">            permissions = emptyList(), // TODO: Parse permissions</span>
<span class="nc bnc" id="L341" title="All 4 branches missed.">            minCliVersion = jsonElement[&quot;minCliVersion&quot;]?.jsonPrimitive?.content,</span>
<span class="nc bnc" id="L342" title="All 4 branches missed.">            website = jsonElement[&quot;website&quot;]?.jsonPrimitive?.content</span>
        )
    }
    
    private fun validateDependencies(dependencies: List&lt;PluginDependency&gt;) {
<span class="nc" id="L347">        dependencies.forEach { dependency -&gt;</span>
<span class="nc bnc" id="L348" title="All 4 branches missed.">            if (!dependency.optional &amp;&amp; !isPluginAvailable(dependency.id)) {</span>
<span class="nc" id="L349">                throw PluginLoadException(&quot;Required dependency ${dependency.id} is not available&quot;)</span>
            }
<span class="nc" id="L351">        }</span>
<span class="nc" id="L352">    }</span>
    
    private fun isPluginAvailable(pluginId: String): Boolean {
<span class="nc" id="L355">        return loadedPlugins.containsKey(pluginId)</span>
    }
}

/**
 * Information about a loaded plugin
 */
<span class="nc" id="L362">private data class LoadedPlugin(</span>
<span class="nc" id="L363">    val plugin: Plugin,</span>
<span class="nc" id="L364">    val context: DefaultPluginContext,</span>
<span class="nc" id="L365">    val classLoader: ClassLoader,</span>
<span class="nc" id="L366">    val filePath: String,</span>
<span class="nc" id="L367">    val state: PluginState</span>
)
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>