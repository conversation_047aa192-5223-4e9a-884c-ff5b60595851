<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginRegistry</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_class">PluginRegistry</span></div><h1>PluginRegistry</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">250 of 379</td><td class="ctr2">34%</td><td class="bar">20 of 22</td><td class="ctr2">9%</td><td class="ctr1">21</td><td class="ctr2">32</td><td class="ctr1">49</td><td class="ctr2">77</td><td class="ctr1">10</td><td class="ctr2">21</td></tr></tfoot><tbody><tr><td id="a16"><a href="PluginRegistry.kt.html#L57" class="el_method">registerCommandPlugin(CommandPlugin)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="48" alt="48"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i0">9</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a15"><a href="PluginRegistry.kt.html#L87" class="el_method">registerAiServicePlugin(AiServicePlugin)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="41" alt="41"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h1">7</td><td class="ctr2" id="i1">7</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a20"><a href="PluginRegistry.kt.html#L40" class="el_method">unregisterPlugin(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="95" height="10" title="38" alt="38"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h2">7</td><td class="ctr2" id="i2">7</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a17"><a href="PluginRegistry.kt.html#L23" class="el_method">registerPlugin(Plugin, DefaultPluginContext)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="36" alt="36"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a19"><a href="PluginRegistry.kt.html#L74" class="el_method">unregisterCommandPlugin(CommandPlugin)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="23" alt="23"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="PluginRegistry.kt.html#L140" class="el_method">getAllCommands()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="17" alt="17"/></td><td class="ctr2" id="c10">54%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a18"><a href="PluginRegistry.kt.html#L102" class="el_method">unregisterAiServicePlugin(AiServicePlugin)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="11" alt="11"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a10"><a href="PluginRegistry.kt.html#L197" class="el_method">getPluginsByType(Class)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="10" alt="10"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="PluginRegistry.kt.html#L124" class="el_method">getCommandPlugin(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="10" alt="10"/></td><td class="ctr2" id="c9">58%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="PluginRegistry.kt.html#L162" class="el_method">getAiServicePlugin(AiProvider)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a9"><a href="PluginRegistry.kt.html#L190" class="el_method">getPluginContext(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="6" alt="6"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a5"><a href="PluginRegistry.kt.html#L132" class="el_method">getCommand(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="8" alt="8"/></td><td class="ctr2" id="c8">61%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a12"><a href="PluginRegistry.kt.html#L183" class="el_method">hasAiProvider(AiProvider)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="5" alt="5"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a14"><a href="PluginRegistry.kt.html#L12" class="el_method">PluginRegistry()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="70" height="10" title="28" alt="28"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a11"><a href="PluginRegistry.kt.html#L204" class="el_method">getStatistics()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="24" alt="24"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a0"><a href="PluginRegistry.kt.html#L217" class="el_method">clear()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="16" alt="16"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a8"><a href="PluginRegistry.kt.html#L110" class="el_method">getPlugin(String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="6" alt="6"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a4"><a href="PluginRegistry.kt.html#L117" class="el_method">getAllPlugins()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="5" alt="5"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a7"><a href="PluginRegistry.kt.html#L155" class="el_method">getCommandPlugins()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="5" alt="5"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a2"><a href="PluginRegistry.kt.html#L169" class="el_method">getAiServicePlugins()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="5" alt="5"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a13"><a href="PluginRegistry.kt.html#L176" class="el_method">hasCommand(String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="5" alt="5"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>