<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiServicePlugin.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">AiServicePlugin.kt</span></div><h1>AiServicePlugin.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

import com.aicodingcli.ai.AiService
import com.aicodingcli.ai.AiServiceConfig
import com.aicodingcli.ai.AiProvider

/**
 * Plugin that provides AI service implementations
 */
interface AiServicePlugin : Plugin {
    /**
     * The AI provider this plugin supports
     */
    val supportedProvider: AiProvider
    
    /**
     * Create an AI service instance with the given configuration
     */
    fun createAiService(config: AiServiceConfig): AiService
    
    /**
     * Validate the configuration for this AI service
     */
    fun validateConfig(config: AiServiceConfig): PluginValidationResult {
<span class="fc" id="L25">        return try {</span>
            // Basic validation - check if provider matches
<span class="fc bfc" id="L27" title="All 2 branches covered.">            if (config.provider != supportedProvider) {</span>
<span class="fc" id="L28">                return PluginValidationResult(</span>
<span class="fc" id="L29">                    isValid = false,</span>
<span class="fc" id="L30">                    errors = listOf(&quot;Provider mismatch: expected $supportedProvider, got ${config.provider}&quot;)</span>
                )
            }
            
            // Perform provider-specific validation
<span class="fc" id="L35">            validateProviderConfig(config)</span>
<span class="nc" id="L36">        } catch (e: Exception) {</span>
<span class="pc" id="L37">            PluginValidationResult(</span>
<span class="nc" id="L38">                isValid = false,</span>
<span class="nc" id="L39">                errors = listOf(&quot;Configuration validation failed: ${e.message}&quot;)</span>
            )
        }
    }
    
    /**
     * Provider-specific configuration validation
     */
    fun validateProviderConfig(config: AiServiceConfig): PluginValidationResult
    
    /**
     * Get the default configuration for this AI service
     */
    fun getDefaultConfig(): AiServiceConfig?
    
    /**
     * Get supported models for this AI service
     */
    fun getSupportedModels(): List&lt;String&gt;
}

/**
 * Base implementation for AI service plugins
 */
<span class="fc" id="L63">abstract class BaseAiServicePlugin : AiServicePlugin {</span>
    private var isInitialized = false
    private lateinit var pluginContext: PluginContext
    
    override fun initialize(context: PluginContext) {
<span class="pc bpc" id="L68" title="1 of 2 branches missed.">        if (isInitialized) {</span>
<span class="nc" id="L69">            throw IllegalStateException(&quot;Plugin ${metadata.id} is already initialized&quot;)</span>
        }
        
<span class="fc" id="L72">        this.pluginContext = context</span>
        
        // Perform plugin-specific initialization
<span class="fc" id="L75">        onInitialize(context)</span>
        
<span class="fc" id="L77">        isInitialized = true</span>
<span class="fc" id="L78">        context.logger.info(&quot;AI service plugin ${metadata.name} initialized for provider ${supportedProvider}&quot;)</span>
<span class="fc" id="L79">    }</span>
    
    override fun shutdown() {
<span class="pc bpc" id="L82" title="1 of 2 branches missed.">        if (!isInitialized) {</span>
<span class="nc" id="L83">            return</span>
        }
        
        // Perform plugin-specific cleanup
<span class="fc" id="L87">        onShutdown()</span>
        
<span class="fc" id="L89">        isInitialized = false</span>
<span class="fc" id="L90">        pluginContext.logger.info(&quot;AI service plugin ${metadata.name} shut down&quot;)</span>
<span class="fc" id="L91">    }</span>
    
    /**
     * Get the plugin context (only available after initialization)
     */
    protected fun getContext(): PluginContext {
<span class="nc bnc" id="L97" title="All 2 branches missed.">        if (!isInitialized) {</span>
<span class="nc" id="L98">            throw IllegalStateException(&quot;Plugin ${metadata.id} is not initialized&quot;)</span>
        }
<span class="nc" id="L100">        return pluginContext</span>
    }
    
    /**
     * Check if the plugin is initialized
     */
<span class="nc" id="L106">    protected fun isInitialized(): Boolean = isInitialized</span>
    
    /**
     * Called when the plugin is being initialized
     * Override this method to perform plugin-specific initialization
     */
    protected open fun onInitialize(context: PluginContext) {
        // Default implementation does nothing
<span class="fc" id="L114">    }</span>
    
    /**
     * Called when the plugin is being shut down
     * Override this method to perform plugin-specific cleanup
     */
    protected open fun onShutdown() {
        // Default implementation does nothing
<span class="fc" id="L122">    }</span>
    
    override fun validateProviderConfig(config: AiServiceConfig): PluginValidationResult {
<span class="fc" id="L125">        val errors = mutableListOf&lt;String&gt;()</span>
<span class="fc" id="L126">        val warnings = mutableListOf&lt;String&gt;()</span>
        
        // Basic validation
<span class="pc bpc" id="L129" title="3 of 6 branches missed.">        if (config.baseUrl?.isBlank() != false) {</span>
<span class="nc" id="L130">            errors.add(&quot;Base URL cannot be empty&quot;)</span>
        }
        
        // Check if API key is required
<span class="pc bpc" id="L134" title="2 of 4 branches missed.">        if (requiresApiKey() &amp;&amp; config.apiKey.isBlank()) {</span>
<span class="nc" id="L135">            errors.add(&quot;API key is required for $supportedProvider&quot;)</span>
        }
        
        // Validate model
<span class="fc" id="L139">        val supportedModels = getSupportedModels()</span>
<span class="pc bpc" id="L140" title="3 of 6 branches missed.">        if (supportedModels.isNotEmpty() &amp;&amp; config.model !in supportedModels) {</span>
<span class="fc" id="L141">            warnings.add(&quot;Model '${config.model}' is not in the list of known supported models: ${supportedModels.joinToString(&quot;, &quot;)}&quot;)</span>
        }
        
<span class="fc" id="L144">        return PluginValidationResult(</span>
<span class="fc" id="L145">            isValid = errors.isEmpty(),</span>
<span class="fc" id="L146">            errors = errors,</span>
<span class="fc" id="L147">            warnings = warnings</span>
        )
    }
    
    /**
     * Whether this AI service requires an API key
     */
<span class="nc" id="L154">    protected open fun requiresApiKey(): Boolean = true</span>
    
    /**
     * Get the default base URL for this AI service
     */
    protected abstract fun getDefaultBaseUrl(): String
    
    /**
     * Get the default model for this AI service
     */
    protected abstract fun getDefaultModel(): String
    
    override fun getDefaultConfig(): AiServiceConfig? {
<span class="fc" id="L167">        return try {</span>
<span class="pc" id="L168">            AiServiceConfig(</span>
<span class="fc" id="L169">                provider = supportedProvider,</span>
<span class="fc" id="L170">                baseUrl = getDefaultBaseUrl(),</span>
<span class="fc" id="L171">                apiKey = &quot;&quot;, // Will need to be set by user</span>
<span class="fc" id="L172">                model = getDefaultModel()</span>
            )
<span class="fc" id="L174">        } catch (e: Exception) {</span>
<span class="fc" id="L175">            null</span>
        }
    }
}

/**
 * Registry for AI service plugins
 */
object AiServicePluginRegistry {
<span class="fc" id="L184">    private val plugins = mutableMapOf&lt;AiProvider, AiServicePlugin&gt;()</span>
    
    /**
     * Register an AI service plugin
     */
    fun register(plugin: AiServicePlugin) {
<span class="fc" id="L190">        plugins[plugin.supportedProvider] = plugin</span>
<span class="fc" id="L191">    }</span>
    
    /**
     * Unregister an AI service plugin
     */
    fun unregister(provider: AiProvider) {
<span class="fc" id="L197">        plugins.remove(provider)</span>
<span class="fc" id="L198">    }</span>
    
    /**
     * Get an AI service plugin for a provider
     */
    fun getPlugin(provider: AiProvider): AiServicePlugin? {
<span class="fc" id="L204">        return plugins[provider]</span>
    }
    
    /**
     * Get all registered AI service plugins
     */
    fun getAllPlugins(): Map&lt;AiProvider, AiServicePlugin&gt; {
<span class="fc" id="L211">        return plugins.toMap()</span>
    }
    
    /**
     * Check if a provider is supported by any plugin
     */
    fun isProviderSupported(provider: AiProvider): Boolean {
<span class="fc" id="L218">        return plugins.containsKey(provider)</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>