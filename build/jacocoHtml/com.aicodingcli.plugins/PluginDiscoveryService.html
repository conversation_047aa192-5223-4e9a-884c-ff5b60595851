<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PluginDiscoveryService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_class">PluginDiscoveryService</span></div><h1>PluginDiscoveryService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">393 of 457</td><td class="ctr2">14%</td><td class="bar">85 of 90</td><td class="ctr2">5%</td><td class="ctr1">47</td><td class="ctr2">51</td><td class="ctr1">41</td><td class="ctr2">54</td><td class="ctr1">3</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a2"><a href="PluginRegistry.kt.html#L245" class="el_method">discoverPlugins()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="196" alt="196"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="27" alt="27"/></td><td class="ctr2" id="c2">12%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="42" alt="42"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">8%</td><td class="ctr1" id="f0">22</td><td class="ctr2" id="g0">24</td><td class="ctr1" id="h0">24</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="PluginRegistry.kt.html#L289" class="el_method">getPluginInfo(String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="169" alt="169"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="31" alt="31"/></td><td class="ctr2" id="c1">15%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="101" height="10" title="39" alt="39"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">2%</td><td class="ctr1" id="f1">20</td><td class="ctr2" id="g1">21</td><td class="ctr1" id="h1">16</td><td class="ctr2" id="i1">23</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="PluginRegistry.kt.html#L251" class="el_method">discoverPlugins$lambda$0(File)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="18" alt="18"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="PluginRegistry.kt.html#L258" class="el_method">discoverPlugins$lambda$3$lambda$1(JsonBuilder)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="PluginRegistry.kt.html#L295" class="el_method">getPluginInfo$lambda$4(JsonBuilder)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a5"><a href="PluginRegistry.kt.html#L239" class="el_method">PluginDiscoveryService(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>