<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MockAiService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_class">MockAiService</span></div><h1>MockAiService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">122 of 122</td><td class="ctr2">0%</td><td class="bar">8 of 8</td><td class="ctr2">0%</td><td class="ctr1">10</td><td class="ctr2">10</td><td class="ctr1">24</td><td class="ctr2">24</td><td class="ctr1">6</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a4"><a href="PluginTestFramework.kt.html#L275" class="el_method">streamChat(AiRequest, Continuation)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="38" alt="38"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h2">6</td><td class="ctr2" id="i2">6</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="PluginTestFramework.kt.html#L262" class="el_method">chat(AiRequest, Continuation)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="91" height="10" title="29" alt="29"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h1">7</td><td class="ctr2" id="i1">7</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="PluginTestFramework.kt.html#L248" class="el_method">MockAiService(AiProvider, List, boolean)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="27" alt="27"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i0">9</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a3"><a href="PluginTestFramework.kt.html#L248" class="el_method">MockAiService(AiProvider, List, boolean, int, DefaultConstructorMarker)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="17" alt="17"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">4</td><td class="ctr2" id="i3">4</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="PluginTestFramework.kt.html#L286" class="el_method">testConnection(Continuation)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="8" alt="8"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="PluginTestFramework.kt.html#L254" class="el_method">getConfig()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>