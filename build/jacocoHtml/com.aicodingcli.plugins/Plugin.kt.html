<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Plugin.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.plugins</a> &gt; <span class="el_source">Plugin.kt</span></div><h1>Plugin.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.plugins

import com.aicodingcli.ai.AiServiceFactory
import com.aicodingcli.config.ConfigManager
import com.aicodingcli.history.HistoryManager


/**
 * Base interface for all plugins
 */
interface Plugin {
    /**
     * Plugin metadata containing information about the plugin
     */
    val metadata: PluginMetadata
    
    /**
     * Initialize the plugin with the given context
     */
    fun initialize(context: PluginContext)
    
    /**
     * Shutdown the plugin and clean up resources
     */
    fun shutdown()
}

/**
 * Plugin metadata containing information about the plugin
 */
<span class="fc" id="L31">data class PluginMetadata(</span>
    /**
     * Unique identifier for the plugin
     */
<span class="fc" id="L35">    val id: String,</span>
    
    /**
     * Human-readable name of the plugin
     */
<span class="fc" id="L40">    val name: String,</span>
    
    /**
     * Version of the plugin (semantic versioning)
     */
<span class="fc" id="L45">    val version: String,</span>
    
    /**
     * Description of what the plugin does
     */
<span class="fc" id="L50">    val description: String,</span>
    
    /**
     * Author or organization that created the plugin
     */
<span class="fc" id="L55">    val author: String,</span>
    
    /**
     * Main class name for the plugin
     */
<span class="fc" id="L60">    val mainClass: String,</span>
    
    /**
     * List of dependencies required by this plugin
     */
<span class="fc" id="L65">    val dependencies: List&lt;PluginDependency&gt; = emptyList(),</span>
    
    /**
     * List of permissions required by this plugin
     */
<span class="fc" id="L70">    val permissions: List&lt;PluginPermission&gt; = emptyList(),</span>
    
    /**
     * Minimum version of the CLI tool required
     */
<span class="pc" id="L75">    val minCliVersion: String? = null,</span>
    
    /**
     * Website or repository URL for the plugin
     */
<span class="pc" id="L80">    val website: String? = null</span>
<span class="fc" id="L81">)</span>

/**
 * Plugin dependency specification
 */
<span class="pc" id="L86">data class PluginDependency(</span>
    /**
     * ID of the required plugin
     */
<span class="fc" id="L90">    val id: String,</span>
    
    /**
     * Version requirement (e.g., &quot;&gt;=1.0.0&quot;, &quot;~1.2.0&quot;)
     */
<span class="fc" id="L95">    val version: String,</span>
    
    /**
     * Whether this dependency is optional
     */
<span class="pc" id="L100">    val optional: Boolean = false</span>
<span class="nc" id="L101">)</span>

/**
 * Base class for plugin permissions
 */
sealed class PluginPermission {
    /**
     * Permission to access file system
     */
<span class="pc" id="L110">    data class FileSystemPermission(</span>
<span class="fc" id="L111">        val allowedPaths: List&lt;String&gt;,</span>
<span class="pc" id="L112">        val readOnly: Boolean = false</span>
<span class="pc" id="L113">    ) : PluginPermission()</span>
    
    /**
     * Permission to make network requests
     */
<span class="fc" id="L118">    data class NetworkPermission(</span>
<span class="fc" id="L119">        val allowedHosts: List&lt;String&gt;</span>
<span class="fc" id="L120">    ) : PluginPermission()</span>
    
    /**
     * Permission to execute system commands
     */
<span class="fc" id="L125">    data class SystemPermission(</span>
<span class="fc" id="L126">        val allowedCommands: List&lt;String&gt;</span>
<span class="fc" id="L127">    ) : PluginPermission()</span>
    
    /**
     * Permission to access configuration
     */
<span class="fc" id="L132">    object ConfigPermission : PluginPermission()</span>
    
    /**
     * Permission to access conversation history
     */
<span class="fc" id="L137">    object HistoryPermission : PluginPermission()</span>
}

/**
 * Plugin context providing access to CLI services
 */
interface PluginContext {
    /**
     * Configuration manager for accessing and modifying settings
     */
    val configManager: ConfigManager
    
    /**
     * History manager for accessing conversation history
     */
    val historyManager: HistoryManager
    
    /**
     * AI service factory for creating AI services
     */
    val aiServiceFactory: AiServiceFactory
    
    /**
     * Logger for plugin-specific logging
     */
    val logger: PluginLogger
    
    /**
     * Register a command that this plugin provides
     */
    fun registerCommand(command: Any) // Will be PluginCommand

    /**
     * Register an event handler for system events
     */
    fun registerEventHandler(handler: Any) // Will be PluginEventHandler
    
    /**
     * Get shared data between plugins
     */
    fun getSharedData(key: String): Any?
    
    /**
     * Set shared data for other plugins to access
     */
    fun setSharedData(key: String, value: Any)
    
    /**
     * Check if the plugin has a specific permission
     */
    fun hasPermission(permission: PluginPermission): Boolean
}

/**
 * Plugin-specific logger interface
 */
interface PluginLogger {
    fun debug(message: String)
    fun info(message: String)
    fun warn(message: String)
<span class="nc" id="L197">    fun error(message: String, throwable: Throwable? = null)</span>
}

/**
 * Plugin lifecycle states
 */
enum class PluginState {
<span class="fc" id="L204">    UNLOADED,</span>
<span class="fc" id="L205">    LOADED,</span>
<span class="fc" id="L206">    INITIALIZED,</span>
<span class="fc" id="L207">    RUNNING,</span>
<span class="fc" id="L208">    STOPPED,</span>
<span class="fc" id="L209">    ERROR</span>
}

/**
 * Plugin validation result
 */
<span class="fc" id="L215">data class PluginValidationResult(</span>
<span class="fc" id="L216">    val isValid: Boolean,</span>
<span class="pc" id="L217">    val errors: List&lt;String&gt; = emptyList(),</span>
<span class="fc" id="L218">    val warnings: List&lt;String&gt; = emptyList()</span>
<span class="fc" id="L219">)</span>

/**
 * Plugin load exception
 */
<span class="fc" id="L224">class PluginLoadException(message: String, cause: Throwable? = null) : Exception(message, cause)</span>

/**
 * Plugin execution exception
 */
<span class="fc" id="L229">class PluginExecutionException(message: String, cause: Throwable? = null) : Exception(message, cause)</span>
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>