<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.ai</a> &gt; <span class="el_source">AiModels.kt</span></div><h1>AiModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.ai

import kotlinx.serialization.Serializable

/**
 * Supported AI providers
 */
enum class AiProvider {
<span class="fc" id="L9">    OPENAI,</span>
<span class="fc" id="L10">    CLAUDE,</span>
<span class="fc" id="L11">    OLLAMA</span>
}

/**
 * Message roles in AI conversation
 */
enum class MessageRole {
<span class="fc" id="L18">    SYSTEM,</span>
<span class="fc" id="L19">    USER,</span>
<span class="fc" id="L20">    ASSISTANT</span>
}

/**
 * Finish reasons for AI responses
 */
enum class FinishReason {
<span class="fc" id="L27">    STOP,</span>
<span class="fc" id="L28">    LENGTH,</span>
<span class="fc" id="L29">    CONTENT_FILTER,</span>
<span class="fc" id="L30">    FUNCTION_CALL</span>
}

/**
 * Configuration for AI service
 */
<span class="pc bpc" id="L36" title="9 of 34 branches missed.">@Serializable</span>
<span class="fc" id="L37">data class AiServiceConfig(</span>
<span class="fc" id="L38">    val provider: AiProvider,</span>
<span class="fc" id="L39">    val apiKey: String,</span>
<span class="fc" id="L40">    val model: String,</span>
<span class="fc" id="L41">    val baseUrl: String? = null,</span>
<span class="fc" id="L42">    val temperature: Float = 0.7f,</span>
<span class="fc" id="L43">    val maxTokens: Int = 1000,</span>
<span class="pc" id="L44">    val timeout: Long = 30000L</span>
<span class="fc" id="L45">) {</span>
<span class="fc" id="L46">    init {</span>
<span class="pc bpc" id="L47" title="2 of 8 branches missed.">        require(apiKey.isNotBlank()) { &quot;API key cannot be empty&quot; }</span>
<span class="pc bpc" id="L48" title="4 of 8 branches missed.">        require(model.isNotBlank()) { &quot;Model cannot be empty&quot; }</span>
<span class="pc bpc" id="L49" title="4 of 12 branches missed.">        require(temperature in 0.0f..1.0f) { &quot;Temperature must be between 0.0 and 1.0&quot; }</span>
<span class="pc bpc" id="L50" title="4 of 8 branches missed.">        require(maxTokens &gt; 0) { &quot;Max tokens must be positive&quot; }</span>
<span class="pc bpc" id="L51" title="4 of 8 branches missed.">        require(timeout &gt; 0) { &quot;Timeout must be positive&quot; }</span>
<span class="fc" id="L52">    }</span>
}

/**
 * AI message in conversation
 */
<span class="pc bnc" id="L58" title="All 2 branches missed.">@Serializable</span>
<span class="fc" id="L59">data class AiMessage(</span>
<span class="fc" id="L60">    val role: MessageRole,</span>
<span class="fc" id="L61">    val content: String</span>
) {
<span class="fc" id="L63">    init {</span>
<span class="pc bpc" id="L64" title="4 of 8 branches missed.">        require(content.isNotBlank()) { &quot;Message content cannot be empty&quot; }</span>
<span class="fc" id="L65">    }</span>
}

/**
 * AI request
 */
<span class="pc bnc" id="L71" title="All 26 branches missed.">@Serializable</span>
<span class="fc" id="L72">data class AiRequest(</span>
<span class="fc" id="L73">    val messages: List&lt;AiMessage&gt;,</span>
<span class="fc" id="L74">    val model: String,</span>
<span class="pc" id="L75">    val temperature: Float = 0.7f,</span>
<span class="pc" id="L76">    val maxTokens: Int = 1000,</span>
<span class="pc" id="L77">    val stream: Boolean = false</span>
<span class="fc" id="L78">) {</span>
<span class="fc" id="L79">    init {</span>
<span class="pc bpc" id="L80" title="4 of 8 branches missed.">        require(messages.isNotEmpty()) { &quot;Messages cannot be empty&quot; }</span>
<span class="pc bpc" id="L81" title="6 of 8 branches missed.">        require(model.isNotBlank()) { &quot;Model cannot be empty&quot; }</span>
<span class="pc bpc" id="L82" title="9 of 12 branches missed.">        require(temperature in 0.0f..1.0f) { &quot;Temperature must be between 0.0 and 1.0&quot; }</span>
<span class="pc bpc" id="L83" title="6 of 8 branches missed.">        require(maxTokens &gt; 0) { &quot;Max tokens must be positive&quot; }</span>
<span class="fc" id="L84">    }</span>
}

/**
 * Token usage information
 */
<span class="nc bnc" id="L90" title="All 2 branches missed.">@Serializable</span>
<span class="fc" id="L91">data class TokenUsage(</span>
<span class="fc" id="L92">    val promptTokens: Int,</span>
<span class="fc" id="L93">    val completionTokens: Int,</span>
<span class="fc" id="L94">    val totalTokens: Int</span>
)

/**
 * AI response
 */
<span class="pc bnc" id="L100" title="All 2 branches missed.">@Serializable</span>
<span class="fc" id="L101">data class AiResponse(</span>
<span class="fc" id="L102">    val content: String,</span>
<span class="fc" id="L103">    val model: String,</span>
<span class="fc" id="L104">    val usage: TokenUsage,</span>
<span class="fc" id="L105">    val finishReason: FinishReason</span>
)

/**
 * AI streaming chunk
 */
<span class="pc bnc" id="L111" title="All 10 branches missed.">@Serializable</span>
<span class="pc" id="L112">data class AiStreamChunk(</span>
<span class="fc" id="L113">    val content: String,</span>
<span class="pc" id="L114">    val finishReason: FinishReason? = null</span>
<span class="nc" id="L115">)</span>
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>