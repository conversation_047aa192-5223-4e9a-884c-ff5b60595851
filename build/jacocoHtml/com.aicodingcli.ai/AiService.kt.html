<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiService.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.ai</a> &gt; <span class="el_source">AiService.kt</span></div><h1>AiService.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.ai

import kotlinx.coroutines.flow.Flow
import com.aicodingcli.ai.providers.OpenAiService as RealOpenAiService
import com.aicodingcli.ai.providers.ClaudeService as RealClaudeService
import com.aicodingcli.ai.providers.OllamaService as RealOllamaService

/**
 * AI service interface for different providers
 */
interface AiService {
    /**
     * Send a chat request and get response
     */
    suspend fun chat(request: AiRequest): AiResponse

    /**
     * Send a chat request and get streaming response
     */
    suspend fun streamChat(request: AiRequest): Flow&lt;AiStreamChunk&gt;

    /**
     * Test connection to AI service
     */
    suspend fun testConnection(): Boolean

    /**
     * Get service configuration
     */
    val config: AiServiceConfig
}

/**
 * Factory for creating AI services
 */
object AiServiceFactory {
    /**
     * Create AI service based on configuration
     */
    fun createService(config: AiServiceConfig): AiService {
        // Configuration is already validated in AiServiceConfig.init
<span class="fc bfc" id="L42" title="All 3 branches covered.">        return when (config.provider) {</span>
<span class="fc" id="L43">            AiProvider.OPENAI -&gt; RealOpenAiService(config)</span>
<span class="fc" id="L44">            AiProvider.CLAUDE -&gt; RealClaudeService(config)</span>
<span class="fc" id="L45">            AiProvider.OLLAMA -&gt; RealOllamaService(config)</span>
        }
    }
}

/**
 * Base implementation for AI services
 */
<span class="pc" id="L53">abstract class BaseAiService(override val config: AiServiceConfig) : AiService {</span>
    
    protected fun validateRequest(request: AiRequest) {
<span class="pc bpc" id="L56" title="2 of 4 branches missed.">        require(request.messages.isNotEmpty()) { &quot;Messages cannot be empty&quot; }</span>
<span class="pc bpc" id="L57" title="2 of 4 branches missed.">        require(request.model.isNotBlank()) { &quot;Model cannot be empty&quot; }</span>
<span class="fc" id="L58">    }</span>
}

/**
 * OpenAI service implementation (placeholder)
 */
<span class="nc" id="L64">class OpenAiService(config: AiServiceConfig) : BaseAiService(config) {</span>
    override suspend fun chat(request: AiRequest): AiResponse {
<span class="nc" id="L66">        validateRequest(request)</span>
        // TODO: Implement actual OpenAI API call
<span class="nc" id="L68">        return AiResponse(</span>
<span class="nc" id="L69">            content = &quot;Mock response from OpenAI&quot;,</span>
<span class="nc" id="L70">            model = request.model,</span>
<span class="nc" id="L71">            usage = TokenUsage(10, 5, 15),</span>
<span class="nc" id="L72">            finishReason = FinishReason.STOP</span>
        )
    }

    override suspend fun streamChat(request: AiRequest): Flow&lt;AiStreamChunk&gt; {
<span class="nc" id="L77">        validateRequest(request)</span>
        // TODO: Implement actual streaming
<span class="nc" id="L79">        return kotlinx.coroutines.flow.flowOf(</span>
<span class="nc" id="L80">            AiStreamChunk(&quot;Mock&quot;, null),</span>
<span class="nc" id="L81">            AiStreamChunk(&quot; response&quot;, null),</span>
<span class="nc" id="L82">            AiStreamChunk(&quot; from OpenAI&quot;, FinishReason.STOP)</span>
        )
    }

    override suspend fun testConnection(): Boolean {
        // TODO: Implement actual connection test
<span class="nc" id="L88">        return true</span>
    }
}






</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>