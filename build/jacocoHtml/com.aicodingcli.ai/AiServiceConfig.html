<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiServiceConfig</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.ai</a> &gt; <span class="el_class">AiServiceConfig</span></div><h1>AiServiceConfig</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">107 of 474</td><td class="ctr2">77%</td><td class="bar">27 of 78</td><td class="ctr2">65%</td><td class="ctr1">28</td><td class="ctr2">51</td><td class="ctr1">0</td><td class="ctr2">17</td><td class="ctr1">1</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a2"><a href="AiModels.kt.html#L36" class="el_method">AiServiceConfig(int, AiProvider, String, String, String, float, int, long, SerializationConstructorMarker)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="52" alt="52"/><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="105" alt="105"/></td><td class="ctr2" id="c10">66%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="75" height="10" title="20" alt="20"/></td><td class="ctr2" id="e2">62%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">17</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="AiModels.kt.html#L37" class="el_method">AiServiceConfig(AiProvider, String, String, String, float, int, long)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="28" alt="28"/><img src="../jacoco-resources/greenbar.gif" width="68" height="10" title="89" alt="89"/></td><td class="ctr2" id="c9">76%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="56" height="10" title="15" alt="15"/></td><td class="ctr2" id="e0">68%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a11"><a href="AiModels.kt.html#L36" class="el_method">write$Self$ai_coding_cli(AiServiceConfig, CompositeEncoder, SerialDescriptor)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="24" alt="24"/><img src="../jacoco-resources/greenbar.gif" width="67" height="10" title="88" alt="88"/></td><td class="ctr2" id="c8">78%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="16" alt="16"/></td><td class="ctr2" id="e1">66%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">13</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">4</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="AiModels.kt.html#L44" class="el_method">getTimeout()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="AiModels.kt.html#L36" class="el_method">static {...}</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="37" alt="37"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="AiModels.kt.html#L37" class="el_method">AiServiceConfig(AiProvider, String, String, String, float, int, long, int, DefaultConstructorMarker)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="30" alt="30"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i2">6</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="AiModels.kt.html#L38" class="el_method">getProvider()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="AiModels.kt.html#L39" class="el_method">getApiKey()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="AiModels.kt.html#L40" class="el_method">getModel()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a4"><a href="AiModels.kt.html#L41" class="el_method">getBaseUrl()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a8"><a href="AiModels.kt.html#L42" class="el_method">getTemperature()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a5"><a href="AiModels.kt.html#L43" class="el_method">getMaxTokens()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>