<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.ai</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.ai</span></div><h1>com.aicodingcli.ai</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">665 of 1,460</td><td class="ctr2">54%</td><td class="bar">102 of 175</td><td class="ctr2">41%</td><td class="ctr1">93</td><td class="ctr2">154</td><td class="ctr1">15</td><td class="ctr2">85</td><td class="ctr1">23</td><td class="ctr2">66</td><td class="ctr1">7</td><td class="ctr2">18</td></tr></tfoot><tbody><tr><td id="a0"><a href="AiModels.kt.html" class="el_source">AiModels.kt</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="587" alt="587"/><img src="../jacoco-resources/greenbar.gif" width="66" height="10" title="737" alt="737"/></td><td class="ctr2" id="c0">55%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="98" alt="98"/><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="66" alt="66"/></td><td class="ctr2" id="e1">40%</td><td class="ctr1" id="f0">84</td><td class="ctr2" id="g0">140</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i0">64</td><td class="ctr1" id="j0">18</td><td class="ctr2" id="k0">58</td><td class="ctr1" id="l0">6</td><td class="ctr2" id="m0">15</td></tr><tr><td id="a1"><a href="AiService.kt.html" class="el_source">AiService.kt</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="78" alt="78"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="58" alt="58"/></td><td class="ctr2" id="c1">42%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">63%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">14</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i1">21</td><td class="ctr1" id="j1">5</td><td class="ctr2" id="k1">8</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">3</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>