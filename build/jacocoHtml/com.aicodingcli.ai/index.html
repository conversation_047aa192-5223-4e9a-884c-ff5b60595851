<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.ai</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.ai</span></div><h1>com.aicodingcli.ai</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">665 of 1,460</td><td class="ctr2">54%</td><td class="bar">102 of 175</td><td class="ctr2">41%</td><td class="ctr1">93</td><td class="ctr2">154</td><td class="ctr1">15</td><td class="ctr2">85</td><td class="ctr1">23</td><td class="ctr2">66</td><td class="ctr1">7</td><td class="ctr2">18</td></tr></tfoot><tbody><tr><td id="a3"><a href="AiRequest.html" class="el_class">AiRequest</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="238" alt="238"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="133" alt="133"/></td><td class="ctr2" id="c8">35%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="78" height="10" title="51" alt="51"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="11" alt="11"/></td><td class="ctr2" id="e4">17%</td><td class="ctr1" id="f0">31</td><td class="ctr2" id="g1">41</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i1">14</td><td class="ctr1" id="j2">2</td><td class="ctr2" id="k1">10</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a7"><a href="AiServiceConfig.html" class="el_class">AiServiceConfig</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="107" alt="107"/><img src="../jacoco-resources/greenbar.gif" width="92" height="10" title="367" alt="367"/></td><td class="ctr2" id="c4">77%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="27" alt="27"/><img src="../jacoco-resources/greenbar.gif" width="78" height="10" title="51" alt="51"/></td><td class="ctr2" id="e1">65%</td><td class="ctr1" id="f1">28</td><td class="ctr2" id="g0">51</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i0">17</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k0">12</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a10"><a href="AiStreamChunk.html" class="el_class">AiStreamChunk</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="70" alt="70"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="32" alt="32"/></td><td class="ctr2" id="c10">31%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="10" alt="10"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j1">3</td><td class="ctr2" id="k3">7</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a5"><a href="AiResponse.html" class="el_class">AiResponse</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="58" alt="58"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="52" alt="52"/></td><td class="ctr2" id="c7">47%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g4">9</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j3">2</td><td class="ctr2" id="k2">8</td><td class="ctr1" id="l10">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a15"><a href="OpenAiService.html" class="el_class">OpenAiService</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="57" alt="57"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g7">4</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i2">13</td><td class="ctr1" id="j0">4</td><td class="ctr2" id="k6">4</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a0"><a href="AiMessage.html" class="el_class">AiMessage</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="55" alt="55"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="51" alt="51"/></td><td class="ctr2" id="c6">48%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">40%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">11</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j4">2</td><td class="ctr2" id="k4">6</td><td class="ctr1" id="l11">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a16"><a href="TokenUsage.html" class="el_class">TokenUsage</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="41" alt="41"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="21" alt="21"/></td><td class="ctr2" id="c9">33%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g5">7</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j5">2</td><td class="ctr2" id="k5">6</td><td class="ctr1" id="l12">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a12"><a href="BaseAiService.html" class="el_class">BaseAiService</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="21" alt="21"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="23" alt="23"/></td><td class="ctr2" id="c5">52%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g6">7</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">3</td><td class="ctr1" id="l13">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a11"><a href="AiStreamChunk$Companion.html" class="el_class">AiStreamChunk.Companion</a></td><td class="bar" id="b8"/><td class="ctr2" id="c12">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a6"><a href="AiResponse$Companion.html" class="el_class">AiResponse.Companion</a></td><td class="bar" id="b9"/><td class="ctr2" id="c13">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a17"><a href="TokenUsage$Companion.html" class="el_class">TokenUsage.Companion</a></td><td class="bar" id="b10"/><td class="ctr2" id="c14">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a4"><a href="AiRequest$Companion.html" class="el_class">AiRequest.Companion</a></td><td class="bar" id="b11"/><td class="ctr2" id="c15">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a8"><a href="AiServiceConfig$Companion.html" class="el_class">AiServiceConfig.Companion</a></td><td class="bar" id="b12"/><td class="ctr2" id="c16">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a1"><a href="AiMessage$Companion.html" class="el_class">AiMessage.Companion</a></td><td class="bar" id="b13"/><td class="ctr2" id="c17">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a9"><a href="AiServiceFactory.html" class="el_class">AiServiceFactory</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="35" alt="35"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="3" alt="3"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i8">4</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td><td class="ctr1" id="l14">0</td><td class="ctr2" id="m14">1</td></tr><tr><td id="a13"><a href="FinishReason.html" class="el_class">FinishReason</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="31" alt="31"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td><td class="ctr1" id="l15">0</td><td class="ctr2" id="m15">1</td></tr><tr><td id="a14"><a href="MessageRole.html" class="el_class">MessageRole</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="25" alt="25"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td><td class="ctr1" id="l16">0</td><td class="ctr2" id="m16">1</td></tr><tr><td id="a2"><a href="AiProvider.html" class="el_class">AiProvider</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="25" alt="25"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td><td class="ctr1" id="l17">0</td><td class="ctr2" id="m17">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>