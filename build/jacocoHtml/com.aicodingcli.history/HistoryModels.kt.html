<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>HistoryModels.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.history</a> &gt; <span class="el_source">HistoryModels.kt</span></div><h1>HistoryModels.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.history

import com.aicodingcli.ai.AiProvider
import com.aicodingcli.ai.MessageRole
import kotlinx.serialization.Serializable
import java.time.Instant
import java.util.UUID

/**
 * Conversation session containing multiple messages
 */
<span class="pc bpc" id="L12" title="9 of 34 branches missed.">@Serializable</span>
<span class="fc" id="L13">data class ConversationSession(</span>
<span class="pc" id="L14">    val id: String = UUID.randomUUID().toString(),</span>
<span class="fc" id="L15">    val title: String,</span>
<span class="fc" id="L16">    val provider: AiProvider,</span>
<span class="fc" id="L17">    val model: String,</span>
<span class="fc" id="L18">    val createdAt: Long = Instant.now().epochSecond,</span>
<span class="fc" id="L19">    val updatedAt: Long = Instant.now().epochSecond,</span>
<span class="pc" id="L20">    val messages: MutableList&lt;ConversationMessage&gt; = mutableListOf()</span>
<span class="fc" id="L21">) {</span>
    /**
     * Add a message to the conversation
     */
    fun addMessage(message: ConversationMessage) {
<span class="fc" id="L26">        messages.add(message)</span>
<span class="fc" id="L27">    }</span>

    /**
     * Get the last user message
     */
    fun getLastUserMessage(): ConversationMessage? {
<span class="fc bfc" id="L33" title="All 2 branches covered.">        return messages.lastOrNull { it.role == MessageRole.USER }</span>
    }

    /**
     * Get the last assistant message
     */
    fun getLastAssistantMessage(): ConversationMessage? {
<span class="fc bfc" id="L40" title="All 2 branches covered.">        return messages.lastOrNull { it.role == MessageRole.ASSISTANT }</span>
    }

    /**
     * Get conversation summary for display
     */
    fun getSummary(): String {
<span class="fc" id="L47">        val messageCount = messages.size</span>
<span class="fc" id="L48">        val lastMessage = messages.lastOrNull()</span>
<span class="pc bpc" id="L49" title="1 of 6 branches missed.">        val preview = lastMessage?.content?.take(50) ?: &quot;Empty conversation&quot;</span>
<span class="fc" id="L50">        return &quot;$title ($messageCount messages) - $preview...&quot;</span>
    }
}

/**
 * Individual message in a conversation
 */
<span class="pc bpc" id="L57" title="7 of 26 branches missed.">@Serializable</span>
<span class="fc" id="L58">data class ConversationMessage(</span>
<span class="pc" id="L59">    val id: String = UUID.randomUUID().toString(),</span>
<span class="fc" id="L60">    val role: MessageRole,</span>
<span class="fc" id="L61">    val content: String,</span>
<span class="fc" id="L62">    val timestamp: Long = Instant.now().epochSecond,</span>
<span class="fc" id="L63">    val tokenUsage: MessageTokenUsage? = null</span>
<span class="fc" id="L64">) {</span>
<span class="fc" id="L65">    init {</span>
<span class="pc bpc" id="L66" title="2 of 8 branches missed.">        require(content.isNotBlank()) { &quot;Message content cannot be empty&quot; }</span>
<span class="fc" id="L67">    }</span>
}

/**
 * Token usage information for a message
 */
<span class="pc bpc" id="L73" title="1 of 2 branches missed.">@Serializable</span>
<span class="fc" id="L74">data class MessageTokenUsage(</span>
<span class="fc" id="L75">    val promptTokens: Int,</span>
<span class="fc" id="L76">    val completionTokens: Int,</span>
<span class="fc" id="L77">    val totalTokens: Int</span>
)

/**
 * Search criteria for conversation history
 */
<span class="fc" id="L83">data class HistorySearchCriteria(</span>
<span class="fc" id="L84">    val query: String? = null,</span>
<span class="fc" id="L85">    val provider: AiProvider? = null,</span>
<span class="fc" id="L86">    val model: String? = null,</span>
<span class="fc" id="L87">    val fromDate: Long? = null,</span>
<span class="fc" id="L88">    val toDate: Long? = null,</span>
<span class="fc" id="L89">    val limit: Int = 50</span>
<span class="fc" id="L90">)</span>

/**
 * History statistics
 */
<span class="pc bnc" id="L95" title="All 2 branches missed.">@Serializable</span>
<span class="fc" id="L96">data class HistoryStatistics(</span>
<span class="fc" id="L97">    val totalConversations: Int,</span>
<span class="fc" id="L98">    val totalMessages: Int,</span>
<span class="fc" id="L99">    val totalTokensUsed: Long,</span>
<span class="fc" id="L100">    val providerBreakdown: Map&lt;AiProvider, Int&gt;,</span>
<span class="fc" id="L101">    val oldestConversation: Long?,</span>
<span class="fc" id="L102">    val newestConversation: Long?</span>
)
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>