<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>HistoryManager.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.history</a> &gt; <span class="el_source">HistoryManager.kt</span></div><h1>HistoryManager.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.history

import com.aicodingcli.ai.AiProvider
import com.aicodingcli.ai.MessageRole
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import java.io.File
import java.io.IOException
import java.time.Instant

/**
 * Manager for conversation history storage and retrieval
 */
<span class="fc" id="L15">class HistoryManager(</span>
<span class="fc" id="L16">    private val historyDir: String = System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli/history&quot;</span>
<span class="fc" id="L17">) {</span>
    
<span class="fc" id="L19">    private val historyFile = File(historyDir, &quot;conversations.json&quot;)</span>
<span class="fc" id="L20">    private val json = Json {</span>
<span class="fc" id="L21">        prettyPrint = true</span>
<span class="fc" id="L22">        ignoreUnknownKeys = true</span>
<span class="fc" id="L23">    }</span>
    
<span class="fc" id="L25">    private var conversations: MutableList&lt;ConversationSession&gt; = mutableListOf()</span>
    
<span class="fc" id="L27">    init {</span>
<span class="fc" id="L28">        ensureHistoryDirectoryExists()</span>
<span class="fc" id="L29">        loadConversations()</span>
<span class="fc" id="L30">    }</span>
    
    /**
     * Create a new conversation session
     */
    fun createConversation(
        title: String,
        provider: AiProvider,
        model: String
    ): ConversationSession {
<span class="fc" id="L40">        val conversation = ConversationSession(</span>
<span class="fc" id="L41">            title = title,</span>
<span class="fc" id="L42">            provider = provider,</span>
<span class="fc" id="L43">            model = model</span>
        )
<span class="fc" id="L45">        conversations.add(conversation)</span>
<span class="fc" id="L46">        saveConversations()</span>
<span class="fc" id="L47">        return conversation</span>
    }
    
    /**
     * Add a message to a conversation
     */
<span class="fc" id="L53">    fun addMessage(</span>
        conversationId: String,
        role: MessageRole,
        content: String,
<span class="fc" id="L57">        tokenUsage: MessageTokenUsage? = null</span>
    ): ConversationMessage {
<span class="fc bfc" id="L59" title="All 2 branches covered.">        val conversation = getConversation(conversationId)</span>
<span class="fc" id="L60">            ?: throw IllegalArgumentException(&quot;Conversation not found: $conversationId&quot;)</span>
        
<span class="fc" id="L62">        val message = ConversationMessage(</span>
<span class="fc" id="L63">            role = role,</span>
<span class="fc" id="L64">            content = content,</span>
<span class="fc" id="L65">            tokenUsage = tokenUsage</span>
        )
        
<span class="fc" id="L68">        conversation.addMessage(message)</span>
<span class="fc" id="L69">        conversation.copy(updatedAt = Instant.now().epochSecond)</span>
<span class="fc" id="L70">        saveConversations()</span>
        
<span class="fc" id="L72">        return message</span>
    }
    
    /**
     * Get a conversation by ID (supports partial ID matching)
     */
    fun getConversation(conversationId: String): ConversationSession? {
        // First try exact match
<span class="fc bfc" id="L80" title="All 6 branches covered.">        conversations.find { it.id == conversationId }?.let { return it }</span>

        // Then try partial match (if ID is at least 4 characters)
<span class="pc bpc" id="L83" title="1 of 2 branches missed.">        if (conversationId.length &gt;= 4) {</span>
<span class="fc" id="L84">            val matches = conversations.filter { it.id.startsWith(conversationId) }</span>
<span class="pc bpc" id="L85" title="1 of 3 branches missed.">            when (matches.size) {</span>
<span class="fc" id="L86">                1 -&gt; return matches.first()</span>
<span class="fc" id="L87">                0 -&gt; return null</span>
                else -&gt; {
                    // Multiple matches - this is ambiguous
<span class="nc" id="L90">                    throw IllegalArgumentException(&quot;Ambiguous conversation ID '$conversationId'. Matches: ${matches.map { it.id.take(8) }}&quot;)</span>
                }
            }
        }

<span class="nc" id="L95">        return null</span>
    }
    
    /**
     * Get all conversations
     */
    fun getAllConversations(): List&lt;ConversationSession&gt; {
<span class="fc" id="L102">        return conversations.sortedByDescending { it.updatedAt }</span>
    }
    
    /**
     * Search conversations
     */
    fun searchConversations(criteria: HistorySearchCriteria): List&lt;ConversationSession&gt; {
<span class="fc" id="L109">        var result = conversations.asSequence()</span>
        
        // Filter by provider
<span class="fc bfc" id="L112" title="All 2 branches covered.">        criteria.provider?.let { provider -&gt;</span>
<span class="fc bfc" id="L113" title="All 2 branches covered.">            result = result.filter { it.provider == provider }</span>
<span class="fc" id="L114">        }</span>
        
        // Filter by model
<span class="fc bfc" id="L117" title="All 2 branches covered.">        criteria.model?.let { model -&gt;</span>
<span class="fc" id="L118">            result = result.filter { it.model == model }</span>
<span class="fc" id="L119">        }</span>
        
        // Filter by date range
<span class="pc bpc" id="L122" title="1 of 2 branches missed.">        criteria.fromDate?.let { fromDate -&gt;</span>
<span class="nc bnc" id="L123" title="All 2 branches missed.">            result = result.filter { it.createdAt &gt;= fromDate }</span>
<span class="nc" id="L124">        }</span>
        
<span class="pc bpc" id="L126" title="1 of 2 branches missed.">        criteria.toDate?.let { toDate -&gt;</span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">            result = result.filter { it.createdAt &lt;= toDate }</span>
<span class="nc" id="L128">        }</span>
        
        // Filter by query (search in title and message content)
<span class="fc bfc" id="L131" title="All 2 branches covered.">        criteria.query?.let { query -&gt;</span>
<span class="fc" id="L132">            val queryLower = query.lowercase()</span>
<span class="fc" id="L133">            result = result.filter { conversation -&gt;</span>
<span class="fc bfc" id="L134" title="All 2 branches covered.">                conversation.title.lowercase().contains(queryLower) ||</span>
<span class="pc bpc" id="L135" title="1 of 2 branches missed.">                conversation.messages.any { it.content.lowercase().contains(queryLower) }</span>
            }
<span class="fc" id="L137">        }</span>
        
<span class="fc" id="L139">        return result</span>
<span class="fc" id="L140">            .sortedByDescending { it.updatedAt }</span>
<span class="fc" id="L141">            .take(criteria.limit)</span>
<span class="fc" id="L142">            .toList()</span>
    }
    
    /**
     * Delete a conversation
     */
    fun deleteConversation(conversationId: String): Boolean {
<span class="fc" id="L149">        val removed = conversations.removeIf { it.id == conversationId }</span>
<span class="fc bfc" id="L150" title="All 2 branches covered.">        if (removed) {</span>
<span class="fc" id="L151">            saveConversations()</span>
        }
<span class="fc" id="L153">        return removed</span>
    }
    
    /**
     * Clear all conversations
     */
    fun clearAllConversations() {
<span class="fc" id="L160">        conversations.clear()</span>
<span class="fc" id="L161">        saveConversations()</span>
<span class="fc" id="L162">    }</span>
    
    /**
     * Get history statistics
     */
    fun getStatistics(): HistoryStatistics {
<span class="fc bfc" id="L168" title="All 2 branches covered.">        val totalMessages = conversations.sumOf { it.messages.size }</span>
<span class="fc bfc" id="L169" title="All 2 branches covered.">        val totalTokens = conversations.sumOf { conversation -&gt;</span>
<span class="fc bfc" id="L170" title="All 4 branches covered.">            conversation.messages.sumOf { it.tokenUsage?.totalTokens?.toLong() ?: 0L }</span>
        }
        
<span class="fc" id="L173">        val providerBreakdown = conversations.groupBy { it.provider }</span>
<span class="fc" id="L174">            .mapValues { it.value.size }</span>
        
<span class="pc bpc" id="L176" title="2 of 6 branches missed.">        val oldestConversation = conversations.minOfOrNull { it.createdAt }</span>
<span class="pc bpc" id="L177" title="1 of 6 branches missed.">        val newestConversation = conversations.maxOfOrNull { it.createdAt }</span>
        
<span class="fc" id="L179">        return HistoryStatistics(</span>
<span class="fc" id="L180">            totalConversations = conversations.size,</span>
<span class="fc" id="L181">            totalMessages = totalMessages,</span>
<span class="fc" id="L182">            totalTokensUsed = totalTokens,</span>
<span class="fc" id="L183">            providerBreakdown = providerBreakdown,</span>
<span class="fc" id="L184">            oldestConversation = oldestConversation,</span>
<span class="fc" id="L185">            newestConversation = newestConversation</span>
        )
    }
    
    /**
     * Load conversations from file
     */
    private fun loadConversations() {
<span class="fc" id="L193">        try {</span>
<span class="fc bfc" id="L194" title="All 2 branches covered.">            if (historyFile.exists()) {</span>
<span class="fc" id="L195">                val content = historyFile.readText()</span>
<span class="pc bpc" id="L196" title="2 of 4 branches missed.">                if (content.isNotBlank()) {</span>
<span class="fc" id="L197">                    conversations = json.decodeFromString&lt;MutableList&lt;ConversationSession&gt;&gt;(content)</span>
                }
            }
<span class="nc" id="L200">        } catch (e: Exception) {</span>
            // If loading fails, start with empty list
<span class="nc" id="L202">            conversations = mutableListOf()</span>
        }
<span class="fc" id="L204">    }</span>
    
    /**
     * Save conversations to file
     */
    private fun saveConversations() {
<span class="fc" id="L210">        try {</span>
<span class="fc" id="L211">            val content = json.encodeToString(conversations)</span>
<span class="fc" id="L212">            historyFile.writeText(content)</span>
<span class="nc" id="L213">        } catch (e: IOException) {</span>
<span class="nc" id="L214">            throw RuntimeException(&quot;Failed to save conversation history: ${e.message}&quot;, e)</span>
        }
<span class="fc" id="L216">    }</span>
    
    /**
     * Ensure history directory exists
     */
    private fun ensureHistoryDirectoryExists() {
<span class="fc" id="L222">        val dir = File(historyDir)</span>
<span class="pc bpc" id="L223" title="1 of 2 branches missed.">        if (!dir.exists()) {</span>
<span class="nc" id="L224">            dir.mkdirs()</span>
        }
<span class="fc" id="L226">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>