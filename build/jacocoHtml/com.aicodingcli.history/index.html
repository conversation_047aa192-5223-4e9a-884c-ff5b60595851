<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli.history</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli.history</span></div><h1>com.aicodingcli.history</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">235 of 1,656</td><td class="ctr2">85%</td><td class="bar">37 of 145</td><td class="ctr2">74%</td><td class="ctr1">39</td><td class="ctr2">144</td><td class="ctr1">11</td><td class="ctr2">153</td><td class="ctr1">7</td><td class="ctr2">76</td><td class="ctr1">3</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a4"><a href="HistoryManager.html" class="el_class">HistoryManager</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="91" alt="91"/><img src="../jacoco-resources/greenbar.gif" width="104" height="10" title="617" alt="617"/></td><td class="ctr2" id="c5">87%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="91" height="10" title="48" alt="48"/></td><td class="ctr2" id="e1">76%</td><td class="ctr1" id="f0">13</td><td class="ctr2" id="g0">49</td><td class="ctr1" id="h0">11</td><td class="ctr2" id="i0">103</td><td class="ctr1" id="j0">2</td><td class="ctr2" id="k0">22</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a6"><a href="HistoryStatistics.html" class="el_class">HistoryStatistics</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="78" alt="78"/><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="77" alt="77"/></td><td class="ctr2" id="c6">49%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">11</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j1">2</td><td class="ctr2" id="k2">10</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="ConversationSession.html" class="el_class">ConversationSession</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="26" alt="26"/><img src="../jacoco-resources/greenbar.gif" width="59" height="10" title="351" alt="351"/></td><td class="ctr2" id="c2">93%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="64" height="10" title="34" alt="34"/></td><td class="ctr2" id="e0">77%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g1">38</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i1">18</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k1">16</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="ConversationMessage.html" class="el_class">ConversationMessage</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="26" alt="26"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="238" alt="238"/></td><td class="ctr2" id="c4">90%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="47" height="10" title="25" alt="25"/></td><td class="ctr2" id="e2">73%</td><td class="ctr1" id="f2">9</td><td class="ctr2" id="g2">27</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k3">10</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a8"><a href="MessageTokenUsage.html" class="el_class">MessageTokenUsage</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="57" alt="57"/></td><td class="ctr2" id="c3">91%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g5">7</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k5">6</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a9"><a href="MessageTokenUsage$Companion.html" class="el_class">MessageTokenUsage.Companion</a></td><td class="bar" id="b5"/><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k6">1</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a1"><a href="ConversationMessage$Companion.html" class="el_class">ConversationMessage.Companion</a></td><td class="bar" id="b6"/><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k7">1</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a7"><a href="HistoryStatistics$Companion.html" class="el_class">HistoryStatistics.Companion</a></td><td class="bar" id="b7"/><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k8">1</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a5"><a href="HistorySearchCriteria.html" class="el_class">HistorySearchCriteria</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="78" alt="78"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g4">8</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k4">8</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a3"><a href="ConversationSession$Companion.html" class="el_class">ConversationSession.Companion</a></td><td class="bar" id="b9"/><td class="ctr2" id="c1">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>