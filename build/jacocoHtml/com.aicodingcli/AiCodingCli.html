<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiCodingCli</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli</a> &gt; <span class="el_class">AiCodingCli</span></div><h1>AiCodingCli</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,290 of 4,105</td><td class="ctr2">44%</td><td class="bar">268 of 405</td><td class="ctr2">33%</td><td class="ctr1">196</td><td class="ctr2">285</td><td class="ctr1">418</td><td class="ctr2">752</td><td class="ctr1">20</td><td class="ctr2">60</td></tr></tfoot><tbody><tr><td id="a43"><a href="Main.kt.html#L1245" class="el_method">handlePluginList(String[])</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="314" alt="314"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="24" alt="24"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f3">13</td><td class="ctr2" id="g4">13</td><td class="ctr1" id="h0">52</td><td class="ctr2" id="i0">52</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a13"><a href="Main.kt.html#L1464" class="el_method">displayPluginInfo(PluginMetadata, boolean, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="244" alt="244"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="28" alt="28"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f2">15</td><td class="ctr2" id="g3">15</td><td class="ctr1" id="h1">40</td><td class="ctr2" id="i2">40</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a56"><a href="Main.kt.html#L543" class="el_method">setConfigValue(String, String, Continuation)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="208" alt="208"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="18" alt="18"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f4">13</td><td class="ctr2" id="g5">13</td><td class="ctr1" id="h2">24</td><td class="ctr2" id="i9">24</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a19"><a href="Main.kt.html#L576" class="el_method">getConfigValue(String, Continuation)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="161" alt="161"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="39" alt="39"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f0">25</td><td class="ctr2" id="g0">25</td><td class="ctr1" id="h7">20</td><td class="ctr2" id="i13">20</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="Main.kt.html#L1000" class="el_method">displayAnalysisResultText(CodeAnalysisResult)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="136" alt="136"/><img src="../jacoco-resources/greenbar.gif" width="58" height="10" title="153" alt="153"/></td><td class="ctr2" id="c34">52%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="9" alt="9"/></td><td class="ctr2" id="e24">31%</td><td class="ctr1" id="f1">16</td><td class="ctr2" id="g1">17</td><td class="ctr1" id="h4">22</td><td class="ctr2" id="i1">46</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a36"><a href="Main.kt.html#L695" class="el_method">handleHistoryShow(String[])</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="116" alt="116"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="29" alt="29"/></td><td class="ctr2" id="c39">20%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="5" alt="5"/></td><td class="ctr2" id="e22">45%</td><td class="ctr1" id="f13">4</td><td class="ctr2" id="g12">7</td><td class="ctr1" id="h6">21</td><td class="ctr2" id="i5">29</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a45"><a href="Main.kt.html#L1520" class="el_method">handlePluginValidate(String[])</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="97" alt="97"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="14" alt="14"/></td><td class="ctr2" id="e31">0%</td><td class="ctr1" id="f7">8</td><td class="ctr2" id="g11">8</td><td class="ctr1" id="h3">23</td><td class="ctr2" id="i10">23</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a38"><a href="Main.kt.html#L1202" class="el_method">handlePluginCommand(String[])</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="94" alt="94"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="12" alt="12"/></td><td class="ctr2" id="e32">0%</td><td class="ctr1" id="f5">10</td><td class="ctr2" id="g8">10</td><td class="ctr1" id="h11">14</td><td class="ctr2" id="i17">14</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a41"><a href="Main.kt.html#L1426" class="el_method">handlePluginInfo(String[])</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="89" alt="89"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="12" alt="12"/></td><td class="ctr2" id="e33">0%</td><td class="ctr1" id="f8">7</td><td class="ctr2" id="g13">7</td><td class="ctr1" id="h8">19</td><td class="ctr2" id="i15">19</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a40"><a href="Main.kt.html#L1369" class="el_method">handlePluginEnable(String[])</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="78" alt="78"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="10" alt="10"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f10">6</td><td class="ctr2" id="g16">6</td><td class="ctr1" id="h12">14</td><td class="ctr2" id="i18">14</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a48"><a href="Main.kt.html#L90" class="el_method">parseArgs(String[])</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="63" alt="63"/><img src="../jacoco-resources/greenbar.gif" width="62" height="10" title="163" alt="163"/></td><td class="ctr2" id="c30">72%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="58" height="10" title="19" alt="19"/></td><td class="ctr2" id="e6">79%</td><td class="ctr1" id="f11">5</td><td class="ctr2" id="g2">16</td><td class="ctr1" id="h16">8</td><td class="ctr2" id="i3">40</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a47"><a href="Main.kt.html#L951" class="el_method">parseAnalyzeOptions(String[])</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="58" alt="58"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="38" alt="38"/></td><td class="ctr2" id="c35">39%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e25">25%</td><td class="ctr1" id="f6">9</td><td class="ctr2" id="g7">11</td><td class="ctr1" id="h9">16</td><td class="ctr2" id="i7">26</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="Main.kt.html#L1163" class="el_method">displayIssues(List, String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="53" alt="53"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="68" alt="68"/></td><td class="ctr2" id="c33">56%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="6" alt="6"/></td><td class="ctr2" id="e23">42%</td><td class="ctr1" id="f9">7</td><td class="ctr2" id="g9">9</td><td class="ctr1" id="h10">15</td><td class="ctr2" id="i4">30</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a4"><a href="Main.kt.html#L379" class="el_method">createDefaultProviderConfig(AiProvider)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="46" alt="46"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="3" alt="3"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f17">3</td><td class="ctr2" id="g23">3</td><td class="ctr1" id="h5">22</td><td class="ctr2" id="i11">22</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a29"><a href="Main.kt.html#L515" class="el_method">handleConfigProvider(String[])</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="42" alt="42"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="15" alt="15"/></td><td class="ctr2" id="c37">26%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">25%</td><td class="ctr1" id="f12">5</td><td class="ctr2" id="g17">6</td><td class="ctr1" id="h15">10</td><td class="ctr2" id="i19">14</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a31"><a href="Main.kt.html#L781" class="el_method">handleHistoryClear()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="37" alt="37"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="6" alt="6"/></td><td class="ctr2" id="e36">0%</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g20">4</td><td class="ctr1" id="h17">7</td><td class="ctr2" id="i29">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a7"><a href="Main.kt.html#L1062" class="el_method">displayAnalysisResultJson(CodeAnalysisResult)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="35" alt="35"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h13">12</td><td class="ctr2" id="i22">12</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a15"><a href="Main.kt.html#L1120" class="el_method">displayProjectAnalysisResultJson(ProjectAnalysisResult)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="35" alt="35"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h14">12</td><td class="ctr2" id="i23">12</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a1"><a href="Main.kt.html#L196" class="el_method">askQuestion$default(AiCodingCli, String, AiProvider, String, boolean, String, boolean, int, Object)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="34" alt="34"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h18">6</td><td class="ctr2" id="i35">6</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a10"><a href="Main.kt.html#L1139" class="el_method">displayMetrics(CodeMetrics, String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="33" alt="33"/><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="82" alt="82"/></td><td class="ctr2" id="c31">71%</td><td class="bar" id="d32"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="3" alt="3"/></td><td class="ctr2" id="e7">75%</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g24">3</td><td class="ctr1" id="h27">2</td><td class="ctr2" id="i16">17</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a30"><a href="Main.kt.html#L445" class="el_method">handleConfigSet(String[])</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="33" alt="33"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="9" alt="9"/></td><td class="ctr2" id="c38">21%</td><td class="bar" id="d33"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e9">50%</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g33">2</td><td class="ctr1" id="h24">4</td><td class="ctr2" id="i30">7</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a42"><a href="Main.kt.html#L1321" class="el_method">handlePluginInstall(String[])</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="29" alt="29"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e37">0%</td><td class="ctr1" id="f18">3</td><td class="ctr2" id="g25">3</td><td class="ctr1" id="h19">6</td><td class="ctr2" id="i36">6</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a44"><a href="Main.kt.html#L1345" class="el_method">handlePluginUninstall(String[])</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="29" alt="29"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e38">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g26">3</td><td class="ctr1" id="h20">6</td><td class="ctr2" id="i37">6</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a39"><a href="Main.kt.html#L1402" class="el_method">handlePluginDisable(String[])</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="29" alt="29"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e39">0%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g27">3</td><td class="ctr1" id="h21">6</td><td class="ctr2" id="i38">6</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a16"><a href="Main.kt.html#L1086" class="el_method">displayProjectAnalysisResultText(ProjectAnalysisResult)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="68" height="10" title="178" alt="178"/></td><td class="ctr2" id="c18">87%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="3" alt="3"/></td><td class="ctr2" id="e10">50%</td><td class="ctr1" id="f45">0</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h22">5</td><td class="ctr2" id="i8">25</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a27"><a href="Main.kt.html#L464" class="el_method">handleConfigGet(String[])</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="11" alt="11"/></td><td class="ctr2" id="c36">37%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">50%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g28">3</td><td class="ctr1" id="h25">3</td><td class="ctr2" id="i39">6</td><td class="ctr1" id="j29">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a34"><a href="Main.kt.html#L653" class="el_method">handleHistoryList(String[])</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="43" height="10" title="113" alt="113"/></td><td class="ctr2" id="c19">87%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">60%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g18">6</td><td class="ctr1" id="h23">5</td><td class="ctr2" id="i6">28</td><td class="ctr1" id="j30">0</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a57"><a href="Main.kt.html#L167" class="el_method">testConnection$default(AiCodingCli, AiProvider, String, int, Object)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="15" alt="15"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a18"><a href="Main.kt.html#L364" class="el_method">generateConversationTitle(String)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="26" alt="26"/></td><td class="ctr2" id="c32">66%</td><td class="bar" id="d34"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e12">50%</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g34">2</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i41">4</td><td class="ctr1" id="j31">0</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a11"><a href="Main.kt.html#L1463" class="el_method">displayPluginInfo$default(AiCodingCli, PluginMetadata, boolean, String, int, Object)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="11" alt="11"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a55"><a href="Main.kt.html#L61" class="el_method">run(String[])</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="130" alt="130"/></td><td class="ctr2" id="c17">92%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="70" height="10" title="23" alt="23"/></td><td class="ctr2" id="e1">95%</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g6">13</td><td class="ctr1" id="h42">0</td><td class="ctr2" id="i20">14</td><td class="ctr1" id="j32">0</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a21"><a href="Main.kt.html#L831" class="el_method">handleAnalyzeCommand(String[])</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="55" alt="55"/></td><td class="ctr2" id="c20">85%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="24" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">88%</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g14">7</td><td class="ctr1" id="h28">2</td><td class="ctr2" id="i24">11</td><td class="ctr1" id="j33">0</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a12"><a href="Main.kt.html#L1460" class="el_method">displayPluginInfo(Plugin, boolean)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="9" alt="9"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h29">2</td><td class="ctr2" id="i51">2</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a22"><a href="Main.kt.html#L870" class="el_method">handleAnalyzeFile(String[])</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="33" alt="33"/></td><td class="ctr2" id="c23">82%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">50%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g29">3</td><td class="ctr1" id="h30">2</td><td class="ctr2" id="i31">7</td><td class="ctr1" id="j34">0</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a25"><a href="Main.kt.html#L889" class="el_method">handleAnalyzeProject(String[])</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="33" alt="33"/></td><td class="ctr2" id="c24">82%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">50%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g30">3</td><td class="ctr1" id="h31">2</td><td class="ctr2" id="i32">7</td><td class="ctr1" id="j35">0</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a24"><a href="Main.kt.html#L908" class="el_method">handleAnalyzeMetrics(String[])</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="33" alt="33"/></td><td class="ctr2" id="c25">82%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">50%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g31">3</td><td class="ctr1" id="h32">2</td><td class="ctr2" id="i33">7</td><td class="ctr1" id="j36">0</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a23"><a href="Main.kt.html#L927" class="el_method">handleAnalyzeIssues(String[])</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="33" alt="33"/></td><td class="ctr2" id="c26">82%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">50%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g32">3</td><td class="ctr1" id="h33">2</td><td class="ctr2" id="i34">7</td><td class="ctr1" id="j37">0</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a35"><a href="Main.kt.html#L736" class="el_method">handleHistorySearch(String[])</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="108" alt="108"/></td><td class="ctr2" id="c16">94%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="5" alt="5"/></td><td class="ctr2" id="e4">83%</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g21">4</td><td class="ctr1" id="h34">2</td><td class="ctr2" id="i12">21</td><td class="ctr1" id="j38">0</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a33"><a href="Main.kt.html#L765" class="el_method">handleHistoryDelete(String[])</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="29" alt="29"/></td><td class="ctr2" id="c22">82%</td><td class="bar" id="d31"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="5" alt="5"/></td><td class="ctr2" id="e5">83%</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g22">4</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i27">9</td><td class="ctr1" id="j39">0</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a53"><a href="Main.kt.html#L1223" class="el_method">printPluginHelp()</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c59">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h26">3</td><td class="ctr2" id="i46">3</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a37"><a href="Main.kt.html#L793" class="el_method">handleHistoryStats()</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="106" alt="106"/></td><td class="ctr2" id="c15">95%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e17">50%</td><td class="ctr1" id="f16">4</td><td class="ctr2" id="g19">5</td><td class="ctr1" id="h43">0</td><td class="ctr2" id="i14">20</td><td class="ctr1" id="j40">0</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a46"><a href="Main.kt.html#L606" class="el_method">maskApiKey(String)</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="20" alt="20"/></td><td class="ctr2" id="c27">80%</td><td class="bar" id="d35"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e18">50%</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g35">2</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i47">3</td><td class="ctr1" id="j41">0</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a20"><a href="Main.kt.html#L372" class="el_method">getProviderConfig(AiProvider, Continuation)</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="23" alt="23"/></td><td class="ctr2" id="c21">85%</td><td class="bar" id="d36"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e19">50%</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g36">2</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i42">4</td><td class="ctr1" id="j42">0</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a6"><a href="Main.kt.html#L993" class="el_method">displayAnalysisResult(CodeAnalysisResult, String)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="14" alt="14"/></td><td class="ctr2" id="c28">77%</td><td class="bar" id="d37"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e20">50%</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g37">2</td><td class="ctr1" id="h44">0</td><td class="ctr2" id="i43">4</td><td class="ctr1" id="j43">0</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a14"><a href="Main.kt.html#L1079" class="el_method">displayProjectAnalysisResult(ProjectAnalysisResult, String)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="14" alt="14"/></td><td class="ctr2" id="c29">77%</td><td class="bar" id="d38"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="e21">50%</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g38">2</td><td class="ctr1" id="h45">0</td><td class="ctr2" id="i44">4</td><td class="ctr1" id="j44">0</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a32"><a href="Main.kt.html#L614" class="el_method">handleHistoryCommand(String[])</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="67" alt="67"/></td><td class="ctr2" id="c14">95%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">90%</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g10">9</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i21">13</td><td class="ctr1" id="j45">0</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a26"><a href="Main.kt.html#L409" class="el_method">handleConfigCommand(String[])</a></td><td class="bar" id="b46"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="57" alt="57"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d39"><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="9" alt="9"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f46">0</td><td class="ctr2" id="g15">7</td><td class="ctr1" id="h46">0</td><td class="ctr2" id="i25">11</td><td class="ctr1" id="j46">0</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a0"><a href="Main.kt.html#L26" class="el_method">AiCodingCli()</a></td><td class="bar" id="b47"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="38" alt="38"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">0</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h47">0</td><td class="ctr2" id="i28">8</td><td class="ctr1" id="j47">0</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a3"><a href="Main.kt.html#L342" class="el_method">buildMessageHistory(ConversationSession, String)</a></td><td class="bar" id="b48"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="36" alt="36"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">0</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h48">0</td><td class="ctr2" id="i26">11</td><td class="ctr1" id="j48">0</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a2"><a href="Main.kt.html#L204" class="el_method">askQuestion(String, AiProvider, String, boolean, String, boolean)</a></td><td class="bar" id="b49"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">0</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h49">0</td><td class="ctr2" id="i52">2</td><td class="ctr1" id="j49">0</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a58"><a href="Main.kt.html#L168" class="el_method">testConnection(AiProvider, String)</a></td><td class="bar" id="b50"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="14" alt="14"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">0</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h50">0</td><td class="ctr2" id="i53">2</td><td class="ctr1" id="j50">0</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a17"><a href="Main.kt.html#L820" class="el_method">formatTimestamp(long)</a></td><td class="bar" id="b51"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">0</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h51">0</td><td class="ctr2" id="i45">4</td><td class="ctr1" id="j51">0</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a28"><a href="Main.kt.html#L492" class="el_method">handleConfigList()</a></td><td class="bar" id="b52"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="12" alt="12"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">0</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h52">0</td><td class="ctr2" id="i54">2</td><td class="ctr1" id="j52">0</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a5"><a href="Main.kt.html#L335" class="el_method">createNewConversation(String, AiServiceConfig)</a></td><td class="bar" id="b53"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="11" alt="11"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">0</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h53">0</td><td class="ctr2" id="i40">5</td><td class="ctr1" id="j53">0</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a50"><a href="Main.kt.html#L427" class="el_method">printConfigHelp()</a></td><td class="bar" id="b54"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">0</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h54">0</td><td class="ctr2" id="i48">3</td><td class="ctr1" id="j54">0</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a52"><a href="Main.kt.html#L634" class="el_method">printHistoryHelp()</a></td><td class="bar" id="b55"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">0</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h55">0</td><td class="ctr2" id="i49">3</td><td class="ctr1" id="j55">0</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a49"><a href="Main.kt.html#L849" class="el_method">printAnalyzeHelp()</a></td><td class="bar" id="b56"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">0</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h56">0</td><td class="ctr2" id="i50">3</td><td class="ctr1" id="j56">0</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a54"><a href="Main.kt.html#L160" class="el_method">printVersion()</a></td><td class="bar" id="b57"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">0</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h57">0</td><td class="ctr2" id="i55">2</td><td class="ctr1" id="j57">0</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a51"><a href="Main.kt.html#L164" class="el_method">printHelp()</a></td><td class="bar" id="b58"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">0</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h58">0</td><td class="ctr2" id="i56">2</td><td class="ctr1" id="j58">0</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a59"><a href="Main.kt.html#L827" class="el_method">times(String, int)</a></td><td class="bar" id="b59"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f59">0</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h59">0</td><td class="ctr2" id="i59">1</td><td class="ctr1" id="j59">0</td><td class="ctr2" id="k59">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>