<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.aicodingcli</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <span class="el_package">com.aicodingcli</span></div><h1>com.aicodingcli</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,732 of 5,237</td><td class="ctr2">47%</td><td class="bar">304 of 467</td><td class="ctr2">34%</td><td class="ctr1">233</td><td class="ctr2">345</td><td class="ctr1">503</td><td class="ctr2">959</td><td class="ctr1">31</td><td class="ctr2">89</td><td class="ctr1">8</td><td class="ctr2">19</td></tr></tfoot><tbody><tr><td id="a0"><a href="AiCodingCli.html" class="el_class">AiCodingCli</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="2,290" alt="2,290"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="1,815" alt="1,815"/></td><td class="ctr2" id="c9">44%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="268" alt="268"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="137" alt="137"/></td><td class="ctr2" id="e4">33%</td><td class="ctr1" id="f0">196</td><td class="ctr2" id="g0">285</td><td class="ctr1" id="h0">418</td><td class="ctr2" id="i0">752</td><td class="ctr1" id="j0">20</td><td class="ctr2" id="k0">60</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a3"><a href="AiCodingCli$askQuestion$1.html" class="el_class">AiCodingCli.askQuestion.new Function2() {...}</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="70" alt="70"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="301" alt="301"/></td><td class="ctr2" id="c3">81%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="19" alt="19"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f1">15</td><td class="ctr2" id="g1">20</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i1">67</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k4">1</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a9"><a href="AiCodingCli$handleConfigGet$1.html" class="el_class">AiCodingCli.handleConfigGet.new Function2() {...}</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="66" alt="66"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h1">12</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k5">1</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a15"><a href="AiCodingCli$handlePluginInstall$1.html" class="el_class">AiCodingCli.handlePluginInstall.new Function2() {...}</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="42" alt="42"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i6">9</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k6">1</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a16"><a href="AiCodingCli$handlePluginUninstall$1.html" class="el_class">AiCodingCli.handlePluginUninstall.new Function2() {...}</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="42" alt="42"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h4">9</td><td class="ctr2" id="i7">9</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k7">1</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a13"><a href="AiCodingCli$handlePluginDisable$1.html" class="el_class">AiCodingCli.handlePluginDisable.new Function2() {...}</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="42" alt="42"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h5">9</td><td class="ctr2" id="i8">9</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k8">1</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a12"><a href="AiCodingCli$handleConfigSet$1.html" class="el_class">AiCodingCli.handleConfigSet.new Function2() {...}</a></td><td class="bar" id="b6"/><td class="ctr2" id="c15">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i14">6</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a11"><a href="AiCodingCli$handleConfigProvider$1.html" class="el_class">AiCodingCli.handleConfigProvider.new Function2() {...}</a></td><td class="bar" id="b7"/><td class="ctr2" id="c16">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h7">6</td><td class="ctr2" id="i15">6</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k10">1</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a14"><a href="AiCodingCli$handlePluginEnable$1.html" class="el_class">AiCodingCli.handlePluginEnable.new Function2() {...}</a></td><td class="bar" id="b8"/><td class="ctr2" id="c17">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h8">6</td><td class="ctr2" id="i16">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k11">1</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a17"><a href="AiCodingCli$testConnection$1.html" class="el_class">AiCodingCli.testConnection.new Function2() {...}</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="73" alt="73"/></td><td class="ctr2" id="c8">74%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i2">18</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k12">1</td><td class="ctr1" id="l10">0</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a1"><a href="AiCodingCli$AnalyzeOptions.html" class="el_class">AiCodingCli.AnalyzeOptions</a></td><td class="bar" id="b10"/><td class="ctr2" id="c10">40%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i17">4</td><td class="ctr1" id="j1">2</td><td class="ctr2" id="k2">4</td><td class="ctr1" id="l11">0</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a18"><a href="MainKt.html" class="el_class">MainKt</a></td><td class="bar" id="b11"/><td class="ctr2" id="c18">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h10">4</td><td class="ctr2" id="i18">4</td><td class="ctr1" id="j2">2</td><td class="ctr2" id="k3">2</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a10"><a href="AiCodingCli$handleConfigList$1.html" class="el_class">AiCodingCli.handleConfigList.new Function2() {...}</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="84" alt="84"/></td><td class="ctr2" id="c2">90%</td><td class="bar" id="d7"/><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i3">18</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k13">1</td><td class="ctr1" id="l12">0</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a7"><a href="AiCodingCli$handleAnalyzeMetrics$1.html" class="el_class">AiCodingCli.handleAnalyzeMetrics.new Function2() {...}</a></td><td class="bar" id="b13"/><td class="ctr2" id="c4">78%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k14">1</td><td class="ctr1" id="l13">0</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a6"><a href="AiCodingCli$handleAnalyzeIssues$1.html" class="el_class">AiCodingCli.handleAnalyzeIssues.new Function2() {...}</a></td><td class="bar" id="b14"/><td class="ctr2" id="c5">78%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i11">7</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k15">1</td><td class="ctr1" id="l14">0</td><td class="ctr2" id="m14">1</td></tr><tr><td id="a8"><a href="AiCodingCli$handleAnalyzeProject$1.html" class="el_class">AiCodingCli.handleAnalyzeProject.new Function2() {...}</a></td><td class="bar" id="b15"/><td class="ctr2" id="c6">77%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i12">7</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k16">1</td><td class="ctr1" id="l15">0</td><td class="ctr2" id="m15">1</td></tr><tr><td id="a5"><a href="AiCodingCli$handleAnalyzeFile$1.html" class="el_class">AiCodingCli.handleAnalyzeFile.new Function2() {...}</a></td><td class="bar" id="b16"/><td class="ctr2" id="c7">77%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i13">7</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k17">1</td><td class="ctr1" id="l16">0</td><td class="ctr2" id="m16">1</td></tr><tr><td id="a4"><a href="AiCodingCli$CommandOptions.html" class="el_class">AiCodingCli.CommandOptions</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="78" alt="78"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k1">8</td><td class="ctr1" id="l17">0</td><td class="ctr2" id="m17">1</td></tr><tr><td id="a2"><a href="AiCodingCli$askQuestion$1$1.html" class="el_class">AiCodingCli.askQuestion.1.new FlowCollector() {...}</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="44" alt="44"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i5">11</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td><td class="ctr1" id="l18">0</td><td class="ctr2" id="m18">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>