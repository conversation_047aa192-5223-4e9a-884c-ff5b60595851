<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AiCodingCli.askQuestion.new Function2() {...}</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli</a> &gt; <span class="el_class">AiCodingCli.askQuestion.new Function2() {...}</span></div><h1>AiCodingCli.askQuestion.new Function2() {...}</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">70 of 371</td><td class="ctr2">81%</td><td class="bar">19 of 38</td><td class="ctr2">50%</td><td class="ctr1">15</td><td class="ctr2">20</td><td class="ctr1">10</td><td class="ctr2">67</td><td class="ctr1">0</td><td class="ctr2">1</td></tr></tfoot><tbody><tr><td id="a0"><a href="Main.kt.html#L204" class="el_method">invokeSuspend(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="70" alt="70"/><img src="../jacoco-resources/greenbar.gif" width="97" height="10" title="301" alt="301"/></td><td class="ctr2" id="c0">81%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="19" alt="19"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f0">15</td><td class="ctr2" id="g0">20</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i0">67</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>