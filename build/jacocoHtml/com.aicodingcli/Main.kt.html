<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Main.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli</a> &gt; <span class="el_source">Main.kt</span></div><h1>Main.kt</h1><pre class="source lang-java linenums">package com.aicodingcli

import com.aicodingcli.ai.*
import com.aicodingcli.config.ConfigManager
import com.aicodingcli.history.HistoryManager
import com.aicodingcli.history.HistorySearchCriteria
import com.aicodingcli.history.MessageTokenUsage
import com.aicodingcli.code.analysis.DefaultCodeAnalyzer
import com.aicodingcli.code.common.ProgrammingLanguage
import com.aicodingcli.plugins.*
import kotlinx.coroutines.runBlocking
import java.io.File
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

fun main(args: Array&lt;String&gt;) {
<span class="nc" id="L19">    val cli = AiCodingCli()</span>
<span class="nc" id="L20">    cli.run(args)</span>
<span class="nc" id="L21">}</span>

// Extension function for string repetition
<span class="nc" id="L24">private operator fun String.times(n: Int): String = this.repeat(n)</span>

<span class="fc" id="L26">class AiCodingCli {</span>
    companion object {
        const val VERSION = &quot;0.1.0&quot;
        const val HELP_TEXT = &quot;&quot;&quot;AI Coding CLI - A command line tool for AI-assisted coding

Usage: ai-coding-cli [COMMAND] [OPTIONS]

Commands:
  test-connection    Test connection to AI service
  ask &lt;message&gt;      Ask AI a question
  config &lt;subcommand&gt; Manage configuration settings
  history &lt;subcommand&gt; Manage conversation history
  analyze &lt;subcommand&gt; Analyze code files and projects
  plugin &lt;subcommand&gt; Manage plugins

Options:
  --version          Show version information
  --help             Show this help message
  --provider &lt;name&gt;  Use specific AI provider (openai, claude, ollama)
  --model &lt;name&gt;     Use specific model for the AI provider
  --stream           Enable streaming response (real-time output)
  --continue &lt;id&gt;    Continue an existing conversation
  --new              Force start a new conversation&quot;&quot;&quot;
    }

<span class="fc" id="L51">    private val configManager = ConfigManager()</span>
<span class="fc" id="L52">    private val historyManager = HistoryManager()</span>
<span class="fc" id="L53">    private val codeAnalyzer = DefaultCodeAnalyzer()</span>
<span class="fc" id="L54">    private val pluginManager = PluginManager(</span>
<span class="fc" id="L55">        configManager = configManager,</span>
<span class="fc" id="L56">        historyManager = historyManager,</span>
<span class="fc" id="L57">        aiServiceFactory = AiServiceFactory</span>
    )

    fun run(args: Array&lt;String&gt;) {
<span class="fc" id="L61">        val (command, options) = parseArgs(args)</span>

<span class="fc" id="L63">        when {</span>
<span class="fc bfc" id="L64" title="All 4 branches covered.">            args.isEmpty() -&gt; printHelp()</span>
<span class="fc bfc" id="L65" title="All 2 branches covered.">            command == &quot;--version&quot; -&gt; printVersion()</span>
<span class="fc bfc" id="L66" title="All 2 branches covered.">            command == &quot;--help&quot; -&gt; printHelp()</span>
<span class="fc bfc" id="L67" title="All 2 branches covered.">            command == &quot;test-connection&quot; -&gt; testConnection(options.provider, options.model)</span>
<span class="fc bfc" id="L68" title="All 6 branches covered.">            command == &quot;ask&quot; &amp;&amp; options.message.isNotEmpty() -&gt; askQuestion(options.message, options.provider, options.model, options.stream, options.continueConversationId, options.forceNew)</span>
<span class="fc bfc" id="L69" title="All 2 branches covered.">            command == &quot;config&quot; -&gt; handleConfigCommand(args.drop(1).toTypedArray())</span>
<span class="fc bfc" id="L70" title="All 2 branches covered.">            command == &quot;history&quot; -&gt; handleHistoryCommand(args.drop(1).toTypedArray())</span>
<span class="fc bfc" id="L71" title="All 2 branches covered.">            command == &quot;analyze&quot; -&gt; handleAnalyzeCommand(args.drop(1).toTypedArray())</span>
<span class="pc bpc" id="L72" title="1 of 2 branches missed.">            command == &quot;plugin&quot; -&gt; handlePluginCommand(args.drop(1).toTypedArray())</span>
            else -&gt; {
<span class="fc" id="L74">                println(&quot;Unknown command: $command&quot;)</span>
<span class="fc" id="L75">                printHelp()</span>
            }
        }
<span class="fc" id="L78">    }</span>

<span class="fc" id="L80">    private data class CommandOptions(</span>
<span class="fc" id="L81">        val provider: AiProvider? = null,</span>
<span class="fc" id="L82">        val model: String? = null,</span>
<span class="fc" id="L83">        val stream: Boolean = false,</span>
<span class="fc" id="L84">        val continueConversationId: String? = null,</span>
<span class="fc" id="L85">        val forceNew: Boolean = false,</span>
<span class="fc" id="L86">        val message: String = &quot;&quot;</span>
<span class="fc" id="L87">    )</span>

    private fun parseArgs(args: Array&lt;String&gt;): Pair&lt;String, CommandOptions&gt; {
<span class="fc bfc" id="L90" title="All 4 branches covered.">        if (args.isEmpty()) return &quot;&quot; to CommandOptions()</span>

<span class="fc" id="L92">        var command = args[0]</span>
<span class="fc" id="L93">        var provider: AiProvider? = null</span>
<span class="fc" id="L94">        var model: String? = null</span>
<span class="fc" id="L95">        var stream = false</span>
<span class="fc" id="L96">        var continueConversationId: String? = null</span>
<span class="fc" id="L97">        var forceNew = false</span>
<span class="fc" id="L98">        var message = &quot;&quot;</span>

<span class="fc" id="L100">        var i = 1</span>
<span class="fc bfc" id="L101" title="All 2 branches covered.">        while (i &lt; args.size) {</span>
<span class="fc bfc" id="L102" title="All 6 branches covered.">            when (args[i]) {</span>
                &quot;--provider&quot; -&gt; {
<span class="pc bpc" id="L104" title="1 of 2 branches missed.">                    if (i + 1 &lt; args.size) {</span>
<span class="pc bpc" id="L105" title="3 of 4 branches missed.">                        provider = when (args[i + 1].lowercase()) {</span>
<span class="nc" id="L106">                            &quot;openai&quot; -&gt; AiProvider.OPENAI</span>
<span class="nc" id="L107">                            &quot;claude&quot; -&gt; AiProvider.CLAUDE</span>
<span class="fc" id="L108">                            &quot;ollama&quot; -&gt; AiProvider.OLLAMA</span>
                            else -&gt; {
<span class="nc" id="L110">                                println(&quot;Unknown provider: ${args[i + 1]}&quot;)</span>
<span class="nc" id="L111">                                return command to CommandOptions()</span>
                            }
                        }
<span class="fc" id="L114">                        i += 2</span>
                    } else {
<span class="nc" id="L116">                        println(&quot;--provider requires a value&quot;)</span>
<span class="nc" id="L117">                        return command to CommandOptions()</span>
                    }
                }
                &quot;--model&quot; -&gt; {
<span class="pc bpc" id="L121" title="1 of 2 branches missed.">                    if (i + 1 &lt; args.size) {</span>
<span class="fc" id="L122">                        model = args[i + 1]</span>
<span class="fc" id="L123">                        i += 2</span>
                    } else {
<span class="nc" id="L125">                        println(&quot;--model requires a value&quot;)</span>
<span class="nc" id="L126">                        return command to CommandOptions()</span>
                    }
                }
                &quot;--stream&quot; -&gt; {
<span class="fc" id="L130">                    stream = true</span>
<span class="fc" id="L131">                    i++</span>
                }
                &quot;--continue&quot; -&gt; {
<span class="fc bfc" id="L134" title="All 2 branches covered.">                    if (i + 1 &lt; args.size) {</span>
<span class="fc" id="L135">                        continueConversationId = args[i + 1]</span>
<span class="fc" id="L136">                        i += 2</span>
                    } else {
<span class="fc" id="L138">                        println(&quot;--continue requires a conversation ID&quot;)</span>
<span class="fc" id="L139">                        return command to CommandOptions()</span>
                    }
                }
                &quot;--new&quot; -&gt; {
<span class="fc" id="L143">                    forceNew = true</span>
<span class="fc" id="L144">                    i++</span>
                }
                else -&gt; {
<span class="fc bfc" id="L147" title="All 2 branches covered.">                    if (command == &quot;ask&quot;) {</span>
<span class="fc" id="L148">                        message = args.drop(i).joinToString(&quot; &quot;)</span>
<span class="fc" id="L149">                        break</span>
                    }
<span class="fc" id="L151">                    i++</span>
                }
            }
        }

<span class="fc" id="L156">        return command to CommandOptions(provider, model, stream, continueConversationId, forceNew, message)</span>
    }

    private fun printVersion() {
<span class="fc" id="L160">        println(VERSION)</span>
<span class="fc" id="L161">    }</span>

    private fun printHelp() {
<span class="fc" id="L164">        println(HELP_TEXT)</span>
<span class="fc" id="L165">    }</span>

<span class="nc" id="L167">    private fun testConnection(provider: AiProvider? = null, model: String? = null) {</span>
<span class="pc" id="L168">        runBlocking {</span>
<span class="fc" id="L169">            try {</span>
<span class="pc bpc" id="L170" title="1 of 2 branches missed.">                val config = if (provider != null) {</span>
<span class="fc" id="L171">                    var providerConfig = getProviderConfig(provider)</span>
<span class="pc bpc" id="L172" title="1 of 2 branches missed.">                    if (model != null) {</span>
<span class="fc" id="L173">                        providerConfig = providerConfig.copy(model = model)</span>
                    }
<span class="fc" id="L175">                    providerConfig</span>
                } else {
<span class="nc" id="L177">                    configManager.getCurrentProviderConfig()</span>
                }
<span class="fc" id="L179">                val service = AiServiceFactory.createService(config)</span>
<span class="fc" id="L180">                val result = service.testConnection()</span>

<span class="pc bpc" id="L182" title="1 of 2 branches missed.">                if (result) {</span>
<span class="fc" id="L183">                    println(&quot;✅ Connection to ${config.provider} successful!&quot;)</span>
<span class="pc bpc" id="L184" title="1 of 2 branches missed.">                    if (model != null) {</span>
<span class="fc" id="L185">                        println(&quot;   Using model: $model&quot;)</span>
                    }
                } else {
<span class="nc" id="L188">                    println(&quot;❌ Connection to ${config.provider} failed!&quot;)</span>
                }
<span class="nc" id="L190">            } catch (e: Exception) {</span>
<span class="nc" id="L191">                println(&quot;❌ Error testing connection: ${e.message}&quot;)</span>
            }
<span class="fc" id="L193">        }</span>
<span class="fc" id="L194">    }</span>

<span class="nc" id="L196">    private fun askQuestion(</span>
        question: String,
<span class="nc" id="L198">        provider: AiProvider? = null,</span>
<span class="nc" id="L199">        model: String? = null,</span>
<span class="nc" id="L200">        stream: Boolean = false,</span>
<span class="nc" id="L201">        continueConversationId: String? = null,</span>
<span class="nc" id="L202">        forceNew: Boolean = false</span>
    ) {
<span class="fc" id="L204">        runBlocking {</span>
<span class="fc" id="L205">            try {</span>
<span class="fc bfc" id="L206" title="All 2 branches covered.">                val config = if (provider != null) {</span>
<span class="fc" id="L207">                    var providerConfig = getProviderConfig(provider)</span>
<span class="pc bpc" id="L208" title="1 of 2 branches missed.">                    if (model != null) {</span>
<span class="nc" id="L209">                        providerConfig = providerConfig.copy(model = model)</span>
                    }
<span class="fc" id="L211">                    providerConfig</span>
                } else {
<span class="fc" id="L213">                    configManager.getCurrentProviderConfig()</span>
                }
<span class="fc" id="L215">                val service = AiServiceFactory.createService(config)</span>

                // Determine conversation to use
<span class="fc" id="L218">                val conversation = when {</span>
<span class="fc bfc" id="L219" title="All 2 branches covered.">                    continueConversationId != null -&gt; {</span>
                        // User explicitly wants to continue a specific conversation
<span class="fc" id="L221">                        val existing = historyManager.getConversation(continueConversationId)</span>
<span class="pc bpc" id="L222" title="1 of 2 branches missed.">                        if (existing == null) {</span>
<span class="fc" id="L223">                            println(&quot;❌ Conversation not found: $continueConversationId&quot;)</span>
<span class="fc" id="L224">                            return@runBlocking</span>
                        }

                        // Validate provider/model compatibility
<span class="nc bnc" id="L228" title="All 4 branches missed.">                        if (provider != null &amp;&amp; existing.provider != config.provider) {</span>
<span class="nc" id="L229">                            println(&quot;⚠️  Warning: Switching provider from ${existing.provider} to ${config.provider}&quot;)</span>
                        }
<span class="nc bnc" id="L231" title="All 4 branches missed.">                        if (model != null &amp;&amp; existing.model != config.model) {</span>
<span class="nc" id="L232">                            println(&quot;⚠️  Warning: Switching model from ${existing.model} to ${config.model}&quot;)</span>
                        }

<span class="nc" id="L235">                        existing</span>
                    }
<span class="fc bfc" id="L237" title="All 2 branches covered.">                    forceNew -&gt; {</span>
                        // User explicitly wants a new conversation
<span class="fc" id="L239">                        createNewConversation(question, config)</span>
                    }
                    else -&gt; {
                        // Smart conversation management: continue recent conversation if compatible
<span class="fc" id="L243">                        val recentConversations = historyManager.getAllConversations().take(5)</span>
<span class="pc bpc" id="L244" title="2 of 4 branches missed.">                        val compatibleConversation = recentConversations.find { conv -&gt;</span>
<span class="pc bpc" id="L245" title="1 of 2 branches missed.">                            conv.provider == config.provider &amp;&amp;</span>
<span class="pc bpc" id="L246" title="1 of 2 branches missed.">                            conv.model == config.model &amp;&amp;</span>
<span class="pc bpc" id="L247" title="2 of 4 branches missed.">                            conv.messages.isNotEmpty()</span>
                        }

<span class="pc bpc" id="L250" title="1 of 2 branches missed.">                        if (compatibleConversation != null) {</span>
<span class="fc" id="L251">                            println(&quot;🔄 Continuing conversation: ${compatibleConversation.title} (${compatibleConversation.id.take(8)})&quot;)</span>
<span class="fc" id="L252">                            compatibleConversation</span>
                        } else {
<span class="nc" id="L254">                            createNewConversation(question, config)</span>
                        }
                    }
                }

                // Add user message to history
<span class="fc" id="L260">                historyManager.addMessage(</span>
<span class="fc" id="L261">                    conversationId = conversation.id,</span>
<span class="fc" id="L262">                    role = MessageRole.USER,</span>
<span class="fc" id="L263">                    content = question</span>
                )

                // Build message history for context
<span class="fc" id="L267">                val messages = buildMessageHistory(conversation, question)</span>

<span class="fc" id="L269">                val request = AiRequest(</span>
<span class="fc" id="L270">                    messages = messages,</span>
<span class="fc" id="L271">                    model = config.model,</span>
<span class="fc" id="L272">                    temperature = config.temperature,</span>
<span class="fc" id="L273">                    maxTokens = config.maxTokens,</span>
<span class="fc" id="L274">                    stream = stream</span>
                )

<span class="fc" id="L277">                println(&quot;🤖 Asking ${config.provider}...&quot;)</span>
<span class="pc bpc" id="L278" title="1 of 2 branches missed.">                if (model != null) {</span>
<span class="nc" id="L279">                    println(&quot;   Using model: $model&quot;)</span>
                }
<span class="pc bpc" id="L281" title="1 of 2 branches missed.">                if (messages.size &gt; 1) {</span>
<span class="fc" id="L282">                    println(&quot;   Context: ${messages.size - 1} previous messages&quot;)</span>
                }
<span class="fc bfc" id="L284" title="All 2 branches covered.">                if (stream) {</span>
<span class="fc" id="L285">                    println(&quot;   Streaming mode enabled&quot;)</span>
<span class="fc" id="L286">                    println()</span>

                    // Handle streaming response
<span class="fc" id="L289">                    val responseBuilder = StringBuilder()</span>
<span class="fc" id="L290">                    service.streamChat(request).collect { chunk -&gt;</span>
<span class="fc" id="L291">                        print(chunk.content)</span>
<span class="fc" id="L292">                        System.out.flush()</span>
<span class="fc" id="L293">                        responseBuilder.append(chunk.content)</span>

<span class="fc bfc" id="L295" title="All 2 branches covered.">                        if (chunk.finishReason != null) {</span>
<span class="fc" id="L296">                            println(&quot;\n&quot;)</span>
<span class="fc" id="L297">                            println(&quot;📊 Streaming response completed&quot;)</span>

                            // Add assistant response to history
<span class="fc" id="L300">                            historyManager.addMessage(</span>
<span class="fc" id="L301">                                conversationId = conversation.id,</span>
<span class="fc" id="L302">                                role = MessageRole.ASSISTANT,</span>
<span class="fc" id="L303">                                content = responseBuilder.toString()</span>
                            )
                        }
<span class="fc" id="L306">                    }</span>
                } else {
                    // Handle regular response
<span class="fc" id="L309">                    val response = service.chat(request)</span>
<span class="fc" id="L310">                    println(&quot;\n${response.content}&quot;)</span>
<span class="fc" id="L311">                    println(&quot;\n📊 Usage: ${response.usage.totalTokens} tokens&quot;)</span>

                    // Add assistant response to history
<span class="fc" id="L314">                    historyManager.addMessage(</span>
<span class="fc" id="L315">                        conversationId = conversation.id,</span>
<span class="fc" id="L316">                        role = MessageRole.ASSISTANT,</span>
<span class="fc" id="L317">                        content = response.content,</span>
<span class="fc" id="L318">                        tokenUsage = MessageTokenUsage(</span>
<span class="fc" id="L319">                            promptTokens = response.usage.promptTokens,</span>
<span class="fc" id="L320">                            completionTokens = response.usage.completionTokens,</span>
<span class="fc" id="L321">                            totalTokens = response.usage.totalTokens</span>
                        )
                    )
                }

<span class="fc" id="L326">                println(&quot;\n💾 Conversation ID: ${conversation.id.take(8)} (${conversation.messages.size} messages)&quot;)</span>

<span class="nc" id="L328">            } catch (e: Exception) {</span>
<span class="nc" id="L329">                println(&quot;❌ Error asking question: ${e.message}&quot;)</span>
            }
<span class="fc" id="L331">        }</span>
<span class="fc" id="L332">    }</span>

    private fun createNewConversation(question: String, config: AiServiceConfig) =
<span class="fc" id="L335">        historyManager.createConversation(</span>
<span class="fc" id="L336">            title = generateConversationTitle(question),</span>
<span class="fc" id="L337">            provider = config.provider,</span>
<span class="fc" id="L338">            model = config.model</span>
<span class="fc" id="L339">        )</span>

    private fun buildMessageHistory(conversation: com.aicodingcli.history.ConversationSession, currentQuestion: String): List&lt;AiMessage&gt; {
<span class="fc" id="L342">        val messages = mutableListOf&lt;AiMessage&gt;()</span>

        // Add previous messages (limit to last 10 to avoid token limits)
<span class="fc" id="L345">        val recentMessages = conversation.messages.takeLast(10)</span>
<span class="fc" id="L346">        recentMessages.forEach { historyMessage -&gt;</span>
<span class="fc" id="L347">            messages.add(AiMessage(</span>
<span class="fc" id="L348">                role = historyMessage.role,</span>
<span class="fc" id="L349">                content = historyMessage.content</span>
            ))
<span class="fc" id="L351">        }</span>

        // Add current question
<span class="fc" id="L354">        messages.add(AiMessage(</span>
<span class="fc" id="L355">            role = MessageRole.USER,</span>
<span class="fc" id="L356">            content = currentQuestion</span>
        ))

<span class="fc" id="L359">        return messages</span>
    }

    private fun generateConversationTitle(question: String): String {
        // Generate a meaningful title from the question
<span class="fc" id="L364">        val words = question.split(&quot; &quot;).take(5)</span>
<span class="pc bpc" id="L365" title="1 of 2 branches missed.">        return if (words.size &lt;= 5) {</span>
<span class="fc" id="L366">            question</span>
        } else {
<span class="nc" id="L368">            &quot;${words.joinToString(&quot; &quot;)}...&quot;</span>
        }
    }

<span class="fc" id="L372">    private suspend fun getProviderConfig(provider: AiProvider): AiServiceConfig {</span>
<span class="fc" id="L373">        val config = configManager.loadConfig()</span>
<span class="pc bpc" id="L374" title="1 of 2 branches missed.">        return config.providers[provider]</span>
<span class="nc" id="L375">            ?: createDefaultProviderConfig(provider)</span>
    }

    private fun createDefaultProviderConfig(provider: AiProvider): AiServiceConfig {
<span class="nc bnc" id="L379" title="All 3 branches missed.">        return when (provider) {</span>
<span class="nc" id="L380">            AiProvider.OPENAI -&gt; AiServiceConfig(</span>
<span class="nc" id="L381">                provider = AiProvider.OPENAI,</span>
<span class="nc" id="L382">                apiKey = &quot;your-openai-api-key&quot;,</span>
<span class="nc" id="L383">                model = &quot;gpt-3.5-turbo&quot;,</span>
<span class="nc" id="L384">                baseUrl = &quot;https://api.openai.com/v1&quot;,</span>
<span class="nc" id="L385">                temperature = 0.7f,</span>
<span class="nc" id="L386">                maxTokens = 1000</span>
            )
<span class="nc" id="L388">            AiProvider.CLAUDE -&gt; AiServiceConfig(</span>
<span class="nc" id="L389">                provider = AiProvider.CLAUDE,</span>
<span class="nc" id="L390">                apiKey = &quot;your-claude-api-key&quot;,</span>
<span class="nc" id="L391">                model = &quot;claude-3-sonnet-20240229&quot;,</span>
<span class="nc" id="L392">                baseUrl = &quot;https://api.anthropic.com/v1&quot;,</span>
<span class="nc" id="L393">                temperature = 0.7f,</span>
<span class="nc" id="L394">                maxTokens = 1000</span>
            )

<span class="nc" id="L397">            AiProvider.OLLAMA -&gt; AiServiceConfig(</span>
<span class="nc" id="L398">                provider = AiProvider.OLLAMA,</span>
<span class="nc" id="L399">                apiKey = &quot;not-required&quot;, // Ollama doesn't require API key but config validation needs non-empty string</span>
<span class="nc" id="L400">                model = &quot;llama2&quot;,</span>
<span class="nc" id="L401">                baseUrl = &quot;http://localhost:11434&quot;,</span>
<span class="nc" id="L402">                temperature = 0.7f,</span>
<span class="nc" id="L403">                maxTokens = 1000</span>
            )
        }
    }

    private fun handleConfigCommand(args: Array&lt;String&gt;) {
<span class="fc bfc" id="L409" title="All 4 branches covered.">        if (args.isEmpty()) {</span>
<span class="fc" id="L410">            printConfigHelp()</span>
<span class="fc" id="L411">            return</span>
        }

<span class="fc bfc" id="L414" title="All 5 branches covered.">        when (args[0]) {</span>
<span class="fc" id="L415">            &quot;set&quot; -&gt; handleConfigSet(args.drop(1).toTypedArray())</span>
<span class="fc" id="L416">            &quot;get&quot; -&gt; handleConfigGet(args.drop(1).toTypedArray())</span>
<span class="fc" id="L417">            &quot;list&quot; -&gt; handleConfigList()</span>
<span class="fc" id="L418">            &quot;provider&quot; -&gt; handleConfigProvider(args.drop(1).toTypedArray())</span>
            else -&gt; {
<span class="fc" id="L420">                println(&quot;Unknown config subcommand: ${args[0]}&quot;)</span>
<span class="fc" id="L421">                printConfigHelp()</span>
            }
        }
<span class="fc" id="L424">    }</span>

    private fun printConfigHelp() {
<span class="fc" id="L427">        println(&quot;&quot;&quot;</span>
            Configuration Management Commands:

            config set &lt;key&gt; &lt;value&gt;    Set a configuration value
            config get &lt;key&gt;            Get a configuration value
            config list                 List all configuration
            config provider &lt;name&gt;      Set default AI provider

            Examples:
            config set openai.api_key sk-...
            config set claude.api_key sk-ant-...
            config get openai.api_key
            config provider ollama
            config list
<span class="fc" id="L441">        &quot;&quot;&quot;.trimIndent())</span>
<span class="fc" id="L442">    }</span>

    private fun handleConfigSet(args: Array&lt;String&gt;) {
<span class="pc bpc" id="L445" title="1 of 2 branches missed.">        if (args.size &lt; 2) {</span>
<span class="fc" id="L446">            println(&quot;Usage: config set &lt;key&gt; &lt;value&gt;&quot;)</span>
<span class="fc" id="L447">            return</span>
        }

<span class="nc" id="L450">        val key = args[0]</span>
<span class="nc" id="L451">        val value = args.drop(1).joinToString(&quot; &quot;)</span>

<span class="nc" id="L453">        runBlocking {</span>
<span class="nc" id="L454">            try {</span>
<span class="nc" id="L455">                setConfigValue(key, value)</span>
<span class="nc" id="L456">                println(&quot;✅ Configuration updated: $key&quot;)</span>
<span class="nc" id="L457">            } catch (e: Exception) {</span>
<span class="nc" id="L458">                println(&quot;❌ Error setting configuration: ${e.message}&quot;)</span>
            }
<span class="nc" id="L460">        }</span>
<span class="nc" id="L461">    }</span>

    private fun handleConfigGet(args: Array&lt;String&gt;) {
<span class="pc bpc" id="L464" title="2 of 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="fc" id="L465">            println(&quot;Usage: config get &lt;key&gt;&quot;)</span>
<span class="fc" id="L466">            return</span>
        }

<span class="nc" id="L469">        val key = args[0]</span>

<span class="nc" id="L471">        runBlocking {</span>
<span class="nc" id="L472">            try {</span>
<span class="nc" id="L473">                val value = getConfigValue(key)</span>
<span class="nc bnc" id="L474" title="All 2 branches missed.">                if (value != null) {</span>
                    // Mask sensitive values
<span class="nc bnc" id="L476" title="All 4 branches missed.">                    val displayValue = if (key.contains(&quot;api_key&quot;) || key.contains(&quot;apikey&quot;)) {</span>
<span class="nc" id="L477">                        maskApiKey(value)</span>
                    } else {
<span class="nc" id="L479">                        value</span>
                    }
<span class="nc" id="L481">                    println(&quot;$key = $displayValue&quot;)</span>
                } else {
<span class="nc" id="L483">                    println(&quot;Configuration key '$key' not found&quot;)</span>
                }
<span class="nc" id="L485">            } catch (e: Exception) {</span>
<span class="nc" id="L486">                println(&quot;❌ Error getting configuration: ${e.message}&quot;)</span>
            }
<span class="nc" id="L488">        }</span>
<span class="nc" id="L489">    }</span>

    private fun handleConfigList() {
<span class="fc" id="L492">        runBlocking {</span>
<span class="fc" id="L493">            try {</span>
<span class="fc" id="L494">                val config = configManager.loadConfig()</span>
<span class="fc" id="L495">                println(&quot;Current Configuration:&quot;)</span>
<span class="fc" id="L496">                println(&quot;Default Provider: ${config.defaultProvider}&quot;)</span>
<span class="fc" id="L497">                println()</span>

<span class="fc" id="L499">                config.providers.forEach { (provider, providerConfig) -&gt;</span>
<span class="fc" id="L500">                    println(&quot;[$provider]&quot;)</span>
<span class="fc" id="L501">                    println(&quot;  api_key = ${maskApiKey(providerConfig.apiKey)}&quot;)</span>
<span class="fc" id="L502">                    println(&quot;  model = ${providerConfig.model}&quot;)</span>
<span class="pc bpc" id="L503" title="1 of 2 branches missed.">                    println(&quot;  base_url = ${providerConfig.baseUrl ?: &quot;default&quot;}&quot;)</span>
<span class="fc" id="L504">                    println(&quot;  temperature = ${providerConfig.temperature}&quot;)</span>
<span class="fc" id="L505">                    println(&quot;  max_tokens = ${providerConfig.maxTokens}&quot;)</span>
<span class="fc" id="L506">                    println()</span>
<span class="fc" id="L507">                }</span>
<span class="nc" id="L508">            } catch (e: Exception) {</span>
<span class="nc" id="L509">                println(&quot;❌ Error listing configuration: ${e.message}&quot;)</span>
            }
<span class="fc" id="L511">        }</span>
<span class="fc" id="L512">    }</span>

    private fun handleConfigProvider(args: Array&lt;String&gt;) {
<span class="pc bpc" id="L515" title="2 of 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="fc" id="L516">            println(&quot;Usage: config provider &lt;name&gt;&quot;)</span>
<span class="fc" id="L517">            println(&quot;Available providers: openai, claude, ollama&quot;)</span>
<span class="fc" id="L518">            return</span>
        }

<span class="nc" id="L521">        val providerName = args[0].lowercase()</span>
<span class="nc bnc" id="L522" title="All 4 branches missed.">        val provider = when (providerName) {</span>
<span class="nc" id="L523">            &quot;openai&quot; -&gt; AiProvider.OPENAI</span>
<span class="nc" id="L524">            &quot;claude&quot; -&gt; AiProvider.CLAUDE</span>
<span class="nc" id="L525">            &quot;ollama&quot; -&gt; AiProvider.OLLAMA</span>
            else -&gt; {
<span class="nc" id="L527">                println(&quot;Unknown provider: $providerName&quot;)</span>
<span class="nc" id="L528">                println(&quot;Available providers: openai, claude, ollama&quot;)</span>
<span class="nc" id="L529">                return</span>
            }
        }

<span class="nc" id="L533">        runBlocking {</span>
<span class="nc" id="L534">            try {</span>
<span class="nc" id="L535">                configManager.setDefaultProvider(provider)</span>
<span class="nc" id="L536">                println(&quot;✅ Default provider set to: $provider&quot;)</span>
<span class="nc" id="L537">            } catch (e: Exception) {</span>
<span class="nc" id="L538">                println(&quot;❌ Error setting default provider: ${e.message}&quot;)</span>
            }
<span class="nc" id="L540">        }</span>
<span class="nc" id="L541">    }</span>

<span class="nc" id="L543">    private suspend fun setConfigValue(key: String, value: String) {</span>
<span class="nc" id="L544">        val parts = key.split(&quot;.&quot;)</span>
<span class="nc bnc" id="L545" title="All 2 branches missed.">        if (parts.size != 2) {</span>
<span class="nc" id="L546">            throw IllegalArgumentException(&quot;Key must be in format: provider.property (e.g., openai.api_key)&quot;)</span>
        }

<span class="nc" id="L549">        val providerName = parts[0].lowercase()</span>
<span class="nc" id="L550">        val property = parts[1].lowercase()</span>

<span class="nc bnc" id="L552" title="All 4 branches missed.">        val provider = when (providerName) {</span>
<span class="nc" id="L553">            &quot;openai&quot; -&gt; AiProvider.OPENAI</span>
<span class="nc" id="L554">            &quot;claude&quot; -&gt; AiProvider.CLAUDE</span>
<span class="nc" id="L555">            &quot;ollama&quot; -&gt; AiProvider.OLLAMA</span>
<span class="nc" id="L556">            else -&gt; throw IllegalArgumentException(&quot;Unknown provider: $providerName&quot;)</span>
        }

<span class="nc" id="L559">        val config = configManager.loadConfig()</span>
<span class="nc bnc" id="L560" title="All 2 branches missed.">        val currentProviderConfig = config.providers[provider] ?: createDefaultProviderConfig(provider)</span>

<span class="nc bnc" id="L562" title="All 6 branches missed.">        val updatedConfig = when (property) {</span>
<span class="nc" id="L563">            &quot;api_key&quot;, &quot;apikey&quot; -&gt; currentProviderConfig.copy(apiKey = value)</span>
<span class="nc" id="L564">            &quot;model&quot; -&gt; currentProviderConfig.copy(model = value)</span>
<span class="nc" id="L565">            &quot;base_url&quot;, &quot;baseurl&quot; -&gt; currentProviderConfig.copy(baseUrl = value)</span>
<span class="nc bnc" id="L566" title="All 2 branches missed.">            &quot;temperature&quot; -&gt; currentProviderConfig.copy(temperature = value.toFloatOrNull()</span>
<span class="nc" id="L567">                ?: throw IllegalArgumentException(&quot;Temperature must be a number&quot;))</span>
<span class="nc bnc" id="L568" title="All 2 branches missed.">            &quot;max_tokens&quot;, &quot;maxtokens&quot; -&gt; currentProviderConfig.copy(maxTokens = value.toIntOrNull()</span>
<span class="nc" id="L569">                ?: throw IllegalArgumentException(&quot;Max tokens must be a number&quot;))</span>
<span class="nc" id="L570">            else -&gt; throw IllegalArgumentException(&quot;Unknown property: $property&quot;)</span>
        }

<span class="nc" id="L573">        configManager.updateProviderConfig(provider, updatedConfig)</span>
<span class="nc" id="L574">    }</span>

<span class="nc" id="L576">    private suspend fun getConfigValue(key: String): String? {</span>
<span class="nc" id="L577">        val parts = key.split(&quot;.&quot;)</span>
<span class="nc bnc" id="L578" title="All 2 branches missed.">        if (parts.size != 2) {</span>
<span class="nc" id="L579">            return null</span>
        }

<span class="nc" id="L582">        val providerName = parts[0].lowercase()</span>
<span class="nc" id="L583">        val property = parts[1].lowercase()</span>

<span class="nc bnc" id="L585" title="All 10 branches missed.">        val provider = when (providerName) {</span>
<span class="nc" id="L586">            &quot;openai&quot; -&gt; AiProvider.OPENAI</span>
<span class="nc" id="L587">            &quot;claude&quot; -&gt; AiProvider.CLAUDE</span>
<span class="nc" id="L588">            &quot;ollama&quot; -&gt; AiProvider.OLLAMA</span>
<span class="nc" id="L589">            else -&gt; return null</span>
        }

<span class="nc" id="L592">        val config = configManager.loadConfig()</span>
<span class="nc bnc" id="L593" title="All 2 branches missed.">        val providerConfig = config.providers[provider] ?: return null</span>

<span class="nc bnc" id="L595" title="All 25 branches missed.">        return when (property) {</span>
<span class="nc" id="L596">            &quot;api_key&quot;, &quot;apikey&quot; -&gt; providerConfig.apiKey</span>
<span class="nc" id="L597">            &quot;model&quot; -&gt; providerConfig.model</span>
<span class="nc" id="L598">            &quot;base_url&quot;, &quot;baseurl&quot; -&gt; providerConfig.baseUrl</span>
<span class="nc" id="L599">            &quot;temperature&quot; -&gt; providerConfig.temperature.toString()</span>
<span class="nc" id="L600">            &quot;max_tokens&quot;, &quot;maxtokens&quot; -&gt; providerConfig.maxTokens.toString()</span>
<span class="nc" id="L601">            else -&gt; null</span>
        }
    }

    private fun maskApiKey(apiKey: String): String {
<span class="pc bpc" id="L606" title="1 of 2 branches missed.">        return if (apiKey.length &gt; 8) {</span>
<span class="fc" id="L607">            &quot;${apiKey.take(4)}${&quot;*&quot;.repeat(apiKey.length - 8)}${apiKey.takeLast(4)}&quot;</span>
        } else {
<span class="nc" id="L609">            &quot;*&quot;.repeat(apiKey.length)</span>
        }
    }

    private fun handleHistoryCommand(args: Array&lt;String&gt;) {
<span class="fc bfc" id="L614" title="All 4 branches covered.">        if (args.isEmpty()) {</span>
<span class="fc" id="L615">            printHistoryHelp()</span>
<span class="fc" id="L616">            return</span>
        }

<span class="pc bpc" id="L619" title="1 of 7 branches missed.">        when (args[0]) {</span>
<span class="fc" id="L620">            &quot;list&quot; -&gt; handleHistoryList(args.drop(1).toTypedArray())</span>
<span class="fc" id="L621">            &quot;show&quot; -&gt; handleHistoryShow(args.drop(1).toTypedArray())</span>
<span class="fc" id="L622">            &quot;search&quot; -&gt; handleHistorySearch(args.drop(1).toTypedArray())</span>
<span class="fc" id="L623">            &quot;delete&quot; -&gt; handleHistoryDelete(args.drop(1).toTypedArray())</span>
<span class="nc" id="L624">            &quot;clear&quot; -&gt; handleHistoryClear()</span>
<span class="fc" id="L625">            &quot;stats&quot; -&gt; handleHistoryStats()</span>
            else -&gt; {
<span class="fc" id="L627">                println(&quot;Unknown history subcommand: ${args[0]}&quot;)</span>
<span class="fc" id="L628">                printHistoryHelp()</span>
            }
        }
<span class="fc" id="L631">    }</span>

    private fun printHistoryHelp() {
<span class="fc" id="L634">        println(&quot;&quot;&quot;</span>
            Conversation History Management Commands:

            history list [--limit N]        List recent conversations
            history show &lt;id&gt;               Show conversation details
            history search &lt;query&gt;          Search conversations
            history delete &lt;id&gt;             Delete a conversation
            history clear                   Clear all conversations
            history stats                   Show history statistics

            Examples:
            history list --limit 10
            history show abc123
            history search &quot;kotlin&quot;
            history delete abc123
<span class="fc" id="L649">        &quot;&quot;&quot;.trimIndent())</span>
<span class="fc" id="L650">    }</span>

    private fun handleHistoryList(args: Array&lt;String&gt;) {
<span class="fc" id="L653">        var limit = 20</span>

        // Parse limit argument
<span class="fc" id="L656">        var i = 0</span>
<span class="fc bfc" id="L657" title="All 2 branches covered.">        while (i &lt; args.size) {</span>
<span class="fc" id="L658">            when (args[i]) {</span>
<span class="pc bpc" id="L659" title="1 of 2 branches missed.">                &quot;--limit&quot; -&gt; {</span>
<span class="pc bpc" id="L660" title="1 of 2 branches missed.">                    if (i + 1 &lt; args.size) {</span>
<span class="pc bpc" id="L661" title="1 of 2 branches missed.">                        limit = args[i + 1].toIntOrNull() ?: 20</span>
<span class="fc" id="L662">                        i += 2</span>
                    } else {
<span class="nc" id="L664">                        println(&quot;--limit requires a value&quot;)</span>
<span class="nc" id="L665">                        return</span>
                    }
                }
<span class="nc" id="L668">                else -&gt; i++</span>
            }
        }

<span class="fc" id="L672">        val conversations = historyManager.getAllConversations().take(limit)</span>

<span class="pc bpc" id="L674" title="1 of 2 branches missed.">        if (conversations.isEmpty()) {</span>
<span class="nc" id="L675">            println(&quot;No conversation history found.&quot;)</span>
<span class="nc" id="L676">            return</span>
        }

<span class="fc" id="L679">        println(&quot;Recent Conversations:&quot;)</span>
<span class="fc" id="L680">        println(&quot;=&quot; * 60)</span>

<span class="fc" id="L682">        conversations.forEach { conversation -&gt;</span>
<span class="fc" id="L683">            val date = formatTimestamp(conversation.updatedAt)</span>
<span class="fc" id="L684">            println(&quot;ID: ${conversation.id.take(8)}&quot;)</span>
<span class="fc" id="L685">            println(&quot;Title: ${conversation.title}&quot;)</span>
<span class="fc" id="L686">            println(&quot;Provider: ${conversation.provider} (${conversation.model})&quot;)</span>
<span class="fc" id="L687">            println(&quot;Updated: $date&quot;)</span>
<span class="fc" id="L688">            println(&quot;Messages: ${conversation.messages.size}&quot;)</span>
<span class="fc" id="L689">            println(&quot;Summary: ${conversation.getSummary()}&quot;)</span>
<span class="fc" id="L690">            println(&quot;-&quot; * 40)</span>
<span class="fc" id="L691">        }</span>
<span class="fc" id="L692">    }</span>

    private fun handleHistoryShow(args: Array&lt;String&gt;) {
<span class="fc bfc" id="L695" title="All 4 branches covered.">        if (args.isEmpty()) {</span>
<span class="fc" id="L696">            println(&quot;Usage: history show &lt;conversation-id&gt;&quot;)</span>
<span class="fc" id="L697">            return</span>
        }

<span class="fc" id="L700">        val conversationId = args[0]</span>
<span class="fc" id="L701">        val conversation = historyManager.getConversation(conversationId)</span>

<span class="pc bpc" id="L703" title="1 of 2 branches missed.">        if (conversation == null) {</span>
<span class="fc" id="L704">            println(&quot;Conversation not found: $conversationId&quot;)</span>
<span class="fc" id="L705">            return</span>
        }

<span class="nc" id="L708">        println(&quot;Conversation: ${conversation.title}&quot;)</span>
<span class="nc" id="L709">        println(&quot;ID: ${conversation.id}&quot;)</span>
<span class="nc" id="L710">        println(&quot;Provider: ${conversation.provider} (${conversation.model})&quot;)</span>
<span class="nc" id="L711">        println(&quot;Created: ${formatTimestamp(conversation.createdAt)}&quot;)</span>
<span class="nc" id="L712">        println(&quot;Updated: ${formatTimestamp(conversation.updatedAt)}&quot;)</span>
<span class="nc" id="L713">        println(&quot;Messages: ${conversation.messages.size}&quot;)</span>
<span class="nc" id="L714">        println(&quot;=&quot; * 60)</span>

<span class="nc" id="L716">        conversation.messages.forEach { message -&gt;</span>
<span class="nc" id="L717">            val timestamp = formatTimestamp(message.timestamp)</span>
<span class="nc bnc" id="L718" title="All 3 branches missed.">            val role = when (message.role) {</span>
<span class="nc" id="L719">                MessageRole.USER -&gt; &quot;👤 User&quot;</span>
<span class="nc" id="L720">                MessageRole.ASSISTANT -&gt; &quot;🤖 Assistant&quot;</span>
<span class="nc" id="L721">                MessageRole.SYSTEM -&gt; &quot;⚙️ System&quot;</span>
            }

<span class="nc" id="L724">            println(&quot;[$timestamp] $role:&quot;)</span>
<span class="nc" id="L725">            println(message.content)</span>

<span class="nc bnc" id="L727" title="All 2 branches missed.">            message.tokenUsage?.let { usage -&gt;</span>
<span class="nc" id="L728">                println(&quot;📊 Tokens: ${usage.totalTokens} (prompt: ${usage.promptTokens}, completion: ${usage.completionTokens})&quot;)</span>
<span class="nc" id="L729">            }</span>

<span class="nc" id="L731">            println(&quot;-&quot; * 40)</span>
<span class="nc" id="L732">        }</span>
<span class="nc" id="L733">    }</span>

    private fun handleHistorySearch(args: Array&lt;String&gt;) {
<span class="fc bfc" id="L736" title="All 4 branches covered.">        if (args.isEmpty()) {</span>
<span class="fc" id="L737">            println(&quot;Usage: history search &lt;query&gt;&quot;)</span>
<span class="fc" id="L738">            return</span>
        }

<span class="fc" id="L741">        val query = args.joinToString(&quot; &quot;)</span>
<span class="fc" id="L742">        val criteria = HistorySearchCriteria(query = query, limit = 20)</span>
<span class="fc" id="L743">        val conversations = historyManager.searchConversations(criteria)</span>

<span class="pc bpc" id="L745" title="1 of 2 branches missed.">        if (conversations.isEmpty()) {</span>
<span class="nc" id="L746">            println(&quot;No conversations found matching: $query&quot;)</span>
<span class="nc" id="L747">            return</span>
        }

<span class="fc" id="L750">        println(&quot;Search Results for: $query&quot;)</span>
<span class="fc" id="L751">        println(&quot;=&quot; * 60)</span>

<span class="fc" id="L753">        conversations.forEach { conversation -&gt;</span>
<span class="fc" id="L754">            val date = formatTimestamp(conversation.updatedAt)</span>
<span class="fc" id="L755">            println(&quot;ID: ${conversation.id.take(8)}&quot;)</span>
<span class="fc" id="L756">            println(&quot;Title: ${conversation.title}&quot;)</span>
<span class="fc" id="L757">            println(&quot;Provider: ${conversation.provider} (${conversation.model})&quot;)</span>
<span class="fc" id="L758">            println(&quot;Updated: $date&quot;)</span>
<span class="fc" id="L759">            println(&quot;Summary: ${conversation.getSummary()}&quot;)</span>
<span class="fc" id="L760">            println(&quot;-&quot; * 40)</span>
<span class="fc" id="L761">        }</span>
<span class="fc" id="L762">    }</span>

    private fun handleHistoryDelete(args: Array&lt;String&gt;) {
<span class="fc bfc" id="L765" title="All 4 branches covered.">        if (args.isEmpty()) {</span>
<span class="fc" id="L766">            println(&quot;Usage: history delete &lt;conversation-id&gt;&quot;)</span>
<span class="fc" id="L767">            return</span>
        }

<span class="fc" id="L770">        val conversationId = args[0]</span>
<span class="fc" id="L771">        val deleted = historyManager.deleteConversation(conversationId)</span>

<span class="pc bpc" id="L773" title="1 of 2 branches missed.">        if (deleted) {</span>
<span class="nc" id="L774">            println(&quot;✅ Conversation deleted: $conversationId&quot;)</span>
        } else {
<span class="fc" id="L776">            println(&quot;❌ Conversation not found: $conversationId&quot;)</span>
        }
<span class="fc" id="L778">    }</span>

    private fun handleHistoryClear() {
<span class="nc" id="L781">        print(&quot;Are you sure you want to clear all conversation history? (y/N): &quot;)</span>
<span class="nc bnc" id="L782" title="All 2 branches missed.">        val confirmation = readlnOrNull()?.lowercase()</span>

<span class="nc bnc" id="L784" title="All 4 branches missed.">        if (confirmation == &quot;y&quot; || confirmation == &quot;yes&quot;) {</span>
<span class="nc" id="L785">            historyManager.clearAllConversations()</span>
<span class="nc" id="L786">            println(&quot;✅ All conversation history cleared.&quot;)</span>
        } else {
<span class="nc" id="L788">            println(&quot;Operation cancelled.&quot;)</span>
        }
<span class="nc" id="L790">    }</span>

    private fun handleHistoryStats() {
<span class="fc" id="L793">        val stats = historyManager.getStatistics()</span>

<span class="fc" id="L795">        println(&quot;Conversation History Statistics:&quot;)</span>
<span class="fc" id="L796">        println(&quot;=&quot; * 40)</span>
<span class="fc" id="L797">        println(&quot;Total Conversations: ${stats.totalConversations}&quot;)</span>
<span class="fc" id="L798">        println(&quot;Total Messages: ${stats.totalMessages}&quot;)</span>
<span class="fc" id="L799">        println(&quot;Total Tokens Used: ${stats.totalTokensUsed}&quot;)</span>
<span class="fc" id="L800">        println()</span>

<span class="pc bpc" id="L802" title="2 of 4 branches missed.">        if (stats.providerBreakdown.isNotEmpty()) {</span>
<span class="fc" id="L803">            println(&quot;Provider Breakdown:&quot;)</span>
<span class="fc" id="L804">            stats.providerBreakdown.forEach { (provider, count) -&gt;</span>
<span class="fc" id="L805">                println(&quot;  $provider: $count conversations&quot;)</span>
<span class="fc" id="L806">            }</span>
<span class="fc" id="L807">            println()</span>
        }

<span class="pc bpc" id="L810" title="1 of 2 branches missed.">        stats.oldestConversation?.let { oldest -&gt;</span>
<span class="fc" id="L811">            println(&quot;Oldest Conversation: ${formatTimestamp(oldest)}&quot;)</span>
<span class="fc" id="L812">        }</span>

<span class="pc bpc" id="L814" title="1 of 2 branches missed.">        stats.newestConversation?.let { newest -&gt;</span>
<span class="fc" id="L815">            println(&quot;Newest Conversation: ${formatTimestamp(newest)}&quot;)</span>
<span class="fc" id="L816">        }</span>
<span class="fc" id="L817">    }</span>

    private fun formatTimestamp(epochSecond: Long): String {
<span class="fc" id="L820">        val dateTime = LocalDateTime.ofInstant(</span>
<span class="fc" id="L821">            Instant.ofEpochSecond(epochSecond),</span>
<span class="fc" id="L822">            ZoneId.systemDefault()</span>
        )
<span class="fc" id="L824">        return dateTime.format(DateTimeFormatter.ofPattern(&quot;yyyy-MM-dd HH:mm:ss&quot;))</span>
    }

<span class="fc" id="L827">    private operator fun String.times(n: Int): String = this.repeat(n)</span>

    // Code Analysis Commands
    private fun handleAnalyzeCommand(args: Array&lt;String&gt;) {
<span class="fc bfc" id="L831" title="All 4 branches covered.">        if (args.isEmpty()) {</span>
<span class="fc" id="L832">            printAnalyzeHelp()</span>
<span class="fc" id="L833">            return</span>
        }

<span class="pc bpc" id="L836" title="1 of 5 branches missed.">        when (args[0]) {</span>
<span class="fc" id="L837">            &quot;file&quot; -&gt; handleAnalyzeFile(args.drop(1).toTypedArray())</span>
<span class="fc" id="L838">            &quot;project&quot; -&gt; handleAnalyzeProject(args.drop(1).toTypedArray())</span>
<span class="fc" id="L839">            &quot;metrics&quot; -&gt; handleAnalyzeMetrics(args.drop(1).toTypedArray())</span>
<span class="fc" id="L840">            &quot;issues&quot; -&gt; handleAnalyzeIssues(args.drop(1).toTypedArray())</span>
            else -&gt; {
<span class="nc" id="L842">                println(&quot;Unknown analyze subcommand: ${args[0]}&quot;)</span>
<span class="nc" id="L843">                printAnalyzeHelp()</span>
            }
        }
<span class="fc" id="L846">    }</span>

    private fun printAnalyzeHelp() {
<span class="fc" id="L849">        println(&quot;&quot;&quot;</span>
            Code Analysis Commands:

            analyze file &lt;file-path&gt;        Analyze a single file
            analyze project &lt;project-path&gt;  Analyze an entire project
            analyze metrics &lt;file-path&gt;     Show code metrics for a file
            analyze issues &lt;file-path&gt;      Show code issues for a file

            Options:
            --format &lt;format&gt;               Output format (text, json) [default: text]
            --language &lt;lang&gt;               Force language detection (kotlin, java, python)

            Examples:
            analyze file src/main/kotlin/Main.kt
            analyze project src/main/kotlin
            analyze metrics --format json src/main/kotlin/Main.kt
            analyze issues src/main/kotlin/Main.kt
<span class="fc" id="L866">        &quot;&quot;&quot;.trimIndent())</span>
<span class="fc" id="L867">    }</span>

    private fun handleAnalyzeFile(args: Array&lt;String&gt;) {
<span class="pc bpc" id="L870" title="2 of 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L871">            println(&quot;Usage: analyze file &lt;file-path&gt; [--format &lt;format&gt;] [--language &lt;lang&gt;]&quot;)</span>
<span class="nc" id="L872">            return</span>
        }

<span class="fc" id="L875">        val filePath = args[0]</span>
<span class="fc" id="L876">        val options = parseAnalyzeOptions(args.drop(1).toTypedArray())</span>

<span class="fc" id="L878">        runBlocking {</span>
<span class="fc" id="L879">            try {</span>
<span class="fc" id="L880">                val result = codeAnalyzer.analyzeFile(filePath)</span>
<span class="fc" id="L881">                displayAnalysisResult(result, options.format)</span>
<span class="nc" id="L882">            } catch (e: Exception) {</span>
<span class="nc" id="L883">                println(&quot;❌ Error analyzing file: ${e.message}&quot;)</span>
            }
<span class="fc" id="L885">        }</span>
<span class="fc" id="L886">    }</span>

    private fun handleAnalyzeProject(args: Array&lt;String&gt;) {
<span class="pc bpc" id="L889" title="2 of 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L890">            println(&quot;Usage: analyze project &lt;project-path&gt; [--format &lt;format&gt;]&quot;)</span>
<span class="nc" id="L891">            return</span>
        }

<span class="fc" id="L894">        val projectPath = args[0]</span>
<span class="fc" id="L895">        val options = parseAnalyzeOptions(args.drop(1).toTypedArray())</span>

<span class="fc" id="L897">        runBlocking {</span>
<span class="fc" id="L898">            try {</span>
<span class="fc" id="L899">                val result = codeAnalyzer.analyzeProject(projectPath)</span>
<span class="fc" id="L900">                displayProjectAnalysisResult(result, options.format)</span>
<span class="nc" id="L901">            } catch (e: Exception) {</span>
<span class="nc" id="L902">                println(&quot;❌ Error analyzing project: ${e.message}&quot;)</span>
            }
<span class="fc" id="L904">        }</span>
<span class="fc" id="L905">    }</span>

    private fun handleAnalyzeMetrics(args: Array&lt;String&gt;) {
<span class="pc bpc" id="L908" title="2 of 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L909">            println(&quot;Usage: analyze metrics &lt;file-path&gt; [--format &lt;format&gt;]&quot;)</span>
<span class="nc" id="L910">            return</span>
        }

<span class="fc" id="L913">        val filePath = args[0]</span>
<span class="fc" id="L914">        val options = parseAnalyzeOptions(args.drop(1).toTypedArray())</span>

<span class="fc" id="L916">        runBlocking {</span>
<span class="fc" id="L917">            try {</span>
<span class="fc" id="L918">                val result = codeAnalyzer.analyzeFile(filePath)</span>
<span class="fc" id="L919">                displayMetrics(result.metrics, options.format)</span>
<span class="nc" id="L920">            } catch (e: Exception) {</span>
<span class="nc" id="L921">                println(&quot;❌ Error analyzing metrics: ${e.message}&quot;)</span>
            }
<span class="fc" id="L923">        }</span>
<span class="fc" id="L924">    }</span>

    private fun handleAnalyzeIssues(args: Array&lt;String&gt;) {
<span class="pc bpc" id="L927" title="2 of 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L928">            println(&quot;Usage: analyze issues &lt;file-path&gt; [--format &lt;format&gt;]&quot;)</span>
<span class="nc" id="L929">            return</span>
        }

<span class="fc" id="L932">        val filePath = args[0]</span>
<span class="fc" id="L933">        val options = parseAnalyzeOptions(args.drop(1).toTypedArray())</span>

<span class="fc" id="L935">        runBlocking {</span>
<span class="fc" id="L936">            try {</span>
<span class="fc" id="L937">                val result = codeAnalyzer.analyzeFile(filePath)</span>
<span class="fc" id="L938">                displayIssues(result.issues, options.format)</span>
<span class="nc" id="L939">            } catch (e: Exception) {</span>
<span class="nc" id="L940">                println(&quot;❌ Error analyzing issues: ${e.message}&quot;)</span>
            }
<span class="fc" id="L942">        }</span>
<span class="fc" id="L943">    }</span>

<span class="pc" id="L945">    private data class AnalyzeOptions(</span>
<span class="pc" id="L946">        val format: String = &quot;text&quot;,</span>
<span class="pc" id="L947">        val language: ProgrammingLanguage? = null</span>
<span class="nc" id="L948">    )</span>

    private fun parseAnalyzeOptions(args: Array&lt;String&gt;): AnalyzeOptions {
<span class="fc" id="L951">        var format = &quot;text&quot;</span>
<span class="fc" id="L952">        var language: ProgrammingLanguage? = null</span>

<span class="fc" id="L954">        var i = 0</span>
<span class="fc bfc" id="L955" title="All 2 branches covered.">        while (i &lt; args.size) {</span>
<span class="fc" id="L956">            when (args[i]) {</span>
<span class="pc bpc" id="L957" title="1 of 2 branches missed.">                &quot;--format&quot; -&gt; {</span>
<span class="pc bpc" id="L958" title="1 of 2 branches missed.">                    if (i + 1 &lt; args.size) {</span>
<span class="fc" id="L959">                        format = args[i + 1]</span>
<span class="fc" id="L960">                        i += 2</span>
                    } else {
<span class="nc" id="L962">                        println(&quot;--format requires a value&quot;)</span>
<span class="nc" id="L963">                        i++</span>
                    }
                }
<span class="nc bnc" id="L966" title="All 2 branches missed.">                &quot;--language&quot; -&gt; {</span>
<span class="nc bnc" id="L967" title="All 2 branches missed.">                    if (i + 1 &lt; args.size) {</span>
<span class="nc bnc" id="L968" title="All 6 branches missed.">                        language = when (args[i + 1].lowercase()) {</span>
<span class="nc" id="L969">                            &quot;kotlin&quot; -&gt; ProgrammingLanguage.KOTLIN</span>
<span class="nc" id="L970">                            &quot;java&quot; -&gt; ProgrammingLanguage.JAVA</span>
<span class="nc" id="L971">                            &quot;python&quot; -&gt; ProgrammingLanguage.PYTHON</span>
<span class="nc" id="L972">                            &quot;javascript&quot; -&gt; ProgrammingLanguage.JAVASCRIPT</span>
<span class="nc" id="L973">                            &quot;typescript&quot; -&gt; ProgrammingLanguage.TYPESCRIPT</span>
                            else -&gt; {
<span class="nc" id="L975">                                println(&quot;Unknown language: ${args[i + 1]}&quot;)</span>
<span class="nc" id="L976">                                null</span>
                            }
                        }
<span class="nc" id="L979">                        i += 2</span>
                    } else {
<span class="nc" id="L981">                        println(&quot;--language requires a value&quot;)</span>
<span class="nc" id="L982">                        i++</span>
                    }
                }
<span class="nc" id="L985">                else -&gt; i++</span>
            }
        }

<span class="fc" id="L989">        return AnalyzeOptions(format, language)</span>
    }

    private fun displayAnalysisResult(result: com.aicodingcli.code.analysis.CodeAnalysisResult, format: String) {
<span class="fc" id="L993">        when (format.lowercase()) {</span>
<span class="pc bpc" id="L994" title="1 of 2 branches missed.">            &quot;json&quot; -&gt; displayAnalysisResultJson(result)</span>
<span class="fc" id="L995">            else -&gt; displayAnalysisResultText(result)</span>
        }
<span class="fc" id="L997">    }</span>

    private fun displayAnalysisResultText(result: com.aicodingcli.code.analysis.CodeAnalysisResult) {
<span class="fc" id="L1000">        println(&quot;📊 Code Analysis Results&quot;)</span>
<span class="fc" id="L1001">        println(&quot;=&quot; * 50)</span>
<span class="fc" id="L1002">        println(&quot;File: ${result.filePath}&quot;)</span>
<span class="fc" id="L1003">        println(&quot;Language: ${result.language.displayName}&quot;)</span>
<span class="fc" id="L1004">        println()</span>

        // Display metrics
<span class="fc" id="L1007">        println(&quot;📈 Metrics:&quot;)</span>
<span class="fc" id="L1008">        println(&quot;  Lines of Code: ${result.metrics.linesOfCode}&quot;)</span>
<span class="fc" id="L1009">        println(&quot;  Cyclomatic Complexity: ${result.metrics.cyclomaticComplexity}&quot;)</span>
<span class="fc" id="L1010">        println(&quot;  Maintainability Index: ${&quot;%.1f&quot;.format(result.metrics.maintainabilityIndex)}&quot;)</span>
<span class="pc bpc" id="L1011" title="1 of 2 branches missed.">        result.metrics.testCoverage?.let { coverage -&gt;</span>
<span class="nc" id="L1012">            println(&quot;  Test Coverage: ${&quot;%.1f&quot;.format(coverage)}%&quot;)</span>
<span class="nc" id="L1013">        }</span>
<span class="fc" id="L1014">        println(&quot;  Duplicated Lines: ${result.metrics.duplicatedLines}&quot;)</span>
<span class="fc" id="L1015">        println()</span>

        // Display issues
<span class="pc bpc" id="L1018" title="2 of 4 branches missed.">        if (result.issues.isNotEmpty()) {</span>
<span class="nc" id="L1019">            println(&quot;⚠️  Issues (${result.issues.size}):&quot;)</span>
<span class="nc" id="L1020">            result.issues.forEach { issue -&gt;</span>
<span class="nc bnc" id="L1021" title="All 4 branches missed.">                val severity = when (issue.severity) {</span>
<span class="nc" id="L1022">                    com.aicodingcli.code.analysis.IssueSeverity.CRITICAL -&gt; &quot;🔴 CRITICAL&quot;</span>
<span class="nc" id="L1023">                    com.aicodingcli.code.analysis.IssueSeverity.HIGH -&gt; &quot;🟠 HIGH&quot;</span>
<span class="nc" id="L1024">                    com.aicodingcli.code.analysis.IssueSeverity.MEDIUM -&gt; &quot;🟡 MEDIUM&quot;</span>
<span class="nc" id="L1025">                    com.aicodingcli.code.analysis.IssueSeverity.LOW -&gt; &quot;🟢 LOW&quot;</span>
                }
<span class="nc bnc" id="L1027" title="All 2 branches missed.">                val location = if (issue.line != null) &quot; (line ${issue.line})&quot; else &quot;&quot;</span>
<span class="nc" id="L1028">                println(&quot;  $severity: ${issue.message}$location&quot;)</span>
<span class="nc bnc" id="L1029" title="All 2 branches missed.">                issue.suggestion?.let { suggestion -&gt;</span>
<span class="nc" id="L1030">                    println(&quot;    💡 Suggestion: $suggestion&quot;)</span>
<span class="nc" id="L1031">                }</span>
<span class="nc" id="L1032">            }</span>
<span class="nc" id="L1033">            println()</span>
        }

        // Display suggestions
<span class="pc bpc" id="L1037" title="2 of 4 branches missed.">        if (result.suggestions.isNotEmpty()) {</span>
<span class="fc" id="L1038">            println(&quot;💡 Improvement Suggestions (${result.suggestions.size}):&quot;)</span>
<span class="fc" id="L1039">            result.suggestions.forEach { suggestion -&gt;</span>
<span class="pc bpc" id="L1040" title="2 of 3 branches missed.">                val priority = when (suggestion.priority) {</span>
<span class="nc" id="L1041">                    com.aicodingcli.code.analysis.ImprovementPriority.HIGH -&gt; &quot;🔴 HIGH&quot;</span>
<span class="nc" id="L1042">                    com.aicodingcli.code.analysis.ImprovementPriority.MEDIUM -&gt; &quot;🟡 MEDIUM&quot;</span>
<span class="fc" id="L1043">                    com.aicodingcli.code.analysis.ImprovementPriority.LOW -&gt; &quot;🟢 LOW&quot;</span>
                }
<span class="pc bpc" id="L1045" title="1 of 2 branches missed.">                val location = if (suggestion.line != null) &quot; (line ${suggestion.line})&quot; else &quot;&quot;</span>
<span class="fc" id="L1046">                println(&quot;  $priority: ${suggestion.description}$location&quot;)</span>
<span class="fc" id="L1047">            }</span>
<span class="fc" id="L1048">            println()</span>
        }

        // Display dependencies
<span class="pc bpc" id="L1052" title="2 of 4 branches missed.">        if (result.dependencies.isNotEmpty()) {</span>
<span class="nc" id="L1053">            println(&quot;📦 Dependencies (${result.dependencies.size}):&quot;)</span>
<span class="nc" id="L1054">            result.dependencies.forEach { dependency -&gt;</span>
<span class="nc bnc" id="L1055" title="All 2 branches missed.">                println(&quot;  ${dependency.name} (${dependency.version ?: &quot;unknown&quot;}) - ${dependency.type}&quot;)</span>
<span class="nc" id="L1056">            }</span>
        }
<span class="fc" id="L1058">    }</span>

    private fun displayAnalysisResultJson(result: com.aicodingcli.code.analysis.CodeAnalysisResult) {
        // Simple JSON output - in a real implementation, you'd use a JSON library
<span class="nc" id="L1062">        println(&quot;&quot;&quot;{</span>
<span class="nc" id="L1063">  &quot;filePath&quot;: &quot;${result.filePath}&quot;,</span>
<span class="nc" id="L1064">  &quot;language&quot;: &quot;${result.language.displayName}&quot;,</span>
  &quot;metrics&quot;: {
<span class="nc" id="L1066">    &quot;linesOfCode&quot;: ${result.metrics.linesOfCode},</span>
<span class="nc" id="L1067">    &quot;cyclomaticComplexity&quot;: ${result.metrics.cyclomaticComplexity},</span>
<span class="nc" id="L1068">    &quot;maintainabilityIndex&quot;: ${result.metrics.maintainabilityIndex},</span>
<span class="nc" id="L1069">    &quot;testCoverage&quot;: ${result.metrics.testCoverage},</span>
<span class="nc" id="L1070">    &quot;duplicatedLines&quot;: ${result.metrics.duplicatedLines}</span>
  },
<span class="nc" id="L1072">  &quot;issuesCount&quot;: ${result.issues.size},</span>
<span class="nc" id="L1073">  &quot;suggestionsCount&quot;: ${result.suggestions.size},</span>
<span class="nc" id="L1074">  &quot;dependenciesCount&quot;: ${result.dependencies.size}</span>
}&quot;&quot;&quot;)
<span class="nc" id="L1076">    }</span>

    private fun displayProjectAnalysisResult(result: com.aicodingcli.code.analysis.ProjectAnalysisResult, format: String) {
<span class="fc" id="L1079">        when (format.lowercase()) {</span>
<span class="pc bpc" id="L1080" title="1 of 2 branches missed.">            &quot;json&quot; -&gt; displayProjectAnalysisResultJson(result)</span>
<span class="fc" id="L1081">            else -&gt; displayProjectAnalysisResultText(result)</span>
        }
<span class="fc" id="L1083">    }</span>

    private fun displayProjectAnalysisResultText(result: com.aicodingcli.code.analysis.ProjectAnalysisResult) {
<span class="fc" id="L1086">        println(&quot;📊 Project Analysis Results&quot;)</span>
<span class="fc" id="L1087">        println(&quot;=&quot; * 50)</span>
<span class="fc" id="L1088">        println(&quot;Project: ${result.projectPath}&quot;)</span>
<span class="fc" id="L1089">        println(&quot;Files Analyzed: ${result.fileResults.size}&quot;)</span>
<span class="fc" id="L1090">        println()</span>

        // Display overall metrics
<span class="fc" id="L1093">        println(&quot;📈 Overall Metrics:&quot;)</span>
<span class="fc" id="L1094">        println(&quot;  Total Lines of Code: ${result.overallMetrics.linesOfCode}&quot;)</span>
<span class="fc" id="L1095">        println(&quot;  Average Complexity: ${result.overallMetrics.cyclomaticComplexity}&quot;)</span>
<span class="fc" id="L1096">        println(&quot;  Average Maintainability: ${&quot;%.1f&quot;.format(result.overallMetrics.maintainabilityIndex)}&quot;)</span>
<span class="fc" id="L1097">        println()</span>

        // Display summary
<span class="fc" id="L1100">        println(&quot;📋 Summary:&quot;)</span>
<span class="fc" id="L1101">        println(&quot;  Total Files: ${result.summary.totalFiles}&quot;)</span>
<span class="fc" id="L1102">        println(&quot;  Total Issues: ${result.summary.totalIssues}&quot;)</span>
<span class="fc" id="L1103">        println(&quot;  Critical Issues: ${result.summary.criticalIssues}&quot;)</span>
<span class="fc" id="L1104">        println(&quot;  Average Complexity: ${&quot;%.1f&quot;.format(result.summary.averageComplexity)}&quot;)</span>
<span class="fc" id="L1105">        println(&quot;  Overall Maintainability: ${&quot;%.1f&quot;.format(result.summary.overallMaintainabilityIndex)}&quot;)</span>
<span class="fc" id="L1106">        println()</span>

        // Display top issues by file
<span class="pc bpc" id="L1109" title="1 of 2 branches missed.">        val filesWithIssues = result.fileResults.filter { it.issues.isNotEmpty() }</span>
<span class="pc bpc" id="L1110" title="2 of 4 branches missed.">        if (filesWithIssues.isNotEmpty()) {</span>
<span class="nc" id="L1111">            println(&quot;⚠️  Files with Issues:&quot;)</span>
<span class="nc" id="L1112">            filesWithIssues.sortedByDescending { it.issues.size }.take(10).forEach { fileResult -&gt;</span>
<span class="nc" id="L1113">                val fileName = File(fileResult.filePath).name</span>
<span class="nc" id="L1114">                println(&quot;  $fileName: ${fileResult.issues.size} issues&quot;)</span>
<span class="nc" id="L1115">            }</span>
        }
<span class="fc" id="L1117">    }</span>

    private fun displayProjectAnalysisResultJson(result: com.aicodingcli.code.analysis.ProjectAnalysisResult) {
<span class="nc" id="L1120">        println(&quot;&quot;&quot;{</span>
<span class="nc" id="L1121">  &quot;projectPath&quot;: &quot;${result.projectPath}&quot;,</span>
<span class="nc" id="L1122">  &quot;filesAnalyzed&quot;: ${result.fileResults.size},</span>
  &quot;overallMetrics&quot;: {
<span class="nc" id="L1124">    &quot;linesOfCode&quot;: ${result.overallMetrics.linesOfCode},</span>
<span class="nc" id="L1125">    &quot;averageComplexity&quot;: ${result.overallMetrics.cyclomaticComplexity},</span>
<span class="nc" id="L1126">    &quot;averageMaintainability&quot;: ${result.overallMetrics.maintainabilityIndex}</span>
  },
  &quot;summary&quot;: {
<span class="nc" id="L1129">    &quot;totalFiles&quot;: ${result.summary.totalFiles},</span>
<span class="nc" id="L1130">    &quot;totalIssues&quot;: ${result.summary.totalIssues},</span>
<span class="nc" id="L1131">    &quot;criticalIssues&quot;: ${result.summary.criticalIssues},</span>
<span class="nc" id="L1132">    &quot;averageComplexity&quot;: ${result.summary.averageComplexity},</span>
<span class="nc" id="L1133">    &quot;overallMaintainability&quot;: ${result.summary.overallMaintainabilityIndex}</span>
  }
}&quot;&quot;&quot;)
<span class="nc" id="L1136">    }</span>

    private fun displayMetrics(metrics: com.aicodingcli.code.analysis.CodeMetrics, format: String) {
<span class="fc" id="L1139">        when (format.lowercase()) {</span>
<span class="fc bfc" id="L1140" title="All 2 branches covered.">            &quot;json&quot; -&gt; {</span>
<span class="fc" id="L1141">                println(&quot;&quot;&quot;{</span>
<span class="fc" id="L1142">  &quot;linesOfCode&quot;: ${metrics.linesOfCode},</span>
<span class="fc" id="L1143">  &quot;cyclomaticComplexity&quot;: ${metrics.cyclomaticComplexity},</span>
<span class="fc" id="L1144">  &quot;maintainabilityIndex&quot;: ${metrics.maintainabilityIndex},</span>
<span class="fc" id="L1145">  &quot;testCoverage&quot;: ${metrics.testCoverage},</span>
<span class="fc" id="L1146">  &quot;duplicatedLines&quot;: ${metrics.duplicatedLines}</span>
}&quot;&quot;&quot;)
            }
            else -&gt; {
<span class="fc" id="L1150">                println(&quot;📈 Code Metrics:&quot;)</span>
<span class="fc" id="L1151">                println(&quot;  Lines of Code: ${metrics.linesOfCode}&quot;)</span>
<span class="fc" id="L1152">                println(&quot;  Cyclomatic Complexity: ${metrics.cyclomaticComplexity}&quot;)</span>
<span class="fc" id="L1153">                println(&quot;  Maintainability Index: ${&quot;%.1f&quot;.format(metrics.maintainabilityIndex)}&quot;)</span>
<span class="pc bpc" id="L1154" title="1 of 2 branches missed.">                metrics.testCoverage?.let { coverage -&gt;</span>
<span class="nc" id="L1155">                    println(&quot;  Test Coverage: ${&quot;%.1f&quot;.format(coverage)}%&quot;)</span>
<span class="nc" id="L1156">                }</span>
<span class="fc" id="L1157">                println(&quot;  Duplicated Lines: ${metrics.duplicatedLines}&quot;)</span>
            }
        }
<span class="fc" id="L1160">    }</span>

    private fun displayIssues(issues: List&lt;com.aicodingcli.code.analysis.CodeIssue&gt;, format: String) {
<span class="fc" id="L1163">        when (format.lowercase()) {</span>
<span class="pc bpc" id="L1164" title="1 of 2 branches missed.">            &quot;json&quot; -&gt; {</span>
<span class="nc" id="L1165">                println(&quot;[&quot;)</span>
<span class="nc" id="L1166">                issues.forEachIndexed { index, issue -&gt;</span>
<span class="nc" id="L1167">                    println(&quot;&quot;&quot;  {</span>
<span class="nc" id="L1168">    &quot;type&quot;: &quot;${issue.type}&quot;,</span>
<span class="nc" id="L1169">    &quot;severity&quot;: &quot;${issue.severity}&quot;,</span>
<span class="nc" id="L1170">    &quot;message&quot;: &quot;${issue.message}&quot;,</span>
<span class="nc" id="L1171">    &quot;line&quot;: ${issue.line},</span>
<span class="nc" id="L1172">    &quot;column&quot;: ${issue.column},</span>
<span class="nc" id="L1173">    &quot;suggestion&quot;: &quot;${issue.suggestion}&quot;</span>
<span class="nc bnc" id="L1174" title="All 2 branches missed.">  }${if (index &lt; issues.size - 1) &quot;,&quot; else &quot;&quot;}&quot;&quot;&quot;)</span>
<span class="nc" id="L1175">                }</span>
<span class="nc" id="L1176">                println(&quot;]&quot;)</span>
            }
            else -&gt; {
<span class="pc bpc" id="L1179" title="1 of 2 branches missed.">                if (issues.isEmpty()) {</span>
<span class="nc" id="L1180">                    println(&quot;✅ No issues found!&quot;)</span>
                } else {
<span class="fc" id="L1182">                    println(&quot;⚠️  Code Issues (${issues.size}):&quot;)</span>
<span class="fc" id="L1183">                    issues.forEach { issue -&gt;</span>
<span class="pc bpc" id="L1184" title="2 of 4 branches missed.">                        val severity = when (issue.severity) {</span>
<span class="nc" id="L1185">                            com.aicodingcli.code.analysis.IssueSeverity.CRITICAL -&gt; &quot;🔴 CRITICAL&quot;</span>
<span class="nc" id="L1186">                            com.aicodingcli.code.analysis.IssueSeverity.HIGH -&gt; &quot;🟠 HIGH&quot;</span>
<span class="fc" id="L1187">                            com.aicodingcli.code.analysis.IssueSeverity.MEDIUM -&gt; &quot;🟡 MEDIUM&quot;</span>
<span class="fc" id="L1188">                            com.aicodingcli.code.analysis.IssueSeverity.LOW -&gt; &quot;🟢 LOW&quot;</span>
                        }
<span class="pc bpc" id="L1190" title="1 of 2 branches missed.">                        val location = if (issue.line != null) &quot; (line ${issue.line})&quot; else &quot;&quot;</span>
<span class="fc" id="L1191">                        println(&quot;  $severity: ${issue.message}$location&quot;)</span>
<span class="pc bpc" id="L1192" title="1 of 2 branches missed.">                        issue.suggestion?.let { suggestion -&gt;</span>
<span class="fc" id="L1193">                            println(&quot;    💡 Suggestion: $suggestion&quot;)</span>
<span class="fc" id="L1194">                        }</span>
<span class="fc" id="L1195">                    }</span>
                }
            }
        }
<span class="fc" id="L1199">    }</span>

    private fun handlePluginCommand(args: Array&lt;String&gt;) {
<span class="nc bnc" id="L1202" title="All 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L1203">            printPluginHelp()</span>
<span class="nc" id="L1204">            return</span>
        }

<span class="nc bnc" id="L1207" title="All 8 branches missed.">        when (args[0]) {</span>
<span class="nc" id="L1208">            &quot;list&quot; -&gt; handlePluginList(args.drop(1).toTypedArray())</span>
<span class="nc" id="L1209">            &quot;install&quot; -&gt; handlePluginInstall(args.drop(1).toTypedArray())</span>
<span class="nc" id="L1210">            &quot;uninstall&quot; -&gt; handlePluginUninstall(args.drop(1).toTypedArray())</span>
<span class="nc" id="L1211">            &quot;enable&quot; -&gt; handlePluginEnable(args.drop(1).toTypedArray())</span>
<span class="nc" id="L1212">            &quot;disable&quot; -&gt; handlePluginDisable(args.drop(1).toTypedArray())</span>
<span class="nc" id="L1213">            &quot;info&quot; -&gt; handlePluginInfo(args.drop(1).toTypedArray())</span>
<span class="nc" id="L1214">            &quot;validate&quot; -&gt; handlePluginValidate(args.drop(1).toTypedArray())</span>
            else -&gt; {
<span class="nc" id="L1216">                println(&quot;Unknown plugin subcommand: ${args[0]}&quot;)</span>
<span class="nc" id="L1217">                printPluginHelp()</span>
            }
        }
<span class="nc" id="L1220">    }</span>

    private fun printPluginHelp() {
<span class="nc" id="L1223">        println(&quot;&quot;&quot;</span>
            Plugin Management Commands:

            plugin list                     List all installed plugins
            plugin install &lt;path-or-url&gt;    Install a plugin from file or URL
            plugin uninstall &lt;plugin-id&gt;    Uninstall a plugin
            plugin enable &lt;plugin-id&gt;       Enable a plugin
            plugin disable &lt;plugin-id&gt;      Disable a plugin
            plugin info &lt;plugin-id&gt;         Show plugin information
            plugin validate &lt;plugin-path&gt;   Validate a plugin file

            Examples:
            plugin list
            plugin install ./my-plugin.jar
            plugin install https://example.com/plugin.jar
            plugin uninstall my-plugin-id
            plugin info my-plugin-id
            plugin validate ./my-plugin.jar
<span class="nc" id="L1241">        &quot;&quot;&quot;.trimIndent())</span>
<span class="nc" id="L1242">    }</span>

    private fun handlePluginList(args: Array&lt;String&gt;) {
<span class="nc" id="L1245">        try {</span>
<span class="nc" id="L1246">            val loadedPlugins = pluginManager.getLoadedPlugins()</span>
<span class="nc" id="L1247">            val discoveryService = PluginDiscoveryService(</span>
<span class="nc" id="L1248">                System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli/plugins&quot;</span>
            )
<span class="nc" id="L1250">            val availablePlugins = discoveryService.discoverPlugins()</span>

<span class="nc bnc" id="L1252" title="All 4 branches missed.">            if (loadedPlugins.isEmpty() &amp;&amp; availablePlugins.isEmpty()) {</span>
<span class="nc" id="L1253">                println(&quot;No plugins found.&quot;)</span>
<span class="nc" id="L1254">                return</span>
            }

<span class="nc" id="L1257">            println(&quot;Plugin Status:&quot;)</span>
<span class="nc" id="L1258">            println(&quot;=&quot; * 60)</span>

            // Show loaded plugins
<span class="nc bnc" id="L1261" title="All 4 branches missed.">            if (loadedPlugins.isNotEmpty()) {</span>
<span class="nc" id="L1262">                println(&quot;\n✅ Loaded Plugins (${loadedPlugins.size}):&quot;)</span>
<span class="nc" id="L1263">                loadedPlugins.forEach { plugin -&gt;</span>
<span class="nc bnc" id="L1264" title="All 2 branches missed.">                    val state = pluginManager.getPluginState(plugin.metadata.id) ?: PluginState.UNLOADED</span>
<span class="nc" id="L1265">                    println(&quot;  📦 ${plugin.metadata.name} (${plugin.metadata.id})&quot;)</span>
<span class="nc" id="L1266">                    println(&quot;     Version: ${plugin.metadata.version}&quot;)</span>
<span class="nc" id="L1267">                    println(&quot;     Author: ${plugin.metadata.author}&quot;)</span>
<span class="nc" id="L1268">                    println(&quot;     State: $state&quot;)</span>
<span class="nc" id="L1269">                    println(&quot;     Description: ${plugin.metadata.description}&quot;)</span>

<span class="nc" id="L1271">                    when (plugin) {</span>
<span class="nc bnc" id="L1272" title="All 2 branches missed.">                        is CommandPlugin -&gt; {</span>
<span class="nc" id="L1273">                            println(&quot;     Type: Command Plugin&quot;)</span>
<span class="nc" id="L1274">                            println(&quot;     Commands: ${plugin.commands.map { it.name }.joinToString(&quot;, &quot;)}&quot;)</span>
                        }
<span class="nc bnc" id="L1276" title="All 2 branches missed.">                        is AiServicePlugin -&gt; {</span>
<span class="nc" id="L1277">                            println(&quot;     Type: AI Service Plugin&quot;)</span>
<span class="nc" id="L1278">                            println(&quot;     Provider: ${plugin.supportedProvider}&quot;)</span>
                        }
                        else -&gt; {
<span class="nc" id="L1281">                            println(&quot;     Type: Generic Plugin&quot;)</span>
                        }
                    }
<span class="nc" id="L1284">                    println()</span>
<span class="nc" id="L1285">                }</span>
            }

            // Show available but not loaded plugins
<span class="nc" id="L1289">            val loadedIds = loadedPlugins.map { it.metadata.id }.toSet()</span>
<span class="nc bnc" id="L1290" title="All 2 branches missed.">            val unloadedPlugins = availablePlugins.filter { it.metadata.id !in loadedIds }</span>

<span class="nc bnc" id="L1292" title="All 4 branches missed.">            if (unloadedPlugins.isNotEmpty()) {</span>
<span class="nc" id="L1293">                println(&quot;📋 Available Plugins (${unloadedPlugins.size}):&quot;)</span>
<span class="nc" id="L1294">                unloadedPlugins.forEach { pluginInfo -&gt;</span>
<span class="nc" id="L1295">                    println(&quot;  📦 ${pluginInfo.metadata.name} (${pluginInfo.metadata.id})&quot;)</span>
<span class="nc" id="L1296">                    println(&quot;     Version: ${pluginInfo.metadata.version}&quot;)</span>
<span class="nc" id="L1297">                    println(&quot;     Author: ${pluginInfo.metadata.author}&quot;)</span>
<span class="nc" id="L1298">                    println(&quot;     File: ${pluginInfo.filePath}&quot;)</span>
<span class="nc" id="L1299">                    println(&quot;     Description: ${pluginInfo.metadata.description}&quot;)</span>
<span class="nc" id="L1300">                    println()</span>
<span class="nc" id="L1301">                }</span>
            }

            // Show registry statistics
<span class="nc" id="L1305">            val stats = pluginManager.getRegistry().getStatistics()</span>
<span class="nc" id="L1306">            println(&quot;📊 Plugin Statistics:&quot;)</span>
<span class="nc" id="L1307">            println(&quot;  Total Plugins: ${stats.totalPlugins}&quot;)</span>
<span class="nc" id="L1308">            println(&quot;  Command Plugins: ${stats.commandPlugins}&quot;)</span>
<span class="nc" id="L1309">            println(&quot;  AI Service Plugins: ${stats.aiServicePlugins}&quot;)</span>
<span class="nc" id="L1310">            println(&quot;  Total Commands: ${stats.totalCommands}&quot;)</span>
<span class="nc bnc" id="L1311" title="All 4 branches missed.">            if (stats.supportedAiProviders.isNotEmpty()) {</span>
<span class="nc" id="L1312">                println(&quot;  Supported AI Providers: ${stats.supportedAiProviders.joinToString(&quot;, &quot;)}&quot;)</span>
            }

<span class="nc" id="L1315">        } catch (e: Exception) {</span>
<span class="nc" id="L1316">            println(&quot;❌ Error listing plugins: ${e.message}&quot;)</span>
        }
<span class="nc" id="L1318">    }</span>

    private fun handlePluginInstall(args: Array&lt;String&gt;) {
<span class="nc bnc" id="L1321" title="All 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L1322">            println(&quot;Usage: plugin install &lt;path-or-url&gt;&quot;)</span>
<span class="nc" id="L1323">            return</span>
        }

<span class="nc" id="L1326">        val pluginSource = args[0]</span>

<span class="nc" id="L1328">        runBlocking {</span>
<span class="nc" id="L1329">            try {</span>
<span class="nc" id="L1330">                println(&quot;📦 Installing plugin from: $pluginSource&quot;)</span>
<span class="nc" id="L1331">                val success = pluginManager.installPlugin(pluginSource)</span>

<span class="nc bnc" id="L1333" title="All 2 branches missed.">                if (success) {</span>
<span class="nc" id="L1334">                    println(&quot;✅ Plugin installed successfully!&quot;)</span>
                } else {
<span class="nc" id="L1336">                    println(&quot;❌ Plugin installation failed!&quot;)</span>
                }
<span class="nc" id="L1338">            } catch (e: Exception) {</span>
<span class="nc" id="L1339">                println(&quot;❌ Error installing plugin: ${e.message}&quot;)</span>
            }
<span class="nc" id="L1341">        }</span>
<span class="nc" id="L1342">    }</span>

    private fun handlePluginUninstall(args: Array&lt;String&gt;) {
<span class="nc bnc" id="L1345" title="All 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L1346">            println(&quot;Usage: plugin uninstall &lt;plugin-id&gt;&quot;)</span>
<span class="nc" id="L1347">            return</span>
        }

<span class="nc" id="L1350">        val pluginId = args[0]</span>

<span class="nc" id="L1352">        runBlocking {</span>
<span class="nc" id="L1353">            try {</span>
<span class="nc" id="L1354">                println(&quot;🗑️  Uninstalling plugin: $pluginId&quot;)</span>
<span class="nc" id="L1355">                val success = pluginManager.uninstallPlugin(pluginId)</span>

<span class="nc bnc" id="L1357" title="All 2 branches missed.">                if (success) {</span>
<span class="nc" id="L1358">                    println(&quot;✅ Plugin uninstalled successfully!&quot;)</span>
                } else {
<span class="nc" id="L1360">                    println(&quot;❌ Plugin not found or uninstall failed!&quot;)</span>
                }
<span class="nc" id="L1362">            } catch (e: Exception) {</span>
<span class="nc" id="L1363">                println(&quot;❌ Error uninstalling plugin: ${e.message}&quot;)</span>
            }
<span class="nc" id="L1365">        }</span>
<span class="nc" id="L1366">    }</span>

    private fun handlePluginEnable(args: Array&lt;String&gt;) {
<span class="nc bnc" id="L1369" title="All 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L1370">            println(&quot;Usage: plugin enable &lt;plugin-id&gt;&quot;)</span>
<span class="nc" id="L1371">            return</span>
        }

<span class="nc" id="L1374">        val pluginId = args[0]</span>

        // For now, this is equivalent to loading the plugin
        // In a more advanced implementation, we might have enable/disable state
<span class="nc" id="L1378">        println(&quot;🔄 Loading plugin: $pluginId&quot;)</span>

<span class="nc" id="L1380">        val discoveryService = PluginDiscoveryService(</span>
<span class="nc" id="L1381">            System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli/plugins&quot;</span>
        )
<span class="nc" id="L1383">        val availablePlugins = discoveryService.discoverPlugins()</span>
<span class="nc bnc" id="L1384" title="All 4 branches missed.">        val pluginInfo = availablePlugins.find { it.metadata.id == pluginId }</span>

<span class="nc bnc" id="L1386" title="All 2 branches missed.">        if (pluginInfo == null) {</span>
<span class="nc" id="L1387">            println(&quot;❌ Plugin not found: $pluginId&quot;)</span>
<span class="nc" id="L1388">            return</span>
        }

<span class="nc" id="L1391">        runBlocking {</span>
<span class="nc" id="L1392">            try {</span>
<span class="nc" id="L1393">                pluginManager.loadPlugin(pluginInfo.filePath)</span>
<span class="nc" id="L1394">                println(&quot;✅ Plugin enabled successfully!&quot;)</span>
<span class="nc" id="L1395">            } catch (e: Exception) {</span>
<span class="nc" id="L1396">                println(&quot;❌ Error enabling plugin: ${e.message}&quot;)</span>
            }
<span class="nc" id="L1398">        }</span>
<span class="nc" id="L1399">    }</span>

    private fun handlePluginDisable(args: Array&lt;String&gt;) {
<span class="nc bnc" id="L1402" title="All 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L1403">            println(&quot;Usage: plugin disable &lt;plugin-id&gt;&quot;)</span>
<span class="nc" id="L1404">            return</span>
        }

<span class="nc" id="L1407">        val pluginId = args[0]</span>

<span class="nc" id="L1409">        runBlocking {</span>
<span class="nc" id="L1410">            try {</span>
<span class="nc" id="L1411">                println(&quot;⏸️  Disabling plugin: $pluginId&quot;)</span>
<span class="nc" id="L1412">                val success = pluginManager.unloadPlugin(pluginId)</span>

<span class="nc bnc" id="L1414" title="All 2 branches missed.">                if (success) {</span>
<span class="nc" id="L1415">                    println(&quot;✅ Plugin disabled successfully!&quot;)</span>
                } else {
<span class="nc" id="L1417">                    println(&quot;❌ Plugin not loaded or disable failed!&quot;)</span>
                }
<span class="nc" id="L1419">            } catch (e: Exception) {</span>
<span class="nc" id="L1420">                println(&quot;❌ Error disabling plugin: ${e.message}&quot;)</span>
            }
<span class="nc" id="L1422">        }</span>
<span class="nc" id="L1423">    }</span>

    private fun handlePluginInfo(args: Array&lt;String&gt;) {
<span class="nc bnc" id="L1426" title="All 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L1427">            println(&quot;Usage: plugin info &lt;plugin-id&gt;&quot;)</span>
<span class="nc" id="L1428">            return</span>
        }

<span class="nc" id="L1431">        val pluginId = args[0]</span>

<span class="nc" id="L1433">        try {</span>
            // Try to get loaded plugin first
<span class="nc" id="L1435">            val loadedPlugin = pluginManager.getPlugin(pluginId)</span>
<span class="nc bnc" id="L1436" title="All 2 branches missed.">            if (loadedPlugin != null) {</span>
<span class="nc" id="L1437">                displayPluginInfo(loadedPlugin, true)</span>
<span class="nc" id="L1438">                return</span>
            }

            // If not loaded, check available plugins
<span class="nc" id="L1442">            val discoveryService = PluginDiscoveryService(</span>
<span class="nc" id="L1443">                System.getProperty(&quot;user.home&quot;) + &quot;/.aicodingcli/plugins&quot;</span>
            )
<span class="nc" id="L1445">            val availablePlugins = discoveryService.discoverPlugins()</span>
<span class="nc bnc" id="L1446" title="All 4 branches missed.">            val pluginInfo = availablePlugins.find { it.metadata.id == pluginId }</span>

<span class="nc bnc" id="L1448" title="All 2 branches missed.">            if (pluginInfo != null) {</span>
<span class="nc" id="L1449">                displayPluginInfo(pluginInfo.metadata, false, pluginInfo.filePath)</span>
            } else {
<span class="nc" id="L1451">                println(&quot;❌ Plugin not found: $pluginId&quot;)</span>
            }

<span class="nc" id="L1454">        } catch (e: Exception) {</span>
<span class="nc" id="L1455">            println(&quot;❌ Error getting plugin info: ${e.message}&quot;)</span>
        }
<span class="nc" id="L1457">    }</span>

    private fun displayPluginInfo(plugin: Plugin, isLoaded: Boolean) {
<span class="nc" id="L1460">        displayPluginInfo(plugin.metadata, isLoaded)</span>
<span class="nc" id="L1461">    }</span>

<span class="nc" id="L1463">    private fun displayPluginInfo(metadata: PluginMetadata, isLoaded: Boolean, filePath: String? = null) {</span>
<span class="nc" id="L1464">        println(&quot;📦 Plugin Information&quot;)</span>
<span class="nc" id="L1465">        println(&quot;=&quot; * 50)</span>
<span class="nc" id="L1466">        println(&quot;ID: ${metadata.id}&quot;)</span>
<span class="nc" id="L1467">        println(&quot;Name: ${metadata.name}&quot;)</span>
<span class="nc" id="L1468">        println(&quot;Version: ${metadata.version}&quot;)</span>
<span class="nc" id="L1469">        println(&quot;Author: ${metadata.author}&quot;)</span>
<span class="nc" id="L1470">        println(&quot;Description: ${metadata.description}&quot;)</span>
<span class="nc" id="L1471">        println(&quot;Main Class: ${metadata.mainClass}&quot;)</span>
<span class="nc bnc" id="L1472" title="All 2 branches missed.">        println(&quot;Status: ${if (isLoaded) &quot;✅ Loaded&quot; else &quot;📋 Available&quot;}&quot;)</span>

<span class="nc bnc" id="L1474" title="All 2 branches missed.">        if (filePath != null) {</span>
<span class="nc" id="L1475">            println(&quot;File: $filePath&quot;)</span>
        }

<span class="nc bnc" id="L1478" title="All 2 branches missed.">        metadata.website?.let { website -&gt;</span>
<span class="nc" id="L1479">            println(&quot;Website: $website&quot;)</span>
<span class="nc" id="L1480">        }</span>

<span class="nc bnc" id="L1482" title="All 2 branches missed.">        metadata.minCliVersion?.let { minVersion -&gt;</span>
<span class="nc" id="L1483">            println(&quot;Min CLI Version: $minVersion&quot;)</span>
<span class="nc" id="L1484">        }</span>

<span class="nc bnc" id="L1486" title="All 4 branches missed.">        if (metadata.dependencies.isNotEmpty()) {</span>
<span class="nc" id="L1487">            println(&quot;\nDependencies:&quot;)</span>
<span class="nc" id="L1488">            metadata.dependencies.forEach { dep -&gt;</span>
<span class="nc bnc" id="L1489" title="All 2 branches missed.">                val optional = if (dep.optional) &quot; (optional)&quot; else &quot;&quot;</span>
<span class="nc" id="L1490">                println(&quot;  - ${dep.id} ${dep.version}$optional&quot;)</span>
<span class="nc" id="L1491">            }</span>
        }

<span class="nc bnc" id="L1494" title="All 4 branches missed.">        if (metadata.permissions.isNotEmpty()) {</span>
<span class="nc" id="L1495">            println(&quot;\nPermissions:&quot;)</span>
<span class="nc" id="L1496">            metadata.permissions.forEach { permission -&gt;</span>
<span class="nc" id="L1497">                when (permission) {</span>
<span class="nc bnc" id="L1498" title="All 2 branches missed.">                    is PluginPermission.FileSystemPermission -&gt; {</span>
<span class="nc bnc" id="L1499" title="All 2 branches missed.">                        val access = if (permission.readOnly) &quot;read-only&quot; else &quot;read-write&quot;</span>
<span class="nc" id="L1500">                        println(&quot;  - File System ($access): ${permission.allowedPaths.joinToString(&quot;, &quot;)}&quot;)</span>
                    }
<span class="nc bnc" id="L1502" title="All 2 branches missed.">                    is PluginPermission.NetworkPermission -&gt; {</span>
<span class="nc" id="L1503">                        println(&quot;  - Network: ${permission.allowedHosts.joinToString(&quot;, &quot;)}&quot;)</span>
                    }
<span class="nc bnc" id="L1505" title="All 2 branches missed.">                    is PluginPermission.SystemPermission -&gt; {</span>
<span class="nc" id="L1506">                        println(&quot;  - System Commands: ${permission.allowedCommands.joinToString(&quot;, &quot;)}&quot;)</span>
                    }
<span class="nc bnc" id="L1508" title="All 2 branches missed.">                    is PluginPermission.ConfigPermission -&gt; {</span>
<span class="nc" id="L1509">                        println(&quot;  - Configuration Access&quot;)</span>
                    }
<span class="nc" id="L1511">                    is PluginPermission.HistoryPermission -&gt; {</span>
<span class="nc" id="L1512">                        println(&quot;  - History Access&quot;)</span>
                    }
                }
<span class="nc" id="L1515">            }</span>
        }
<span class="nc" id="L1517">    }</span>

    private fun handlePluginValidate(args: Array&lt;String&gt;) {
<span class="nc bnc" id="L1520" title="All 4 branches missed.">        if (args.isEmpty()) {</span>
<span class="nc" id="L1521">            println(&quot;Usage: plugin validate &lt;plugin-path&gt;&quot;)</span>
<span class="nc" id="L1522">            return</span>
        }

<span class="nc" id="L1525">        val pluginPath = args[0]</span>

<span class="nc" id="L1527">        try {</span>
<span class="nc" id="L1528">            println(&quot;🔍 Validating plugin: $pluginPath&quot;)</span>
<span class="nc" id="L1529">            val result = pluginManager.validatePlugin(pluginPath)</span>

<span class="nc bnc" id="L1531" title="All 2 branches missed.">            if (result.isValid) {</span>
<span class="nc" id="L1532">                println(&quot;✅ Plugin validation successful!&quot;)</span>
            } else {
<span class="nc" id="L1534">                println(&quot;❌ Plugin validation failed!&quot;)</span>
            }

<span class="nc bnc" id="L1537" title="All 4 branches missed.">            if (result.errors.isNotEmpty()) {</span>
<span class="nc" id="L1538">                println(&quot;\n🔴 Errors:&quot;)</span>
<span class="nc" id="L1539">                result.errors.forEach { error -&gt;</span>
<span class="nc" id="L1540">                    println(&quot;  - $error&quot;)</span>
<span class="nc" id="L1541">                }</span>
            }

<span class="nc bnc" id="L1544" title="All 4 branches missed.">            if (result.warnings.isNotEmpty()) {</span>
<span class="nc" id="L1545">                println(&quot;\n🟡 Warnings:&quot;)</span>
<span class="nc" id="L1546">                result.warnings.forEach { warning -&gt;</span>
<span class="nc" id="L1547">                    println(&quot;  - $warning&quot;)</span>
<span class="nc" id="L1548">                }</span>
            }

<span class="nc" id="L1551">        } catch (e: Exception) {</span>
<span class="nc" id="L1552">            println(&quot;❌ Error validating plugin: ${e.message}&quot;)</span>
        }
<span class="nc" id="L1554">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>