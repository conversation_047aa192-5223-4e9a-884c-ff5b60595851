<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ExecutionMetadataManager.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_source">ExecutionMetadataManager.kt</span></div><h1>ExecutionMetadataManager.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation.tools

import com.aicodingcli.conversation.ToolCall
import com.aicodingcli.conversation.ToolResult
import java.time.Instant
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.measureTime

/**
 * Manages execution metadata for tool calls
 */
<span class="fc" id="L13">class ExecutionMetadataManager {</span>
    
    /**
     * Enhance tool result with execution metadata
     */
    fun enhanceWithMetadata(
        toolCall: ToolCall,
        result: ToolResult,
        startTime: Instant,
        executionDuration: Duration
    ): ToolResult {
<span class="fc" id="L24">        val metadata = result.metadata.toMutableMap()</span>
        
        // Add standard execution metadata
<span class="fc" id="L27">        metadata[&quot;tool_name&quot;] = toolCall.toolName</span>
<span class="fc" id="L28">        metadata[&quot;execution_time&quot;] = executionDuration.toString()</span>
<span class="fc" id="L29">        metadata[&quot;executed_at&quot;] = startTime.toString()</span>
<span class="fc" id="L30">        metadata[&quot;success&quot;] = result.success.toString()</span>
        
        // Add parameter count
<span class="fc" id="L33">        metadata[&quot;parameter_count&quot;] = toolCall.parameters.size.toString()</span>
        
        // Add output size
<span class="fc" id="L36">        metadata[&quot;output_size&quot;] = result.output.length.toString()</span>
        
<span class="fc" id="L38">        return result.copy(metadata = metadata)</span>
    }
    
    /**
     * Measure execution time for an operation
     */
    suspend fun &lt;T&gt; measureExecution(
        operation: suspend () -&gt; T
    ): Pair&lt;T, Duration&gt; {
<span class="nc" id="L47">        var result: T? = null</span>
<span class="nc" id="L48">        val duration = measureTime {</span>
<span class="nc" id="L49">            result = operation()</span>
<span class="nc" id="L50">        }</span>
<span class="nc" id="L51">        return Pair(result!!, duration)</span>
    }
}

/**
 * Execution context for tool calls
 */
<span class="nc" id="L58">data class ExecutionContext(</span>
<span class="nc" id="L59">    val toolCall: ToolCall,</span>
<span class="nc" id="L60">    val workingDirectory: String,</span>
<span class="nc" id="L61">    val startTime: Instant = Instant.now(),</span>
<span class="nc" id="L62">    val metadata: MutableMap&lt;String, String&gt; = mutableMapOf()</span>
<span class="nc" id="L63">) {</span>
    fun addMetadata(key: String, value: String) {
<span class="nc" id="L65">        metadata[key] = value</span>
<span class="nc" id="L66">    }</span>
    
    fun getElapsedTime(): kotlin.time.Duration {
<span class="nc" id="L69">        val now = Instant.now()</span>
<span class="nc" id="L70">        return (now.toEpochMilli() - startTime.toEpochMilli()).milliseconds</span>
    }
}

/**
 * Tool execution statistics
 */
<span class="fc" id="L77">data class ToolExecutionStats(</span>
<span class="pc" id="L78">    val toolName: String,</span>
<span class="fc" id="L79">    val executionCount: Int,</span>
<span class="fc" id="L80">    val successCount: Int,</span>
<span class="pc" id="L81">    val failureCount: Int,</span>
<span class="pc" id="L82">    val averageExecutionTime: Duration,</span>
<span class="pc" id="L83">    val totalExecutionTime: Duration</span>
) {
    val successRate: Double
<span class="nc bnc" id="L86" title="All 2 branches missed.">        get() = if (executionCount &gt; 0) successCount.toDouble() / executionCount else 0.0</span>
}

/**
 * Collects and manages tool execution statistics
 */
<span class="fc" id="L92">class ToolExecutionStatsCollector {</span>
<span class="fc" id="L93">    private val executions = mutableMapOf&lt;String, MutableList&lt;ToolExecutionRecord&gt;&gt;()</span>
    
<span class="fc" id="L95">    data class ToolExecutionRecord(</span>
<span class="pc" id="L96">        val toolName: String,</span>
<span class="fc" id="L97">        val success: Boolean,</span>
<span class="fc" id="L98">        val executionTime: Duration,</span>
<span class="pc" id="L99">        val timestamp: Instant</span>
    )
    
    /**
     * Record a tool execution
     */
    fun recordExecution(
        toolName: String,
        success: Boolean,
        executionTime: Duration
    ) {
<span class="fc" id="L110">        val record = ToolExecutionRecord(</span>
<span class="fc" id="L111">            toolName = toolName,</span>
<span class="fc" id="L112">            success = success,</span>
<span class="fc" id="L113">            executionTime = executionTime,</span>
<span class="fc" id="L114">            timestamp = Instant.now()</span>
        )
        
<span class="fc" id="L117">        executions.getOrPut(toolName) { mutableListOf() }.add(record)</span>
<span class="fc" id="L118">    }</span>
    
    /**
     * Get statistics for a specific tool
     */
    fun getStatsForTool(toolName: String): ToolExecutionStats? {
<span class="pc bpc" id="L124" title="1 of 2 branches missed.">        val records = executions[toolName] ?: return null</span>
        
<span class="pc bpc" id="L126" title="1 of 2 branches missed.">        if (records.isEmpty()) return null</span>
        
<span class="fc" id="L128">        val executionCount = records.size</span>
<span class="fc" id="L129">        val successCount = records.count { it.success }</span>
<span class="fc" id="L130">        val failureCount = executionCount - successCount</span>
<span class="fc" id="L131">        val totalTime = records.fold(Duration.ZERO) { acc, record -&gt; acc + record.executionTime }</span>
<span class="fc" id="L132">        val averageTime = totalTime / executionCount</span>
        
<span class="fc" id="L134">        return ToolExecutionStats(</span>
<span class="fc" id="L135">            toolName = toolName,</span>
<span class="fc" id="L136">            executionCount = executionCount,</span>
<span class="fc" id="L137">            successCount = successCount,</span>
<span class="fc" id="L138">            failureCount = failureCount,</span>
<span class="fc" id="L139">            averageExecutionTime = averageTime,</span>
<span class="fc" id="L140">            totalExecutionTime = totalTime</span>
        )
    }
    
    /**
     * Get statistics for all tools
     */
    fun getAllStats(): List&lt;ToolExecutionStats&gt; {
<span class="fc" id="L148">        return executions.keys.mapNotNull { getStatsForTool(it) }</span>
    }
    
    /**
     * Clear all statistics
     */
    fun clearStats() {
<span class="fc" id="L155">        executions.clear()</span>
<span class="fc" id="L156">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>