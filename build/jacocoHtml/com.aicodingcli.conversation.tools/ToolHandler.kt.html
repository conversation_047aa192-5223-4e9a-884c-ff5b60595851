<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ToolHandler.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_source">ToolHandler.kt</span></div><h1>ToolHandler.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation.tools

import com.aicodingcli.conversation.*

/**
 * Interface for handling specific tool execution
 */
interface ToolHandler {
    /**
     * The name of the tool this handler supports
     */
    val toolName: String
    
    /**
     * Execute the tool with given parameters
     */
    suspend fun execute(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult
    
    /**
     * Validate tool parameters
     */
    fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult
    
    /**
     * Get tool metadata
     */
    fun getMetadata(): ToolMetadata
}

/**
 * Abstract base class for tool handlers
 */
<span class="fc" id="L33">abstract class BaseToolHandler(override val toolName: String) : ToolHandler {</span>
    
    override suspend fun execute(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L36">        return try {</span>
<span class="fc" id="L37">            val validation = validateParameters(parameters)</span>
<span class="fc bfc" id="L38" title="All 2 branches covered.">            if (!validation.isValid) {</span>
<span class="fc" id="L39">                return ToolResult.failure(</span>
<span class="fc" id="L40">                    error = &quot;Parameter validation failed: ${validation.errors.joinToString(&quot;, &quot;)}&quot;</span>
                )
            }
            
<span class="fc" id="L44">            executeInternal(parameters, workingDirectory)</span>
<span class="nc" id="L45">        } catch (e: Exception) {</span>
<span class="pc" id="L46">            ToolResult.failure(&quot;Tool execution failed: ${e.message}&quot;)</span>
        }
    }
    
    /**
     * Internal execution method to be implemented by subclasses
     */
    protected abstract suspend fun executeInternal(
        parameters: Map&lt;String, String&gt;, 
        workingDirectory: String
    ): ToolResult
    
    /**
     * Helper method to check required parameters
     */
    protected fun checkRequiredParameters(
        parameters: Map&lt;String, String&gt;, 
        required: List&lt;String&gt;
    ): List&lt;String&gt; {
<span class="fc" id="L65">        val errors = mutableListOf&lt;String&gt;()</span>
<span class="fc" id="L66">        required.forEach { param -&gt;</span>
<span class="pc bpc" id="L67" title="3 of 8 branches missed.">            if (!parameters.containsKey(param) || parameters[param].isNullOrBlank()) {</span>
<span class="fc" id="L68">                errors.add(&quot;Missing required parameter: $param&quot;)</span>
            }
<span class="fc" id="L70">        }</span>
<span class="fc" id="L71">        return errors</span>
    }
}

/**
 * Registry for tool handlers
 */
<span class="fc" id="L78">class ToolHandlerRegistry {</span>
<span class="fc" id="L79">    private val handlers = mutableMapOf&lt;String, ToolHandler&gt;()</span>
    
    /**
     * Register a tool handler
     */
    fun register(handler: ToolHandler) {
<span class="fc" id="L85">        handlers[handler.toolName] = handler</span>
<span class="fc" id="L86">    }</span>
    
    /**
     * Get handler for a tool
     */
    fun getHandler(toolName: String): ToolHandler? {
<span class="fc" id="L92">        return handlers[toolName]</span>
    }
    
    /**
     * Get all registered handlers
     */
    fun getAllHandlers(): List&lt;ToolHandler&gt; {
<span class="nc" id="L99">        return handlers.values.toList()</span>
    }
    
    /**
     * Get metadata for all tools
     */
    fun getAllToolMetadata(): List&lt;ToolMetadata&gt; {
<span class="fc" id="L106">        return handlers.values.map { it.getMetadata() }</span>
    }
}

/**
 * Factory for creating default tool handlers
 */
object DefaultToolHandlerFactory {
    
    fun createDefaultHandlers(): List&lt;ToolHandler&gt; {
<span class="fc" id="L116">        return listOf(</span>
            // File operations
<span class="fc" id="L118">            SaveFileHandler(),</span>
<span class="fc" id="L119">            ViewHandler(),</span>
<span class="fc" id="L120">            StrReplaceEditorHandler(),</span>
<span class="fc" id="L121">            RemoveFilesHandler(),</span>

            // Analysis tools
<span class="fc" id="L124">            CodebaseRetrievalHandler(),</span>
<span class="fc" id="L125">            DiagnosticsHandler(),</span>

            // Task management
<span class="fc" id="L128">            TaskManagementHandler(),</span>
<span class="fc" id="L129">            UpdateTasksHandler(),</span>

            // Process management
<span class="fc" id="L132">            LaunchProcessHandler(),</span>
<span class="fc" id="L133">            ReadTerminalHandler(),</span>

            // Network tools
<span class="fc" id="L136">            WebSearchHandler(),</span>
<span class="fc" id="L137">            WebFetchHandler(),</span>
<span class="fc" id="L138">            OpenBrowserHandler(),</span>

            // Git operations
<span class="fc" id="L141">            GitHubApiHandler(),</span>
<span class="fc" id="L142">            GitOperationsHandler()</span>
        )
    }
    
    fun createDefaultRegistry(): ToolHandlerRegistry {
<span class="fc" id="L147">        val registry = ToolHandlerRegistry()</span>
<span class="fc" id="L148">        createDefaultHandlers().forEach { registry.register(it) }</span>
<span class="fc" id="L149">        return registry</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>