<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NetworkToolHandlers.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_source">NetworkToolHandlers.kt</span></div><h1>NetworkToolHandlers.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation.tools

import com.aicodingcli.conversation.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.URL
import java.net.URLEncoder

/**
 * Handler for web-search tool
 */
<span class="fc" id="L12">class WebSearchHandler : BaseToolHandler(&quot;web-search&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L15">        val query = parameters[&quot;query&quot;]!!</span>
<span class="pc bpc" id="L16" title="2 of 4 branches missed.">        val numResults = parameters[&quot;num_results&quot;]?.toIntOrNull() ?: 5</span>
        
        // Mock implementation - in real scenario this would use actual search API
<span class="fc" id="L19">        val results = (1..numResults).map { i -&gt;</span>
<span class="fc" id="L20">            &quot;Result $i: Mock search result for '$query'\n&quot; +</span>
<span class="fc" id="L21">            &quot;URL: https://example.com/result$i\n&quot; +</span>
<span class="fc" id="L22">            &quot;Snippet: This is a mock search result snippet for the query '$query'.\n&quot;</span>
<span class="fc" id="L23">        }.joinToString(&quot;\n&quot;)</span>
        
<span class="fc" id="L25">        return ToolResult.success(</span>
<span class="fc" id="L26">            output = results,</span>
<span class="fc" id="L27">            metadata = mapOf(</span>
<span class="fc" id="L28">                &quot;query&quot; to query,</span>
<span class="fc" id="L29">                &quot;num_results&quot; to numResults.toString(),</span>
<span class="fc" id="L30">                &quot;mock&quot; to &quot;true&quot;</span>
            )
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L36">        val errors = checkRequiredParameters(parameters, listOf(&quot;query&quot;)).toMutableList()</span>
        
<span class="fc" id="L38">        val numResults = parameters[&quot;num_results&quot;]</span>
<span class="pc bpc" id="L39" title="1 of 2 branches missed.">        if (numResults != null) {</span>
<span class="fc" id="L40">            try {</span>
<span class="fc" id="L41">                val num = numResults.toInt()</span>
<span class="pc bpc" id="L42" title="1 of 4 branches missed.">                if (num &lt; 1 || num &gt; 10) {</span>
<span class="fc" id="L43">                    errors.add(&quot;num_results must be between 1 and 10&quot;)</span>
                }
<span class="nc" id="L45">            } catch (e: NumberFormatException) {</span>
<span class="nc" id="L46">                errors.add(&quot;num_results must be a valid number&quot;)</span>
            }
        }
        
<span class="fc bfc" id="L50" title="All 2 branches covered.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L54">        return ToolMetadata(</span>
<span class="fc" id="L55">            name = toolName,</span>
<span class="fc" id="L56">            description = &quot;Search the web for information&quot;,</span>
<span class="fc" id="L57">            parameters = listOf(</span>
<span class="fc" id="L58">                ToolParameter(&quot;query&quot;, &quot;string&quot;, &quot;The search query to send&quot;, required = true),</span>
<span class="fc" id="L59">                ToolParameter(&quot;num_results&quot;, &quot;integer&quot;, &quot;Number of results to return (1-10)&quot;, required = false)</span>
            ),
<span class="fc" id="L61">            category = &quot;network&quot;</span>
        )
    }
}

/**
 * Handler for web-fetch tool
 */
<span class="fc" id="L69">class WebFetchHandler : BaseToolHandler(&quot;web-fetch&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="nc" id="L72">        val url = parameters[&quot;url&quot;]!!</span>
        
<span class="nc" id="L74">        return withContext(Dispatchers.IO) {</span>
<span class="nc" id="L75">            try {</span>
                // Validate URL
<span class="nc" id="L77">                URL(url)</span>
                
                // Mock implementation - in real scenario this would fetch actual web content
<span class="nc" id="L80">                val mockContent = &quot;&quot;&quot;</span>
                    # Mock Web Content
                    
<span class="nc" id="L83">                    This is mock content fetched from: $url</span>
                    
                    ## Sample Content
                    - Mock paragraph 1
                    - Mock paragraph 2
                    - Mock paragraph 3
                    
                    **Note**: This is a mock implementation for testing purposes.
<span class="nc" id="L91">                &quot;&quot;&quot;.trimIndent()</span>
                
<span class="nc" id="L93">                ToolResult.success(</span>
<span class="nc" id="L94">                    output = mockContent,</span>
<span class="nc" id="L95">                    metadata = mapOf(</span>
<span class="nc" id="L96">                        &quot;url&quot; to url,</span>
<span class="nc" id="L97">                        &quot;content_length&quot; to mockContent.length.toString(),</span>
<span class="nc" id="L98">                        &quot;mock&quot; to &quot;true&quot;</span>
                    )
                )
<span class="nc" id="L101">            } catch (e: Exception) {</span>
<span class="nc" id="L102">                ToolResult.failure(&quot;Failed to fetch URL: ${e.message}&quot;)</span>
<span class="nc" id="L103">            }</span>
        }
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="nc" id="L108">        val errors = checkRequiredParameters(parameters, listOf(&quot;url&quot;)).toMutableList()</span>
        
<span class="nc" id="L110">        val url = parameters[&quot;url&quot;]</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">        if (url != null) {</span>
<span class="nc" id="L112">            try {</span>
<span class="nc" id="L113">                URL(url)</span>
<span class="nc" id="L114">            } catch (e: Exception) {</span>
<span class="nc" id="L115">                errors.add(&quot;Invalid URL format: $url&quot;)</span>
            }
        }
        
<span class="nc bnc" id="L119" title="All 2 branches missed.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L123">        return ToolMetadata(</span>
<span class="fc" id="L124">            name = toolName,</span>
<span class="fc" id="L125">            description = &quot;Fetches data from a webpage and converts it into Markdown&quot;,</span>
<span class="fc" id="L126">            parameters = listOf(</span>
<span class="fc" id="L127">                ToolParameter(&quot;url&quot;, &quot;string&quot;, &quot;The URL to fetch&quot;, required = true)</span>
            ),
<span class="fc" id="L129">            category = &quot;network&quot;</span>
        )
    }
}

/**
 * Handler for open-browser tool
 */
<span class="fc" id="L137">class OpenBrowserHandler : BaseToolHandler(&quot;open-browser&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="nc" id="L140">        val url = parameters[&quot;url&quot;]!!</span>
        
<span class="nc" id="L142">        return withContext(Dispatchers.IO) {</span>
<span class="nc" id="L143">            try {</span>
                // Validate URL
<span class="nc" id="L145">                URL(url)</span>
                
                // Mock implementation - in real scenario this would open actual browser
<span class="nc" id="L148">                ToolResult.success(</span>
<span class="nc" id="L149">                    output = &quot;Browser opened successfully for URL: $url&quot;,</span>
<span class="nc" id="L150">                    metadata = mapOf(</span>
<span class="nc" id="L151">                        &quot;url&quot; to url,</span>
<span class="nc" id="L152">                        &quot;mock&quot; to &quot;true&quot;</span>
                    )
                )
<span class="nc" id="L155">            } catch (e: Exception) {</span>
<span class="nc" id="L156">                ToolResult.failure(&quot;Failed to open browser: ${e.message}&quot;)</span>
<span class="nc" id="L157">            }</span>
        }
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="nc" id="L162">        val errors = checkRequiredParameters(parameters, listOf(&quot;url&quot;)).toMutableList()</span>
        
<span class="nc" id="L164">        val url = parameters[&quot;url&quot;]</span>
<span class="nc bnc" id="L165" title="All 2 branches missed.">        if (url != null) {</span>
<span class="nc" id="L166">            try {</span>
<span class="nc" id="L167">                URL(url)</span>
<span class="nc" id="L168">            } catch (e: Exception) {</span>
<span class="nc" id="L169">                errors.add(&quot;Invalid URL format: $url&quot;)</span>
            }
        }
        
<span class="nc bnc" id="L173" title="All 2 branches missed.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L177">        return ToolMetadata(</span>
<span class="fc" id="L178">            name = toolName,</span>
<span class="fc" id="L179">            description = &quot;Open a URL in the default browser&quot;,</span>
<span class="fc" id="L180">            parameters = listOf(</span>
<span class="fc" id="L181">                ToolParameter(&quot;url&quot;, &quot;string&quot;, &quot;The URL to open in the browser&quot;, required = true)</span>
            ),
<span class="fc" id="L183">            category = &quot;network&quot;</span>
        )
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>