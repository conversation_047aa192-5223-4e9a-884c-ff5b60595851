<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AnalysisToolHandlers.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_source">AnalysisToolHandlers.kt</span></div><h1>AnalysisToolHandlers.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation.tools

import com.aicodingcli.conversation.*

/**
 * Handler for codebase-retrieval tool
 */
<span class="fc" id="L8">class CodebaseRetrievalHandler : BaseToolHandler(&quot;codebase-retrieval&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L11">        val request = parameters[&quot;information_request&quot;]!!</span>
        
        // Mock implementation - in real scenario this would integrate with actual codebase analysis
<span class="fc" id="L14">        return ToolResult.success(</span>
<span class="fc" id="L15">            output = &quot;Mock codebase retrieval result for: $request\n&quot; +</span>
                    &quot;Found classes: User, Product, Order\n&quot; +
                    &quot;Found interfaces: Service, Repository\n&quot; +
                    &quot;Found packages: com.example.model, com.example.service&quot;,
<span class="fc" id="L19">            metadata = mapOf(&quot;mock&quot; to &quot;true&quot;, &quot;request&quot; to request)</span>
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L24">        val errors = checkRequiredParameters(parameters, listOf(&quot;information_request&quot;))</span>
<span class="pc bpc" id="L25" title="1 of 2 branches missed.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L29">        return ToolMetadata(</span>
<span class="fc" id="L30">            name = toolName,</span>
<span class="fc" id="L31">            description = &quot;Retrieve information from codebase&quot;,</span>
<span class="fc" id="L32">            parameters = listOf(</span>
<span class="fc" id="L33">                ToolParameter(&quot;information_request&quot;, &quot;string&quot;, &quot;What information to retrieve&quot;, required = true)</span>
            ),
<span class="fc" id="L35">            category = &quot;analysis&quot;</span>
        )
    }
}

/**
 * Handler for task management tools (add_tasks, update_tasks)
 */
<span class="fc" id="L43">class TaskManagementHandler : BaseToolHandler(&quot;add_tasks&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L46">        val name = parameters[&quot;name&quot;]!!</span>
<span class="fc" id="L47">        val description = parameters[&quot;description&quot;]!!</span>
        
        // Mock implementation - in real scenario this would integrate with actual task management
<span class="fc" id="L50">        return ToolResult.success(</span>
<span class="fc" id="L51">            output = &quot;Task added successfully: $name&quot;,</span>
<span class="fc" id="L52">            metadata = mapOf(&quot;task_name&quot; to name, &quot;task_description&quot; to description)</span>
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L57">        val errors = checkRequiredParameters(parameters, listOf(&quot;name&quot;, &quot;description&quot;))</span>
<span class="pc bpc" id="L58" title="1 of 2 branches missed.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L62">        return ToolMetadata(</span>
<span class="fc" id="L63">            name = toolName,</span>
<span class="fc" id="L64">            description = &quot;Add new tasks to task list&quot;,</span>
<span class="fc" id="L65">            parameters = listOf(</span>
<span class="fc" id="L66">                ToolParameter(&quot;name&quot;, &quot;string&quot;, &quot;Task name&quot;, required = true),</span>
<span class="fc" id="L67">                ToolParameter(&quot;description&quot;, &quot;string&quot;, &quot;Task description&quot;, required = true)</span>
            ),
<span class="fc" id="L69">            category = &quot;task&quot;</span>
        )
    }
}

/**
 * Handler for update_tasks tool
 */
<span class="fc" id="L77">class UpdateTasksHandler : BaseToolHandler(&quot;update_tasks&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="nc" id="L80">        val taskId = parameters[&quot;task_id&quot;]</span>
<span class="nc" id="L81">        val state = parameters[&quot;state&quot;]</span>
        
        // Mock implementation
<span class="nc" id="L84">        return ToolResult.success(</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">            output = &quot;Task updated successfully${if (taskId != null) &quot;: $taskId&quot; else &quot;&quot;}&quot;,</span>
<span class="nc" id="L86">            metadata = mapOf(</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">                &quot;task_id&quot; to (taskId ?: &quot;batch&quot;),</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">                &quot;state&quot; to (state ?: &quot;unknown&quot;)</span>
            )
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
        // update_tasks has optional parameters
<span class="nc" id="L95">        return ValidationResult.valid()</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L99">        return ToolMetadata(</span>
<span class="fc" id="L100">            name = toolName,</span>
<span class="fc" id="L101">            description = &quot;Update existing tasks&quot;,</span>
<span class="fc" id="L102">            parameters = listOf(</span>
<span class="fc" id="L103">                ToolParameter(&quot;task_id&quot;, &quot;string&quot;, &quot;Task ID to update&quot;, required = false),</span>
<span class="fc" id="L104">                ToolParameter(&quot;state&quot;, &quot;string&quot;, &quot;New task state&quot;, required = false)</span>
            ),
<span class="fc" id="L106">            category = &quot;task&quot;</span>
        )
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>