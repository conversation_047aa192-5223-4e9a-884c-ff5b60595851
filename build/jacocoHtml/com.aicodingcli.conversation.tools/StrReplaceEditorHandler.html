<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StrReplaceEditorHandler</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_class">StrReplaceEditorHandler</span></div><h1>StrReplaceEditorHandler</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">8 of 178</td><td class="ctr2">95%</td><td class="bar">1 of 6</td><td class="ctr2">83%</td><td class="ctr1">1</td><td class="ctr2">7</td><td class="ctr1">1</td><td class="ctr2">25</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a0"><a href="FileToolHandlers.kt.html#L128" class="el_method">executeInternal(Map, String, Continuation)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="108" height="10" title="76" alt="76"/></td><td class="ctr2" id="c3">90%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="3" alt="3"/></td><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i0">14</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="FileToolHandlers.kt.html#L158" class="el_method">getMetadata()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="59" alt="59"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">8</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="FileToolHandlers.kt.html#L153" class="el_method">validateParameters(Map)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="44" height="10" title="31" alt="31"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">2</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="FileToolHandlers.kt.html#L125" class="el_method">StrReplaceEditorHandler()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>