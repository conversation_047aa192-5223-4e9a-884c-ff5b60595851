<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BaseToolHandler</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_class">BaseToolHandler</span></div><h1>BaseToolHandler</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">12 of 96</td><td class="ctr2">87%</td><td class="bar">3 of 10</td><td class="ctr2">70%</td><td class="ctr1">3</td><td class="ctr2">9</td><td class="ctr1">1</td><td class="ctr2">15</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a2"><a href="ToolHandler.kt.html#L36" class="el_method">execute$suspendImpl(BaseToolHandler, Map, String, Continuation)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="96" height="10" title="41" alt="41"/></td><td class="ctr2" id="c3">80%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f1">0</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i0">8</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="ToolHandler.kt.html#L65" class="el_method">checkRequiredParameters(Map, List)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="34" alt="34"/></td><td class="ctr2" id="c2">94%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="75" height="10" title="5" alt="5"/></td><td class="ctr2" id="e1">62%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">6</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="ToolHandler.kt.html#L33" class="el_method">BaseToolHandler(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a3"><a href="ToolHandler.kt.html#L33" class="el_method">getToolName()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>