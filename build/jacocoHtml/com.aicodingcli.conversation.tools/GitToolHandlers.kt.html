<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GitToolHandlers.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_source">GitToolHandlers.kt</span></div><h1>GitToolHandlers.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation.tools

import com.aicodingcli.conversation.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Handler for github-api tool
 */
<span class="fc" id="L11">class GitHubApiHandler : BaseToolHandler(&quot;github-api&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L14">        val path = parameters[&quot;path&quot;]!!</span>
<span class="pc bpc" id="L15" title="1 of 2 branches missed.">        val method = parameters[&quot;method&quot;] ?: &quot;GET&quot;</span>
<span class="fc" id="L16">        val summary = parameters[&quot;summary&quot;]</span>
        
<span class="fc" id="L18">        return withContext(Dispatchers.IO) {</span>
<span class="fc" id="L19">            try {</span>
                // Mock implementation - in real scenario this would make actual GitHub API calls
<span class="fc" id="L21">                val mockResponse = when {</span>
<span class="pc bpc" id="L22" title="2 of 4 branches missed.">                    path.contains(&quot;/repos/&quot;) &amp;&amp; path.contains(&quot;/issues&quot;) -&gt; {</span>
                        &quot;&quot;&quot;
                        issues:
                        - number: 1
                          title: &quot;Sample Issue&quot;
                          state: &quot;open&quot;
                          body: &quot;This is a sample issue&quot;
                        - number: 2
                          title: &quot;Another Issue&quot;
                          state: &quot;closed&quot;
                          body: &quot;This is another sample issue&quot;
<span class="fc" id="L33">                        &quot;&quot;&quot;.trimIndent()</span>
                    }
<span class="nc bnc" id="L35" title="All 4 branches missed.">                    path.contains(&quot;/repos/&quot;) &amp;&amp; path.contains(&quot;/pulls&quot;) -&gt; {</span>
                        &quot;&quot;&quot;
                        pulls:
                        - number: 1
                          title: &quot;Sample Pull Request&quot;
                          state: &quot;open&quot;
                          body: &quot;This is a sample pull request&quot;
<span class="nc" id="L42">                        &quot;&quot;&quot;.trimIndent()</span>
                    }
<span class="nc bnc" id="L44" title="All 4 branches missed.">                    path.contains(&quot;/repos/&quot;) &amp;&amp; path.contains(&quot;/commits&quot;) -&gt; {</span>
                        &quot;&quot;&quot;
                        commits:
                        - sha: &quot;abc123&quot;
                          message: &quot;Sample commit message&quot;
                          author: &quot;<EMAIL>&quot;
<span class="nc" id="L50">                        &quot;&quot;&quot;.trimIndent()</span>
                    }
                    else -&gt; {
                        &quot;&quot;&quot;
                        response:
<span class="nc" id="L55">                          message: &quot;Mock GitHub API response for $method $path&quot;</span>
                          status: &quot;success&quot;
<span class="nc" id="L57">                        &quot;&quot;&quot;.trimIndent()</span>
                    }
                }
                
<span class="fc" id="L61">                ToolResult.success(</span>
<span class="fc" id="L62">                    output = mockResponse,</span>
<span class="fc" id="L63">                    metadata = mapOf(</span>
<span class="fc" id="L64">                        &quot;path&quot; to path,</span>
<span class="fc" id="L65">                        &quot;method&quot; to method,</span>
<span class="fc" id="L66">                        &quot;mock&quot; to &quot;true&quot;,</span>
<span class="pc bpc" id="L67" title="1 of 2 branches missed.">                        &quot;summary&quot; to (summary ?: &quot;GitHub API call&quot;)</span>
                    )
                )
<span class="nc" id="L70">            } catch (e: Exception) {</span>
<span class="pc" id="L71">                ToolResult.failure(&quot;GitHub API call failed: ${e.message}&quot;)</span>
<span class="fc" id="L72">            }</span>
        }
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L77">        val errors = checkRequiredParameters(parameters, listOf(&quot;path&quot;)).toMutableList()</span>
        
<span class="fc" id="L79">        val method = parameters[&quot;method&quot;]</span>
<span class="pc bpc" id="L80" title="1 of 4 branches missed.">        if (method != null &amp;&amp; method !in listOf(&quot;GET&quot;, &quot;POST&quot;, &quot;PATCH&quot;, &quot;PUT&quot;)) {</span>
<span class="fc" id="L81">            errors.add(&quot;method must be one of: GET, POST, PATCH, PUT&quot;)</span>
        }
        
<span class="fc bfc" id="L84" title="All 2 branches covered.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L88">        return ToolMetadata(</span>
<span class="fc" id="L89">            name = toolName,</span>
<span class="fc" id="L90">            description = &quot;Make GitHub API calls&quot;,</span>
<span class="fc" id="L91">            parameters = listOf(</span>
<span class="fc" id="L92">                ToolParameter(&quot;path&quot;, &quot;string&quot;, &quot;GitHub API path&quot;, required = true),</span>
<span class="fc" id="L93">                ToolParameter(&quot;method&quot;, &quot;string&quot;, &quot;HTTP method (GET, POST, PATCH, PUT)&quot;, required = false),</span>
<span class="fc" id="L94">                ToolParameter(&quot;summary&quot;, &quot;string&quot;, &quot;Summary of what this API call will do&quot;, required = false),</span>
<span class="fc" id="L95">                ToolParameter(&quot;data&quot;, &quot;object&quot;, &quot;Data to send as query params or JSON body&quot;, required = false),</span>
<span class="fc" id="L96">                ToolParameter(&quot;details&quot;, &quot;boolean&quot;, &quot;Include all fields in response&quot;, required = false)</span>
            ),
<span class="fc" id="L98">            category = &quot;git&quot;</span>
        )
    }
}

/**
 * Handler for git operations (mock implementation)
 */
<span class="fc" id="L106">class GitOperationsHandler : BaseToolHandler(&quot;git-operations&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="nc" id="L109">        val operation = parameters[&quot;operation&quot;]!!</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">        val args = parameters[&quot;args&quot;] ?: &quot;&quot;</span>
        
<span class="nc" id="L112">        return withContext(Dispatchers.IO) {</span>
<span class="nc" id="L113">            try {</span>
<span class="nc" id="L114">                val workingDir = File(workingDirectory)</span>
                
                // Mock implementation - in real scenario this would execute actual git commands
<span class="nc bnc" id="L117" title="All 22 branches missed.">                val mockOutput = when (operation) {</span>
                    &quot;status&quot; -&gt; {
                        &quot;&quot;&quot;
                        On branch main
                        Your branch is up to date with 'origin/main'.
                        
                        Changes not staged for commit:
                          modified:   src/main/kotlin/Example.kt
                        
                        Untracked files:
                          src/main/kotlin/NewFile.kt
<span class="nc" id="L128">                        &quot;&quot;&quot;.trimIndent()</span>
                    }
<span class="nc" id="L130">                    &quot;add&quot; -&gt; &quot;Mock: Added files to staging area: $args&quot;</span>
<span class="nc" id="L131">                    &quot;commit&quot; -&gt; &quot;Mock: Committed changes with message: $args&quot;</span>
<span class="nc" id="L132">                    &quot;push&quot; -&gt; &quot;Mock: Pushed changes to remote repository&quot;</span>
<span class="nc" id="L133">                    &quot;pull&quot; -&gt; &quot;Mock: Pulled latest changes from remote repository&quot;</span>
                    &quot;branch&quot; -&gt; {
                        &quot;&quot;&quot;
                        * main
                          feature-branch
                          develop
<span class="nc" id="L139">                        &quot;&quot;&quot;.trimIndent()</span>
                    }
                    &quot;log&quot; -&gt; {
                        &quot;&quot;&quot;
                        commit abc123def456 (HEAD -&gt; main)
                        Author: Developer &lt;<EMAIL>&gt;
                        Date: Mon Jan 1 12:00:00 2024 +0000
                        
                            Sample commit message
<span class="nc" id="L148">                        &quot;&quot;&quot;.trimIndent()</span>
                    }
<span class="nc" id="L150">                    else -&gt; &quot;Mock: Executed git $operation $args&quot;</span>
                }
                
<span class="nc" id="L153">                ToolResult.success(</span>
<span class="nc" id="L154">                    output = mockOutput,</span>
<span class="nc" id="L155">                    metadata = mapOf(</span>
<span class="nc" id="L156">                        &quot;operation&quot; to operation,</span>
<span class="nc" id="L157">                        &quot;args&quot; to args,</span>
<span class="nc" id="L158">                        &quot;working_directory&quot; to workingDirectory,</span>
<span class="nc" id="L159">                        &quot;mock&quot; to &quot;true&quot;</span>
                    )
                )
<span class="nc" id="L162">            } catch (e: Exception) {</span>
<span class="nc" id="L163">                ToolResult.failure(&quot;Git operation failed: ${e.message}&quot;)</span>
<span class="nc" id="L164">            }</span>
        }
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="nc" id="L169">        val errors = checkRequiredParameters(parameters, listOf(&quot;operation&quot;)).toMutableList()</span>
        
<span class="nc" id="L171">        val operation = parameters[&quot;operation&quot;]</span>
<span class="nc" id="L172">        val validOperations = listOf(&quot;status&quot;, &quot;add&quot;, &quot;commit&quot;, &quot;push&quot;, &quot;pull&quot;, &quot;branch&quot;, &quot;log&quot;, &quot;diff&quot;, &quot;checkout&quot;)</span>
<span class="nc bnc" id="L173" title="All 4 branches missed.">        if (operation != null &amp;&amp; operation !in validOperations) {</span>
<span class="nc" id="L174">            errors.add(&quot;operation must be one of: ${validOperations.joinToString(&quot;, &quot;)}&quot;)</span>
        }
        
<span class="nc bnc" id="L177" title="All 2 branches missed.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L181">        return ToolMetadata(</span>
<span class="fc" id="L182">            name = toolName,</span>
<span class="fc" id="L183">            description = &quot;Execute Git operations in the repository&quot;,</span>
<span class="fc" id="L184">            parameters = listOf(</span>
<span class="fc" id="L185">                ToolParameter(&quot;operation&quot;, &quot;string&quot;, &quot;Git operation to perform&quot;, required = true),</span>
<span class="fc" id="L186">                ToolParameter(&quot;args&quot;, &quot;string&quot;, &quot;Arguments for the git operation&quot;, required = false)</span>
            ),
<span class="fc" id="L188">            category = &quot;git&quot;</span>
        )
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>