<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FileToolHandlers.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_source">FileToolHandlers.kt</span></div><h1>FileToolHandlers.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation.tools

import com.aicodingcli.conversation.*
import java.io.File

/**
 * Handler for save-file tool
 */
<span class="fc" id="L9">class SaveFileHandler : BaseToolHandler(&quot;save-file&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L12">        val path = parameters[&quot;path&quot;]!!</span>
<span class="fc" id="L13">        val content = parameters[&quot;file_content&quot;]!!</span>
        
        // Validate path
<span class="pc bpc" id="L16" title="8 of 10 branches missed.">        if (path.contains('\u0000') || (path.startsWith(&quot;/&quot;) &amp;&amp; File(path).parentFile?.canWrite() != true)) {</span>
<span class="nc" id="L17">            return ToolResult.failure(&quot;Invalid or inaccessible file path: $path&quot;)</span>
        }
        
<span class="fc" id="L20">        val file = File(workingDirectory, path)</span>
        
        // Try to create parent directories
<span class="fc" id="L23">        val parentDir = file.parentFile</span>
<span class="pc bpc" id="L24" title="2 of 4 branches missed.">        if (parentDir != null &amp;&amp; !parentDir.exists()) {</span>
<span class="nc bnc" id="L25" title="All 2 branches missed.">            if (!parentDir.mkdirs()) {</span>
<span class="nc" id="L26">                return ToolResult.failure(&quot;Failed to create parent directories for: $path&quot;)</span>
            }
        }
        
<span class="fc" id="L30">        file.writeText(content)</span>
        
<span class="fc" id="L32">        return ToolResult.success(</span>
<span class="fc" id="L33">            output = &quot;File saved successfully: $path&quot;,</span>
<span class="fc" id="L34">            metadata = mapOf(&quot;file_size&quot; to file.length().toString())</span>
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L39">        val errors = checkRequiredParameters(parameters, listOf(&quot;path&quot;, &quot;file_content&quot;)).toMutableList()</span>

<span class="fc" id="L41">        val path = parameters[&quot;path&quot;]</span>
<span class="pc bpc" id="L42" title="1 of 6 branches missed.">        if (path.isNullOrBlank()) {</span>
<span class="fc" id="L43">            errors.add(&quot;Path cannot be empty&quot;)</span>
        }

<span class="fc bfc" id="L46" title="All 2 branches covered.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L50">        return ToolMetadata(</span>
<span class="fc" id="L51">            name = toolName,</span>
<span class="fc" id="L52">            description = &quot;Create a new file with content&quot;,</span>
<span class="fc" id="L53">            parameters = listOf(</span>
<span class="fc" id="L54">                ToolParameter(&quot;path&quot;, &quot;string&quot;, &quot;File path relative to working directory&quot;, required = true),</span>
<span class="fc" id="L55">                ToolParameter(&quot;file_content&quot;, &quot;string&quot;, &quot;Content to write to the file&quot;, required = true)</span>
            ),
<span class="fc" id="L57">            category = &quot;file&quot;</span>
        )
    }
}

/**
 * Handler for view tool
 */
<span class="fc" id="L65">class ViewHandler : BaseToolHandler(&quot;view&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L68">        val path = parameters[&quot;path&quot;]!!</span>
<span class="fc" id="L69">        val type = parameters[&quot;type&quot;]!!</span>
        
<span class="fc" id="L71">        val file = File(workingDirectory, path)</span>
        
<span class="fc bfc" id="L73" title="All 2 branches covered.">        if (!file.exists()) {</span>
<span class="fc" id="L74">            return ToolResult.failure(&quot;File or directory does not exist: $path&quot;)</span>
        }
        
<span class="fc" id="L77">        val output = when (type) {</span>
<span class="pc bpc" id="L78" title="1 of 2 branches missed.">            &quot;file&quot; -&gt; {</span>
<span class="pc bpc" id="L79" title="1 of 2 branches missed.">                if (file.isFile) {</span>
<span class="fc" id="L80">                    file.readText()</span>
                } else {
<span class="nc" id="L82">                    &quot;Path is not a file: $path&quot;</span>
                }
            }
<span class="nc bnc" id="L85" title="All 2 branches missed.">            &quot;directory&quot; -&gt; {</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">                if (file.isDirectory) {</span>
<span class="nc bnc" id="L87" title="All 4 branches missed.">                    file.listFiles()?.joinToString(&quot;\n&quot;) { it.name } ?: &quot;Empty directory&quot;</span>
                } else {
<span class="nc" id="L89">                    &quot;Path is not a directory: $path&quot;</span>
                }
            }
<span class="nc" id="L92">            else -&gt; &quot;Invalid type: $type. Use 'file' or 'directory'&quot;</span>
        }
        
<span class="fc" id="L95">        return ToolResult.success(output)</span>
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L99">        val errors = checkRequiredParameters(parameters, listOf(&quot;path&quot;, &quot;type&quot;)).toMutableList()</span>

<span class="fc" id="L101">        val type = parameters[&quot;type&quot;]</span>
<span class="pc bpc" id="L102" title="1 of 2 branches missed.">        if (type !in listOf(&quot;file&quot;, &quot;directory&quot;)) {</span>
<span class="nc" id="L103">            errors.add(&quot;Type must be 'file' or 'directory'&quot;)</span>
        }

<span class="fc bfc" id="L106" title="All 2 branches covered.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L110">        return ToolMetadata(</span>
<span class="fc" id="L111">            name = toolName,</span>
<span class="fc" id="L112">            description = &quot;View file or directory contents&quot;,</span>
<span class="fc" id="L113">            parameters = listOf(</span>
<span class="fc" id="L114">                ToolParameter(&quot;path&quot;, &quot;string&quot;, &quot;Path to view&quot;, required = true),</span>
<span class="fc" id="L115">                ToolParameter(&quot;type&quot;, &quot;string&quot;, &quot;Type: 'file' or 'directory'&quot;, required = true)</span>
            ),
<span class="fc" id="L117">            category = &quot;file&quot;</span>
        )
    }
}

/**
 * Handler for str-replace-editor tool
 */
<span class="fc" id="L125">class StrReplaceEditorHandler : BaseToolHandler(&quot;str-replace-editor&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L128">        val path = parameters[&quot;path&quot;]!!</span>
<span class="fc" id="L129">        val oldStr = parameters[&quot;old_str&quot;]!!</span>
<span class="fc" id="L130">        val newStr = parameters[&quot;new_str&quot;]!!</span>
        
<span class="fc" id="L132">        val file = File(workingDirectory, path)</span>
        
<span class="fc bfc" id="L134" title="All 2 branches covered.">        if (!file.exists()) {</span>
<span class="fc" id="L135">            return ToolResult.failure(&quot;File does not exist: $path&quot;)</span>
        }
        
<span class="fc" id="L138">        val content = file.readText()</span>
<span class="pc bpc" id="L139" title="1 of 2 branches missed.">        if (!content.contains(oldStr)) {</span>
<span class="nc" id="L140">            return ToolResult.failure(&quot;Text to replace not found: $oldStr&quot;)</span>
        }
        
<span class="fc" id="L143">        val newContent = content.replace(oldStr, newStr)</span>
<span class="fc" id="L144">        file.writeText(newContent)</span>
        
<span class="fc" id="L146">        return ToolResult.success(</span>
<span class="fc" id="L147">            output = &quot;Text replaced successfully in $path&quot;,</span>
<span class="fc" id="L148">            metadata = mapOf(&quot;replacements&quot; to &quot;1&quot;)</span>
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L153">        val errors = checkRequiredParameters(parameters, listOf(&quot;path&quot;, &quot;old_str&quot;, &quot;new_str&quot;))</span>
<span class="fc bfc" id="L154" title="All 2 branches covered.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L158">        return ToolMetadata(</span>
<span class="fc" id="L159">            name = toolName,</span>
<span class="fc" id="L160">            description = &quot;Replace text in a file&quot;,</span>
<span class="fc" id="L161">            parameters = listOf(</span>
<span class="fc" id="L162">                ToolParameter(&quot;path&quot;, &quot;string&quot;, &quot;File path&quot;, required = true),</span>
<span class="fc" id="L163">                ToolParameter(&quot;old_str&quot;, &quot;string&quot;, &quot;Text to replace&quot;, required = true),</span>
<span class="fc" id="L164">                ToolParameter(&quot;new_str&quot;, &quot;string&quot;, &quot;Replacement text&quot;, required = true)</span>
            ),
<span class="fc" id="L166">            category = &quot;file&quot;</span>
        )
    }
}

/**
 * Handler for remove-files tool
 */
<span class="fc" id="L174">class RemoveFilesHandler : BaseToolHandler(&quot;remove-files&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="nc" id="L177">        val filePaths = parameters[&quot;file_paths&quot;]!!</span>
<span class="nc" id="L178">        val paths = filePaths.split(&quot;,&quot;).map { it.trim() }</span>
<span class="nc" id="L179">        val removedFiles = mutableListOf&lt;String&gt;()</span>
        
<span class="nc" id="L181">        paths.forEach { path -&gt;</span>
<span class="nc" id="L182">            val file = File(workingDirectory, path)</span>
<span class="nc bnc" id="L183" title="All 4 branches missed.">            if (file.exists() &amp;&amp; file.delete()) {</span>
<span class="nc" id="L184">                removedFiles.add(path)</span>
            }
<span class="nc" id="L186">        }</span>
        
<span class="nc" id="L188">        return ToolResult.success(</span>
<span class="nc" id="L189">            output = &quot;Removed ${removedFiles.size} files: ${removedFiles.joinToString(&quot;, &quot;)}&quot;,</span>
<span class="nc" id="L190">            metadata = mapOf(&quot;removed_count&quot; to removedFiles.size.toString())</span>
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="nc" id="L195">        val errors = checkRequiredParameters(parameters, listOf(&quot;file_paths&quot;))</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L200">        return ToolMetadata(</span>
<span class="fc" id="L201">            name = toolName,</span>
<span class="fc" id="L202">            description = &quot;Remove files from filesystem&quot;,</span>
<span class="fc" id="L203">            parameters = listOf(</span>
<span class="fc" id="L204">                ToolParameter(&quot;file_paths&quot;, &quot;array&quot;, &quot;Array of file paths to remove&quot;, required = true)</span>
            ),
<span class="fc" id="L206">            category = &quot;file&quot;</span>
        )
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>