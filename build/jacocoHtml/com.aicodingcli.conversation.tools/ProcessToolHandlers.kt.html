<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ProcessToolHandlers.kt</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ai-coding-cli</a> &gt; <a href="index.source.html" class="el_package">com.aicodingcli.conversation.tools</a> &gt; <span class="el_source">ProcessToolHandlers.kt</span></div><h1>ProcessToolHandlers.kt</h1><pre class="source lang-java linenums">package com.aicodingcli.conversation.tools

import com.aicodingcli.conversation.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * Handler for launch-process tool
 */
<span class="fc" id="L12">class LaunchProcessHandler : BaseToolHandler(&quot;launch-process&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="fc" id="L15">        val command = parameters[&quot;command&quot;]!!</span>
<span class="pc bpc" id="L16" title="1 of 2 branches missed.">        val wait = parameters[&quot;wait&quot;]?.toBoolean() ?: false</span>
<span class="pc bpc" id="L17" title="1 of 2 branches missed.">        val maxWaitSeconds = parameters[&quot;max_wait_seconds&quot;]?.toLong() ?: 30L</span>
        
<span class="fc" id="L19">        return withContext(Dispatchers.IO) {</span>
<span class="fc" id="L20">            try {</span>
<span class="fc" id="L21">                val processBuilder = ProcessBuilder()</span>
<span class="fc" id="L22">                    .command(command.split(&quot; &quot;))</span>
<span class="fc" id="L23">                    .directory(File(workingDirectory))</span>
<span class="fc" id="L24">                    .redirectErrorStream(true)</span>
                
<span class="fc" id="L26">                val process = processBuilder.start()</span>
                
<span class="pc bpc" id="L28" title="1 of 2 branches missed.">                if (wait) {</span>
<span class="fc" id="L29">                    val finished = process.waitFor(maxWaitSeconds, TimeUnit.SECONDS)</span>
<span class="pc bpc" id="L30" title="1 of 2 branches missed.">                    val output = process.inputStream.bufferedReader().readText()</span>
<span class="pc bpc" id="L31" title="1 of 2 branches missed.">                    val exitCode = if (finished) process.exitValue() else -1</span>
                    
<span class="pc bpc" id="L33" title="1 of 2 branches missed.">                    if (!finished) {</span>
<span class="nc" id="L34">                        process.destroyForcibly()</span>
<span class="nc" id="L35">                        return@withContext ToolResult.failure(</span>
<span class="nc" id="L36">                            error = &quot;Process timed out after $maxWaitSeconds seconds&quot;,</span>
<span class="nc" id="L37">                            output = output</span>
                        )
                    }
                    
<span class="pc bpc" id="L41" title="1 of 2 branches missed.">                    if (exitCode == 0) {</span>
<span class="fc" id="L42">                        ToolResult.success(</span>
<span class="fc" id="L43">                            output = output,</span>
<span class="fc" id="L44">                            metadata = mapOf(</span>
<span class="fc" id="L45">                                &quot;exit_code&quot; to exitCode.toString(),</span>
<span class="fc" id="L46">                                &quot;command&quot; to command,</span>
<span class="fc" id="L47">                                &quot;duration&quot; to &quot;${maxWaitSeconds}s&quot;</span>
                            )
                        )
                    } else {
<span class="nc" id="L51">                        ToolResult.failure(</span>
<span class="nc" id="L52">                            error = &quot;Process exited with code $exitCode&quot;,</span>
<span class="nc" id="L53">                            output = output</span>
                        )
                    }
                } else {
                    // Non-blocking execution
<span class="pc" id="L58">                    ToolResult.success(</span>
<span class="nc" id="L59">                        output = &quot;Process launched successfully: $command&quot;,</span>
<span class="nc" id="L60">                        metadata = mapOf(</span>
<span class="nc" id="L61">                            &quot;command&quot; to command,</span>
<span class="nc" id="L62">                            &quot;pid&quot; to process.pid().toString(),</span>
<span class="nc" id="L63">                            &quot;blocking&quot; to &quot;false&quot;</span>
                        )
                    )
                }
<span class="nc" id="L67">            } catch (e: Exception) {</span>
<span class="pc" id="L68">                ToolResult.failure(&quot;Failed to launch process: ${e.message}&quot;)</span>
<span class="fc" id="L69">            }</span>
        }
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L74">        val errors = checkRequiredParameters(parameters, listOf(&quot;command&quot;)).toMutableList()</span>

<span class="fc" id="L76">        val wait = parameters[&quot;wait&quot;]</span>
<span class="pc bpc" id="L77" title="1 of 4 branches missed.">        if (wait != null &amp;&amp; wait !in listOf(&quot;true&quot;, &quot;false&quot;)) {</span>
<span class="fc" id="L78">            errors.add(&quot;wait parameter must be 'true' or 'false'&quot;)</span>
        }

<span class="fc" id="L81">        val maxWaitSeconds = parameters[&quot;max_wait_seconds&quot;]</span>
<span class="fc bfc" id="L82" title="All 2 branches covered.">        if (maxWaitSeconds != null) {</span>
<span class="fc" id="L83">            try {</span>
<span class="fc" id="L84">                val seconds = maxWaitSeconds.toLong()</span>
<span class="pc bpc" id="L85" title="1 of 2 branches missed.">                if (seconds &lt;= 0) {</span>
<span class="nc" id="L86">                    errors.add(&quot;max_wait_seconds must be positive&quot;)</span>
                }
<span class="nc" id="L88">            } catch (e: NumberFormatException) {</span>
<span class="nc" id="L89">                errors.add(&quot;max_wait_seconds must be a valid number&quot;)</span>
            }
        }

<span class="fc bfc" id="L93" title="All 2 branches covered.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L97">        return ToolMetadata(</span>
<span class="fc" id="L98">            name = toolName,</span>
<span class="fc" id="L99">            description = &quot;Launch a process with a shell command&quot;,</span>
<span class="fc" id="L100">            parameters = listOf(</span>
<span class="fc" id="L101">                ToolParameter(&quot;command&quot;, &quot;string&quot;, &quot;The shell command to execute&quot;, required = true),</span>
<span class="fc" id="L102">                ToolParameter(&quot;wait&quot;, &quot;boolean&quot;, &quot;Whether to wait for the command to complete&quot;, required = false),</span>
<span class="fc" id="L103">                ToolParameter(&quot;max_wait_seconds&quot;, &quot;number&quot;, &quot;Number of seconds to wait for the command to complete&quot;, required = false)</span>
            ),
<span class="fc" id="L105">            category = &quot;process&quot;</span>
        )
    }
}

/**
 * Handler for read-terminal tool
 */
<span class="fc" id="L113">class ReadTerminalHandler : BaseToolHandler(&quot;read-terminal&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
        // Mock implementation - in real scenario this would read from actual terminal
<span class="nc" id="L117">        return ToolResult.success(</span>
<span class="nc" id="L118">            output = &quot;Mock terminal output - this would read from the active terminal&quot;,</span>
<span class="nc" id="L119">            metadata = mapOf(&quot;mock&quot; to &quot;true&quot;)</span>
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
        // read-terminal has optional parameters
<span class="nc" id="L125">        return ValidationResult.valid()</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L129">        return ToolMetadata(</span>
<span class="fc" id="L130">            name = toolName,</span>
<span class="fc" id="L131">            description = &quot;Read output from the active or most-recently used terminal&quot;,</span>
<span class="fc" id="L132">            parameters = listOf(</span>
<span class="fc" id="L133">                ToolParameter(&quot;only_selected&quot;, &quot;boolean&quot;, &quot;Whether to read only the selected text in the terminal&quot;, required = false)</span>
            ),
<span class="fc" id="L135">            category = &quot;process&quot;</span>
        )
    }
}

/**
 * Handler for diagnostics tool
 */
<span class="fc" id="L143">class DiagnosticsHandler : BaseToolHandler(&quot;diagnostics&quot;) {</span>
    
    override suspend fun executeInternal(parameters: Map&lt;String, String&gt;, workingDirectory: String): ToolResult {
<span class="pc bpc" id="L146" title="2 of 4 branches missed.">        val paths = parameters[&quot;paths&quot;]?.split(&quot;,&quot;)?.map { it.trim() } ?: emptyList()</span>
        
        // Mock implementation - in real scenario this would get actual IDE diagnostics
<span class="fc" id="L149">        val diagnostics = paths.map { path -&gt;</span>
<span class="fc" id="L150">            &quot;File: $path\n&quot; +</span>
            &quot;  - Warning: Unused import at line 5\n&quot; +
<span class="fc" id="L152">            &quot;  - Error: Unresolved reference at line 12\n&quot;</span>
<span class="fc" id="L153">        }.joinToString(&quot;\n&quot;)</span>
        
<span class="fc" id="L155">        return ToolResult.success(</span>
<span class="pc bpc" id="L156" title="2 of 4 branches missed.">            output = if (diagnostics.isNotEmpty()) diagnostics else &quot;No diagnostics found&quot;,</span>
<span class="fc" id="L157">            metadata = mapOf(</span>
<span class="fc" id="L158">                &quot;paths_count&quot; to paths.size.toString(),</span>
<span class="fc" id="L159">                &quot;mock&quot; to &quot;true&quot;</span>
            )
        )
    }
    
    override fun validateParameters(parameters: Map&lt;String, String&gt;): ValidationResult {
<span class="fc" id="L165">        val errors = checkRequiredParameters(parameters, listOf(&quot;paths&quot;))</span>
<span class="pc bpc" id="L166" title="1 of 2 branches missed.">        return if (errors.isEmpty()) ValidationResult.valid() else ValidationResult.invalid(errors)</span>
    }
    
    override fun getMetadata(): ToolMetadata {
<span class="fc" id="L170">        return ToolMetadata(</span>
<span class="fc" id="L171">            name = toolName,</span>
<span class="fc" id="L172">            description = &quot;Get issues (errors, warnings, etc.) from the IDE&quot;,</span>
<span class="fc" id="L173">            parameters = listOf(</span>
<span class="fc" id="L174">                ToolParameter(&quot;paths&quot;, &quot;array&quot;, &quot;Required list of file paths to get issues for from the IDE&quot;, required = true)</span>
            ),
<span class="fc" id="L176">            category = &quot;analysis&quot;</span>
        )
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.13.202504020838</span></div></body></html>