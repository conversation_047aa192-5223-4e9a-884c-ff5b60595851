<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.AiCodingCliTest" tests="26" skipped="0" failures="0" errors="0" timestamp="2025-06-18T04:43:00.494Z" hostname="zxnapdeMacBook-Pro.local" time="1.546">
  <properties/>
  <testcase name="should handle unknown history subcommand()" classname="com.aicodingcli.AiCodingCliTest" time="0.107"/>
  <testcase name="should show error for unknown command()" classname="com.aicodingcli.AiCodingCliTest" time="0.003"/>
  <testcase name="should handle history list with limit parameter()" classname="com.aicodingcli.AiCodingCliTest" time="0.014"/>
  <testcase name="should handle config provider with no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.003"/>
  <testcase name="should print help when no arguments provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.002"/>
  <testcase name="should handle history stats command()" classname="com.aicodingcli.AiCodingCliTest" time="0.006"/>
  <testcase name="should handle config get with no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle history show with non-existent ID()" classname="com.aicodingcli.AiCodingCliTest" time="0.002"/>
  <testcase name="should handle stream parameter correctly()" classname="com.aicodingcli.AiCodingCliTest" time="1.112"/>
  <testcase name="should handle config list command()" classname="com.aicodingcli.AiCodingCliTest" time="0.003"/>
  <testcase name="should print help when --help argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.002"/>
  <testcase name="should show history help when history command has no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle history list command()" classname="com.aicodingcli.AiCodingCliTest" time="0.002"/>
  <testcase name="should handle continue parameter correctly()" classname="com.aicodingcli.AiCodingCliTest" time="0.002"/>
  <testcase name="should handle history show with missing ID()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle history delete with non-existent ID()" classname="com.aicodingcli.AiCodingCliTest" time="0.002"/>
  <testcase name="should handle history search with missing query()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle invalid config subcommand()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle config set with insufficient arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <testcase name="should handle history delete with missing ID()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle history search with query()" classname="com.aicodingcli.AiCodingCliTest" time="0.002"/>
  <testcase name="should handle model parameter correctly()" classname="com.aicodingcli.AiCodingCliTest" time="0.02"/>
  <testcase name="should handle new parameter correctly()" classname="com.aicodingcli.AiCodingCliTest" time="0.245"/>
  <testcase name="should show config help when config command has no arguments()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <testcase name="should handle continue parameter without ID()" classname="com.aicodingcli.AiCodingCliTest" time="0.002"/>
  <testcase name="should print version when --version argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
