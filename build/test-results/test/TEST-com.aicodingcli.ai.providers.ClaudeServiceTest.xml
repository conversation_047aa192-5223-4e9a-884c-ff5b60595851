<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.providers.ClaudeServiceTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-18T04:43:02.575Z" hostname="zxnapdeMacBook-Pro.local" time="0.149">
  <properties/>
  <testcase name="should use custom base URL if provided()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.121"/>
  <testcase name="should test connection successfully()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.004"/>
  <testcase name="should fail connection test on error()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.005"/>
  <testcase name="should handle streaming chat request()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.002"/>
  <testcase name="should validate request before sending()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.001"/>
  <testcase name="should make successful chat request()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.006"/>
  <testcase name="should handle API error response()" classname="com.aicodingcli.ai.providers.ClaudeServiceTest" time="0.006"/>
  <system-out><![CDATA[12:43:02.576 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#3
12:43:02.680 [Test worker @kotlinx.coroutines.test runner#74] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #3#4
12:43:02.685 [Test worker @kotlinx.coroutines.test runner#74] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "msg_test",
    "type": "message",
    "role": "assistant",
    "content": [
        {
            "type": "text",
            "text": "Hi"
        }
    ],
    "model": "claude-3-sonnet-20240229",
    "stop_reason": "end_turn",
    "stop_sequence": null,
    "usage": {
        "input_tokens": 1,
        "output_tokens": 1
    }
}, headers={}) on AiHttpClient(#3).post(https://custom.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Hello"}],"max_tokens":1000,"temperature":0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
12:43:02.699 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#5
12:43:02.701 [Test worker @kotlinx.coroutines.test runner#80] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #5#6
12:43:02.702 [Test worker @kotlinx.coroutines.test runner#80] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "msg_test",
    "type": "message",
    "role": "assistant",
    "content": [
        {
            "type": "text",
            "text": "Connection test successful"
        }
    ],
    "model": "claude-3-sonnet-20240229",
    "stop_reason": "end_turn",
    "stop_sequence": null,
    "usage": {
        "input_tokens": 5,
        "output_tokens": 5
    }
}, headers={Content-Type=application/json}) on AiHttpClient(#5).post(https://api.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Test connection"}],"max_tokens":10}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
12:43:02.703 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#7
12:43:02.705 [Test worker @kotlinx.coroutines.test runner#84] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #7#8
12:43:02.708 [Test worker @kotlinx.coroutines.test runner#84] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: Unauthorized on AiHttpClient(#7).post(https://api.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Test connection"}],"max_tokens":10}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
12:43:02.709 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#9
12:43:02.711 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#10
12:43:02.713 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#11
12:43:02.715 [Test worker @kotlinx.coroutines.test runner#92] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #11#12
12:43:02.715 [Test worker @kotlinx.coroutines.test runner#92] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "msg_01XFDUDYJgAACzvnptvVoYEL",
    "type": "message",
    "role": "assistant",
    "content": [
        {
            "type": "text",
            "text": "Hello! How can I help you today?"
        }
    ],
    "model": "claude-3-sonnet-20240229",
    "stop_reason": "end_turn",
    "stop_sequence": null,
    "usage": {
        "input_tokens": 10,
        "output_tokens": 15
    }
}, headers={Content-Type=application/json}) on AiHttpClient(#11).post(https://api.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Hello, Claude!"}],"max_tokens":1000,"temperature":0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
12:43:02.719 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#13
12:43:02.721 [Test worker @kotlinx.coroutines.test runner#98] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #13#14
12:43:02.722 [Test worker @kotlinx.coroutines.test runner#98] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: Unauthorized on AiHttpClient(#13).post(https://api.anthropic.com/v1/messages, {"model":"claude-3-sonnet-20240229","messages":[{"role":"user","content":"Hello"}],"max_tokens":1000,"temperature":0.7}, {x-api-key=test-api-key, Content-Type=application/json, anthropic-version=2023-06-01}, continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
