<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.AnalyzeCommandTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-06-18T04:43:02.042Z" hostname="zxnapdeMacBook-Pro.local" time="0.032">
  <properties/>
  <testcase name="should handle analyze issues command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.014"/>
  <testcase name="should handle json format output()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.004"/>
  <testcase name="should handle analyze file command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.003"/>
  <testcase name="should handle analyze help command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.001"/>
  <testcase name="should handle analyze project command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.005"/>
  <testcase name="should handle analyze metrics command()" classname="com.aicodingcli.AnalyzeCommandTest" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
