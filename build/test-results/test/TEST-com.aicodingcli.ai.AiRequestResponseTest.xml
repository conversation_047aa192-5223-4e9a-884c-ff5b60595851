<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.AiRequestResponseTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-06-18T04:43:02.074Z" hostname="zxnapdeMacBook-Pro.local" time="0.495">
  <properties/>
  <testcase name="should create AI response with content()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.0"/>
  <testcase name="should create AI request with required fields()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.001"/>
  <testcase name="should handle streaming response()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.493"/>
  <testcase name="should handle conversation with multiple messages()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.0"/>
  <testcase name="should validate message content is not empty()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.001"/>
  <system-out><![CDATA[12:43:02.330 [Test worker @kotlinx.coroutines.test runner#56] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiService name=#1
12:43:02.564 [Test worker @kotlinx.coroutines.test runner#56] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for Flow name=child of #1#2
12:43:02.567 [Test worker @kotlinx.coroutines.test runner#56] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering kotlinx.coroutines.flow.FlowKt__BuildersKt$flowOf$$inlined$unsafeFlow$1@2be9add0 on AiService(#1).streamChat(AiRequest(messages=[AiMessage(role=USER, content=Hello)], model=gpt-3.5-turbo, temperature=0.7, maxTokens=1000, stream=false), continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
