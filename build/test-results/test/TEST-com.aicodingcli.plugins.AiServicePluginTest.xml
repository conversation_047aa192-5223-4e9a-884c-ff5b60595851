<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.plugins.AiServicePluginTest" tests="12" skipped="0" failures="0" errors="0" timestamp="2025-06-18T04:43:03.767Z" hostname="zxnapdeMacBook-Pro.local" time="0.011">
  <properties/>
  <testcase name="should initialize and shutdown correctly()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.004"/>
  <testcase name="should provide default configuration()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.001"/>
  <testcase name="should provide supported models()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.0"/>
  <testcase name="should fail validation for missing API key()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.001"/>
  <testcase name="should support correct AI provider()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.0"/>
  <testcase name="should create AI service plugin with correct metadata()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.0"/>
  <testcase name="should handle AI service operations()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.001"/>
  <testcase name="should validate configuration successfully()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.001"/>
  <testcase name="should create AI service()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.0"/>
  <testcase name="should validate plugin successfully()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.001"/>
  <testcase name="should register with AI service plugin registry()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.001"/>
  <testcase name="should fail validation for wrong provider()" classname="com.aicodingcli.plugins.AiServicePluginTest" time="0.0"/>
  <system-out><![CDATA[[2025-06-18 12:43:03] [INFO] [Plugin:test-ai-service-plugin] AI service plugin Test AI Service Plugin initialized for provider OPENAI
[2025-06-18 12:43:03] [INFO] [Plugin:test-ai-service-plugin] AI service plugin Test AI Service Plugin shut down
[2025-06-18 12:43:03] [INFO] [Plugin:test-ai-service-plugin] AI service plugin Test AI Service Plugin initialized for provider OPENAI
[2025-06-18 12:43:03] [INFO] [Plugin:test-ai-service-plugin] AI service plugin Test AI Service Plugin shut down
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
