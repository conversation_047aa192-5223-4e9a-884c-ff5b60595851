<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.conversation.RequirementParserTest" tests="17" skipped="0" failures="5" errors="0" timestamp="2025-06-18T04:43:02.997Z" hostname="zxnapdeMacBook-Pro.local" time="0.021">
  <properties/>
  <testcase name="should extract file operations from requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.005">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;REFACTOR_CODE&gt; but was: &lt;CREATE_SERVICE&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;REFACTOR_CODE&gt; but was: &lt;CREATE_SERVICE&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract file operations from requirement$1.invokeSuspend(RequirementParserTest.kt:232)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract file operations from requirement$1.invoke(RequirementParserTest.kt)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract file operations from requirement$1.invoke(RequirementParserTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.RequirementParserTest.should extract file operations from requirement(RequirementParserTest.kt:223)
	at java.base@17.0.14/java.lang.reflect.Method.invoke(Method.java:569)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
</failure>
  </testcase>
  <testcase name="should extract intent from simple requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;CREATE_CLASS&gt; but was: &lt;CREATE_SERVICE&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;CREATE_CLASS&gt; but was: &lt;CREATE_SERVICE&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract intent from simple requirement$1.invokeSuspend(RequirementParserTest.kt:91)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract intent from simple requirement$1.invoke(RequirementParserTest.kt)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract intent from simple requirement$1.invoke(RequirementParserTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.RequirementParserTest.should extract intent from simple requirement(RequirementParserTest.kt:83)
	at java.base@17.0.14/java.lang.reflect.Method.invoke(Method.java:569)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
</failure>
  </testcase>
  <testcase name="should parse simple class creation requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.002">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;CREATE_CLASS&gt; but was: &lt;CREATE_CONFIG&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;CREATE_CLASS&gt; but was: &lt;CREATE_CONFIG&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at app//com.aicodingcli.conversation.RequirementParserTest$should parse simple class creation requirement$1.invokeSuspend(RequirementParserTest.kt:27)
	at app//com.aicodingcli.conversation.RequirementParserTest$should parse simple class creation requirement$1.invoke(RequirementParserTest.kt)
	at app//com.aicodingcli.conversation.RequirementParserTest$should parse simple class creation requirement$1.invoke(RequirementParserTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.RequirementParserTest.should parse simple class creation requirement(RequirementParserTest.kt:18)
	at java.base@17.0.14/java.lang.reflect.Method.invoke(Method.java:569)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
</failure>
  </testcase>
  <testcase name="should parse requirement with validation constraints()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001"/>
  <testcase name="should parse configuration requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001"/>
  <testcase name="should handle vague requirement gracefully()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.0"/>
  <testcase name="should parse requirement with performance requirements()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.002"/>
  <testcase name="should parse test creation requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001"/>
  <testcase name="should parse requirement with specific output format()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001"/>
  <testcase name="should handle empty requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.0"/>
  <testcase name="should handle complex requirement with multiple entities()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.0"/>
  <testcase name="should extract parameters for class creation()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001"/>
  <testcase name="should extract intent from API requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001"/>
  <testcase name="should parse requirement with specific technology stack()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001"/>
  <testcase name="should extract parameters for API creation()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;order&gt; but was: &lt;Order&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;order&gt; but was: &lt;Order&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract parameters for API creation$1.invokeSuspend(RequirementParserTest.kt:147)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract parameters for API creation$1.invoke(RequirementParserTest.kt)
	at app//com.aicodingcli.conversation.RequirementParserTest$should extract parameters for API creation$1.invoke(RequirementParserTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.RequirementParserTest.should extract parameters for API creation(RequirementParserTest.kt:138)
	at java.base@17.0.14/java.lang.reflect.Method.invoke(Method.java:569)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
</failure>
  </testcase>
  <testcase name="should parse REST API requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.001">
    <failure message="org.opentest4j.AssertionFailedError: expected: &lt;user&gt; but was: &lt;User&gt;" type="org.opentest4j.AssertionFailedError">org.opentest4j.AssertionFailedError: expected: &lt;user&gt; but was: &lt;User&gt;
	at app//org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at app//org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at app//org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at app//org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at app//org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at app//com.aicodingcli.conversation.RequirementParserTest$should parse REST API requirement$1.invokeSuspend(RequirementParserTest.kt:46)
	at app//com.aicodingcli.conversation.RequirementParserTest$should parse REST API requirement$1.invoke(RequirementParserTest.kt)
	at app//com.aicodingcli.conversation.RequirementParserTest$should parse REST API requirement$1.invoke(RequirementParserTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.aicodingcli.conversation.RequirementParserTest.should parse REST API requirement(RequirementParserTest.kt:36)
	at java.base@17.0.14/java.lang.reflect.Method.invoke(Method.java:569)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.base@17.0.14/java.util.ArrayList.forEach(ArrayList.java:1511)
</failure>
  </testcase>
  <testcase name="should extract intent from refactor requirement()" classname="com.aicodingcli.conversation.RequirementParserTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
