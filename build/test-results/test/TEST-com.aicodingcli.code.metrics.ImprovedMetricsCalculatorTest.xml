<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-18T04:43:02.807Z" hostname="zxnapdeMacBook-Pro.local" time="0.003">
  <properties/>
  <testcase name="should detect actual duplicate lines accurately()" classname="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" time="0.0"/>
  <testcase name="should handle edge cases gracefully()" classname="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" time="0.0"/>
  <testcase name="should calculate accurate cyclomatic complexity for simple method()" classname="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" time="0.001"/>
  <testcase name="should calculate accurate cyclomatic complexity for loops()" classname="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" time="0.0"/>
  <testcase name="should calculate complexity for different programming languages()" classname="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" time="0.0"/>
  <testcase name="should count lines of code excluding comments and empty lines()" classname="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" time="0.001"/>
  <testcase name="should calculate accurate cyclomatic complexity for method with conditions()" classname="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" time="0.0"/>
  <testcase name="should calculate maintainability index based on complexity and size()" classname="com.aicodingcli.code.metrics.ImprovedMetricsCalculatorTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
