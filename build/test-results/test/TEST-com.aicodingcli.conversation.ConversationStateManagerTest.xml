<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.conversation.ConversationStateManagerTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-06-18T04:43:02.984Z" hostname="zxnapdeMacBook-Pro.local" time="0.013">
  <properties/>
  <testcase name="should retrieve existing conversation session()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.001"/>
  <testcase name="should end conversation session and mark as completed()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.001"/>
  <testcase name="should throw exception when ending non-existent session()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.001"/>
  <testcase name="should update conversation state and persist changes()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.002"/>
  <testcase name="should persist conversation sessions to file()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.001"/>
  <testcase name="should return null for non-existent conversation session()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.001"/>
  <testcase name="should handle concurrent session updates safely()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.002"/>
  <testcase name="should throw exception when updating non-existent session()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.001"/>
  <testcase name="should create new conversation session with initial state()" classname="com.aicodingcli.conversation.ConversationStateManagerTest" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
