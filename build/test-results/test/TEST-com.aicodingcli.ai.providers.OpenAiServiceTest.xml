<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.providers.OpenAiServiceTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-18T04:43:02.752Z" hostname="zxnapdeMacBook-Pro.local" time="0.032">
  <properties/>
  <testcase name="should use custom base URL if provided()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.011"/>
  <testcase name="should test connection successfully()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.006"/>
  <testcase name="should fail connection test on error()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.002"/>
  <testcase name="should handle streaming chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.002"/>
  <testcase name="should validate request before sending()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.001"/>
  <testcase name="should make successful chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.004"/>
  <testcase name="should handle API error response()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.006"/>
  <system-out><![CDATA[12:43:02.753 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#29
12:43:02.755 [Test worker @kotlinx.coroutines.test runner#136] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #29#30
12:43:02.758 [Test worker @kotlinx.coroutines.test runner#136] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-test",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hi"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 1,
        "completion_tokens": 1,
        "total_tokens": 2
    }
}, headers={}) on AiHttpClient(#29).post(https://custom.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
12:43:02.764 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#31
12:43:02.766 [Test worker @kotlinx.coroutines.test runner#142] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #31#32
12:43:02.767 [Test worker @kotlinx.coroutines.test runner#142] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={"object": "list", "data": [{"id": "gpt-3.5-turbo", "object": "model", "created": 1677610602, "owned_by": "openai"}]}, headers={Content-Type=application/json}) on AiHttpClient(#31).get(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
12:43:02.770 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#33
12:43:02.771 [Test worker @kotlinx.coroutines.test runner#146] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #33#34
12:43:02.771 [Test worker @kotlinx.coroutines.test runner#146] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: Unauthorized on AiHttpClient(#33).get(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
12:43:02.772 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#35
12:43:02.774 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#36
12:43:02.775 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#37
12:43:02.777 [Test worker @kotlinx.coroutines.test runner#154] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #37#38
12:43:02.777 [Test worker @kotlinx.coroutines.test runner#154] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hello! How can I help you today?"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 9,
        "completion_tokens": 12,
        "total_tokens": 21
    }
}, headers={Content-Type=application/json}) on AiHttpClient(#37).post(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello, AI!"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
12:43:02.779 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#39
12:43:02.781 [Test worker @kotlinx.coroutines.test runner#160] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #39#40
12:43:02.781 [Test worker @kotlinx.coroutines.test runner#160] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: Unauthorized on AiHttpClient(#39).post(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
